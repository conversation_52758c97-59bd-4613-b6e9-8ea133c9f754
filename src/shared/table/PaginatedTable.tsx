import * as Styled from './style'
import * as SharedStyled from '../../styles/styled'
import { usePagination, useTable, useAsyncDebounce, useGlobalFilter, useFilters } from 'react-table'
import { forwardRef, Fragment, useEffect, useState } from 'react'
import { colors } from '../../styles/theme'
import useDebounce from '../hooks/useDebounce'

interface I_Table {
  columns: any
  data: any
  totalItems?: number
  pageCount?: number
  setPageIndex?: React.Dispatch<React.SetStateAction<number>>
  setpageSize?: React.Dispatch<React.SetStateAction<number>>
  fetchData: ({ pageSize, pageIndex }: any) => void
  loading?: boolean
  onRowClick?: (val: any) => void
  isLoadMoreLoading?: boolean
  noBorder?: boolean
  padding?: string
  minWidth?: string
  noLink?: boolean
  hideHeader?: boolean
}

export const PaginatedTable = forwardRef((props: I_Table, ref: any) => {
  const {
    columns,
    data,
    loading,
    fetchData,
    onRowClick,
    totalItems = 0,
    pageCount = 0,
    setPageIndex,
    setpageSize,
    isLoadMoreLoading,
    noBorder,
    padding,
    minWidth,
    noLink,
    hideHeader,
  } = props

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow,
    canPreviousPage,
    canNextPage,
    nextPage,
    previousPage,
    gotoPage,
    setPageSize,
    setGlobalFilter,
    state: { pageIndex, pageSize, globalFilter },
  }: any = useTable(
    {
      columns,
      data,
      initialState: { pageIndex: 0, pageSize: 10 },
      manualPagination: true,
      pageCount,
      autoResetPage: false,
    },
    useFilters,
    useGlobalFilter,
    usePagination
  )

  const debouncedPageIndex = useDebounce(pageIndex, 500) // 500ms delay
  const debouncedPageSize = useDebounce(pageSize, 500)
  useEffect(() => {
    setPageIndex?.(debouncedPageIndex)
    setpageSize?.(debouncedPageSize)
    fetchData({ pageIndex: debouncedPageIndex, pageSize: debouncedPageSize })
  }, [debouncedPageIndex, debouncedPageSize])

  const start = Math.min(pageIndex * pageSize + 1, totalItems)
  const end = Math.min((pageIndex + 1) * pageSize, totalItems)

  const [inputPage, setInputPage] = useState(String(pageIndex + 1))

  useEffect(() => {
    setInputPage(String(pageIndex + 1))
  }, [pageIndex])

  return (
    <>
      <Styled.Pagination>
        <Styled.ShowEntriesDiv>
          <Styled.PageSpan>
            {start}-{end} of {totalItems}
          </Styled.PageSpan>

          <Styled.ShowEntriesDiv>
            <Styled.IconContainer onClick={() => gotoPage(0)} disabled={!canPreviousPage}>
              ⏮
            </Styled.IconContainer>

            <Styled.IconContainer onClick={previousPage} disabled={!canPreviousPage}>
              ◀
            </Styled.IconContainer>
          </Styled.ShowEntriesDiv>

          <Styled.PageInput
            type="number"
            min={1}
            max={pageCount}
            value={inputPage}
            onChange={(e) => {
              const rawValue = e.target.value
              setInputPage(rawValue) // Allow typing

              // Validate and debounce
              const page = rawValue ? Math.min(Math.max(1, Number(rawValue)), pageCount) - 1 : 0
              gotoPage(page)
            }}
          />

          <Styled.PageSpan>of {pageCount}</Styled.PageSpan>

          <Styled.ShowEntriesDiv>
            <Styled.IconContainer onClick={nextPage} disabled={!canNextPage}>
              ▶
            </Styled.IconContainer>

            <Styled.IconContainer onClick={() => gotoPage(pageCount - 1)} disabled={!canNextPage}>
              ⏭
            </Styled.IconContainer>
          </Styled.ShowEntriesDiv>

          <Styled.ShowEntriesDiv>
            <Styled.ShowText>Per page:</Styled.ShowText>
            <Styled.SelectDiv
              value={pageSize}
              onChange={(e) => {
                const newSize = Number(e.target.value)
                setPageSize(newSize)
                gotoPage(0) // <-- Reset to first page to prevent inconsistency
              }}
            >
              {[10, 20, 30, 40, 50].map((size) => (
                <Styled.SelectOption key={size} value={size}>
                  {size}
                </Styled.SelectOption>
              ))}
            </Styled.SelectDiv>
          </Styled.ShowEntriesDiv>
        </Styled.ShowEntriesDiv>
      </Styled.Pagination>

      <Styled.TableOuterContainer>
        <Styled.TableContainer {...getTableProps()} noLink={noLink} minWidth={minWidth}>
          {!hideHeader && (
            <Styled.TableHeader>
              {headerGroups.map((headerGroup: any, idx: number) => (
                <Styled.TableRow {...headerGroup.getHeaderGroupProps()} noLink={true} key={idx}>
                  {headerGroup.headers.map((column: any, index: number) => (
                    <Styled.TableHeading noBorder={noBorder} padding={padding} {...column.getHeaderProps()} key={index}>
                      {column.render('Header')}
                    </Styled.TableHeading>
                  ))}
                </Styled.TableRow>
              ))}
            </Styled.TableHeader>
          )}

          <Styled.TableBody {...getTableBodyProps()}>
            {rows.length > 0 ? (
              rows.map((row: any, index: number) => {
                prepareRow(row)
                return (
                  <Fragment key={index}>
                    <Styled.TableRow
                      noBorder={noBorder}
                      key={index}
                      {...row.getRowProps()}
                      onClick={() => {
                        if (onRowClick) {
                          onRowClick(row.original)
                        }
                      }}
                    >
                      {row.cells.map((cell: any, idx: number) => (
                        <Styled.TableData
                          noBorder={noBorder}
                          padding={row.original.padding && idx === 0 ? row.original.padding : padding}
                          {...cell.getCellProps()}
                          key={idx}
                        >
                          {cell.render('Cell')}
                        </Styled.TableData>
                      ))}
                    </Styled.TableRow>
                  </Fragment>
                )
              })
            ) : globalFilter ? (
              <Styled.LoaderContainer>
                <td>
                  <Styled.LoaderContent>No Results</Styled.LoaderContent>
                </td>
              </Styled.LoaderContainer>
            ) : loading ? (
              [1, 2, 3, 4, 5, 6].map((idx: number) => (
                <Styled.TableRow key={idx} noLink>
                  {headerGroups[0].headers.map((headerGroup: any, key: number) => (
                    <Styled.TableData key={key}>
                      <SharedStyled.SkeletonLoader key={key}>
                        <div className="skeleton"></div>
                      </SharedStyled.SkeletonLoader>
                    </Styled.TableData>
                  ))}
                </Styled.TableRow>
              ))
            ) : (
              <Styled.LoaderContainer>
                <td>
                  <Styled.LoaderContent>No Results</Styled.LoaderContent>
                </td>
              </Styled.LoaderContainer>
            )}
          </Styled.TableBody>
        </Styled.TableContainer>
      </Styled.TableOuterContainer>
    </>
  )
})
