import React from 'react'
import * as SharedStyled from '../../../styles/styled'
import * as Styled from './style'
import { MessageIcon, AddIcon, EmailIcon, TagIcon, DeleteIcon, ExportIcon } from './icons'

interface ActionButtonsProps {
  onAddClick?: () => void
  onMessageClick?: () => void
  onEmailClick?: () => void
  onAddTagsClick?: () => void
  onRemoveTagsClick?: () => void
  onDeleteClick?: () => void
  onExportClick?: () => void
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  onAddClick,
  onMessageClick,
  onEmailClick,
  onAddTagsClick,
  onRemoveTagsClick,
  onDeleteClick,
  onExportClick,
}) => {
  return (
    <SharedStyled.FlexRow justifyContent="flex-start" gap="12px">
      <Styled.ActionButton onClick={onAddClick} title="Add">
        <AddIcon color="#000000" />
      </Styled.ActionButton>

      <Styled.ActionButton onClick={onMessageClick} title="Message">
        <MessageIcon color="#000000" />
      </Styled.ActionButton>

      <Styled.ActionButton onClick={onEmailClick} title="Email">
        <EmailIcon color="#000000" />
      </Styled.ActionButton>

      <Styled.ActionButton onClick={onAddTagsClick} title="Add Tags">
        <TagIcon color="#000000" />
      </Styled.ActionButton>

      <Styled.ActionButton onClick={onRemoveTagsClick} title="Remove Tags">
        <div style={{ position: 'relative' }}>
          <TagIcon color="#000000" />
          <div style={{ position: 'absolute', top: -5, right: -5 }}>
            <DeleteIcon color="#000000" width="12" height="12" />
          </div>
        </div>
      </Styled.ActionButton>

      <Styled.ActionButton onClick={onDeleteClick} title="Delete">
        <DeleteIcon color="#000000" />
      </Styled.ActionButton>

      <Styled.ActionButton onClick={onExportClick} title="Export">
        <ExportIcon color="#000000" />
      </Styled.ActionButton>
    </SharedStyled.FlexRow>
  )
}

export default ActionButtons
