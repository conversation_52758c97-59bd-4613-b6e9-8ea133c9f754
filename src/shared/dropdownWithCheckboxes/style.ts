import { Field } from 'formik'
import styled from 'styled-components'

export const DropdownContainer = styled.div<{ marginTop?: string }>`
  position: relative;
  display: inline-block;
  margin-top: ${(props) => (props.marginTop ? props.marginTop : '8px')};
  .selection {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  img {
    margin-left: 10px;
  }
  .rotate {
    rotate: 180deg;
    transition: all;
  }

  &.client-filter {
    margin-left: auto;
  }
`

export const DropdownButton = styled.button`
  border-radius: 5px;
  background-color: white;
  border: 1px solid #6a747e33;
  padding: 15px;
  cursor: pointer;
  min-width: 200px;
  width: auto;
`

export const DropdownContent = styled.div`
  display: block;
  position: absolute;
  background-color: white;
  min-width: 200px;
  width: auto;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  padding: 12px 16px;
  border: 1px solid #ccc;
`

export const DropdownLabel = styled.label`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  cursor: pointer;
  input {
    cursor: pointer;
  }
`

export const DropdownCheckbox = styled(Field)`
  margin-right: 8px;
`
