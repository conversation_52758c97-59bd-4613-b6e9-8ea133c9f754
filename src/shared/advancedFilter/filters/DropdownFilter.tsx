import React, { useState } from 'react'
import * as SharedStyled from '../../../styles/styled'
import * as Styled from '../style'
import Button from '../../components/button/Button'

interface DropdownOption {
  _id: string
  name: string
}

interface DropdownFilterProps {
  label: string
  fieldKey: string
  options: DropdownOption[] | string[]
  onApply: (query: Record<string, any>) => void
  onReset: () => void
  onTypeChange: () => void
  onRemoveFilter: (fieldKey: string, operator: string, value?: string) => void
  appliedFilters?: any[]
}

const DropdownFilter: React.FC<DropdownFilterProps> = ({
  label,
  fieldKey,
  options,
  onApply,
  onReset,
  onTypeChange,
  onRemoveFilter,
  appliedFilters = [],
}) => {
  const [type, setType] = useState<'is' | 'is_not'>('is')
  const [selectedOption, setSelectedOption] = useState('')

  // Check if options are objects or strings
  const isObjectOptions = options.length > 0 && typeof options[0] !== 'string'

  // Get display name for a value
  const getDisplayName = (value: string) => {
    if (!isObjectOptions) return value

    const option = (options as DropdownOption[]).find((opt) => opt._id === value)
    return option ? option.name : value
  }

  // Clear values when type changes
  const handleTypeChange = (newType: 'is' | 'is_not') => {
    // Only call onTypeChange if the type is actually changing
    if (type !== newType) {
      setType(newType)
      setSelectedOption('')
      onTypeChange() // Call parent's onTypeChange to clear filters
    } else {
      setType(newType)
    }
  }

  const handleApply = () => {
    if (!selectedOption) return

    const query: Record<string, any> = {}
    const filterValue = { field: fieldKey, operator: type, value: selectedOption }
    query[fieldKey] = filterValue
    onApply(query)

    // Reset form after applying
    setSelectedOption('')
  }

  const handleReset = () => {
    setType('is')
    setSelectedOption('')
    onReset()
  }

  return (
    <SharedStyled.FlexCol gap="8px">
      <SharedStyled.Text fontWeight="600">{label}</SharedStyled.Text>

      {/* Display applied filters */}
      {appliedFilters && appliedFilters.length > 0 && (
        <SharedStyled.FlexCol gap="8px" margin="0 0 16px 0">
          <SharedStyled.Text fontWeight="600">Applied Filters:</SharedStyled.Text>
          <Styled.AppliedFiltersContainer>
            {appliedFilters.map((filter, index) => (
              <Styled.AppliedFilterTag key={index}>
                {filter.operator === 'is' && `Is: ${getDisplayName(filter.value)}`}
                {filter.operator === 'is_not' && `Is Not: ${getDisplayName(filter.value)}`}
                <Styled.RemoveFilterButton onClick={() => onRemoveFilter(fieldKey, filter.operator, filter.value)}>
                  ×
                </Styled.RemoveFilterButton>
              </Styled.AppliedFilterTag>
            ))}
          </Styled.AppliedFiltersContainer>
        </SharedStyled.FlexCol>
      )}

      <SharedStyled.FlexCol gap="4px">
        <Styled.RadioWrapper>
          <input
            type="radio"
            name={`${fieldKey}-type`}
            value="is"
            checked={type === 'is'}
            onChange={() => handleTypeChange('is')}
          />
          <SharedStyled.Text>Is</SharedStyled.Text>
        </Styled.RadioWrapper>

        <Styled.RadioWrapper>
          <input
            type="radio"
            name={`${fieldKey}-type`}
            value="is_not"
            checked={type === 'is_not'}
            onChange={() => handleTypeChange('is_not')}
          />
          <SharedStyled.Text>Is Not</SharedStyled.Text>
        </Styled.RadioWrapper>
      </SharedStyled.FlexCol>

      <Styled.Select value={selectedOption} onChange={(e) => setSelectedOption(e.target.value)}>
        <option value="">-- Select Option --</option>
        {isObjectOptions
          ? (options as DropdownOption[]).map((opt) => (
              <option key={opt._id} value={opt._id}>
                {opt.name}
              </option>
            ))
          : (options as string[]).map((opt) => (
              <option key={opt} value={opt}>
                {opt}
              </option>
            ))}
      </Styled.Select>

      <SharedStyled.FlexRow gap="8px">
        <Button onClick={handleApply} padding="6px 12px">
          Apply
        </Button>
        <Button onClick={handleReset} className="gray" padding="6px 12px">
          Reset
        </Button>
      </SharedStyled.FlexRow>
    </SharedStyled.FlexCol>
  )
}

export default DropdownFilter
