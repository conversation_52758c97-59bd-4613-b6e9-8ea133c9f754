import React, { useState } from 'react'
import { Container, Indicator, Switch, Title } from './style'

interface ToggleProps {
  title: string
  customStyles?: any
  isToggled: boolean
  onToggle: any
  className?: string
  width?: string
  disabled?: boolean
}

const Toggle: React.FC<ToggleProps> = ({ title, customStyles, isToggled, onToggle, width, disabled, className }) => {
  const handleToggle = (event: React.MouseEvent) => {
    event.stopPropagation() // Prevents parent event handlers from being triggered
    onToggle() // Call the toggle function
  }

  return (
    <div style={{ width: width ? width : '100%' }}>
      <Container style={customStyles} className={className}>
        {title ? <Title>{title}</Title> : null}
        <Switch
          onClick={(event) => !disabled && handleToggle(event)}
          style={{ cursor: disabled ? 'not-allowed' : 'pointer' }}
          isToggled={isToggled}
        >
          <Indicator isToggled={isToggled} />
        </Switch>
      </Container>
    </div>
  )
}

export default Toggle
