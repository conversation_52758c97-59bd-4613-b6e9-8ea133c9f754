import styled from 'styled-components'
import { colors, screenSizes } from '../../styles/theme'
import { SettingModalHeaderContainer } from '../../modules/units/components/newUnitModal/style'
import { Nue } from '../../shared/helpers/constants'

export const EditLeadSourcePopUpContainer = styled.div`
  background: ${colors.white};
  width: 500px;
  height: 100%;
  border-radius: 10px;
  @media (max-width: 768px) {
    width: 90vw;
  }
`

export const ModalHeaderContainer = styled(SettingModalHeaderContainer)``

export const ModalHeader = styled.p`
  margin: 0;
  line-height: 1.5;
  font-size: 20px;
  font-family: ${Nue.medium};
`

export const CrossContainer = styled.div`
  cursor: pointer;
  svg {
    width: 25px;
    height: 25px;
    svg path {
      stroke: ${colors.darkGrey};
    }
    :hover {
      svg path {
        stroke: ${colors.grey};
      }
      transform: scale(1.03);
    }
    transition: all 0.01s linear;
  }
`

export const ModalBodyContainer = styled.div`
  padding: 20px;
`

export const ModalDescription = styled.p`
  margin: 0;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  @media (min-width: ${screenSizes.M}px) {
    font-size: 18px;
  }
`
