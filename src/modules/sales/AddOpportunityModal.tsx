import { Field, Form, Formik } from 'formik'
import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'
import { getLeadSources } from '../../logic/apis/leadSource'
import { getPosition } from '../../logic/apis/position'
import { getTeamMembers } from '../../logic/apis/team'
import AddressAutocomplete from '../../shared/addressInput/AddressInput'
import { CustomModal } from '../../shared/customModal/CustomModal'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { SharedDate } from '../../shared/date/SharedDate'
import {
  combineDateTime,
  convertFilters,
  dayjsFormat,
  formatPhoneNumber,
  getBooleanFromName,
  getDataFromLocalStorage,
  getIdFromName,
  getKeysFromObjects,
  getNameFromId,
  getNameFrom_Id,
  getValueByKeyAndMatch,
  hasValues,
  isSuccess,
  notify,
  parseTimeString,
  startOfDate,
} from '../../shared/helpers/util'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../styles/styled'
import { AddCityModal } from '../client/components/addCityModal/AddCityModal'
import {
  AddNewClientModal,
  mergeSourceAndCampaignNames,
} from '../client/components/addNewClientModal/AddNewClientModal'
import * as Styled from './style'
import {
  convertToOpportunity,
  createOpportunity,
  deleteLead,
  getPositionMembersById,
  getStages,
} from '../../logic/apis/sales'
import AutoComplete from '../../shared/autoComplete/AutoComplete'
import { I_Stage } from '../opportunity/components/assessmentForm/AssessmentForm'
import { getProjectTypes } from '../../logic/apis/projects'
import AutoCompleteAddress from '../../shared/autoCompleteAdress/AutoCompleteAddress'
import { CrossContainer, ModalContainer, ModalHeader, ModalHeaderContainer, ModalHeaderInfo } from './style'
import { CrossIcon } from '../../assets/icons/CrossIcon'
import UnitSvg from '../../assets/newIcons/unitModal.svg'
import Button from '../../shared/components/button/Button'
import { getReferres, getSalesPersonAndPM } from '../../logic/apis/company'
import { Nue, StageGroupEnum, StorageKey, usStatesShortNames } from '../../shared/helpers/constants'
import { colors } from '../../styles/theme'
import ReferrerModal from '../Refferer/components/referrerModal/ReferrerModal'
import { getDistanceAndDuration, getLatLongFromAddress } from '../../shared/helpers/map'
import { LoadScript } from '@react-google-maps/api'
import { getConfig } from '../../config'
import { SLoader } from '../../shared/components/loader/Loader'
import { getSalesPersonDetailsByClient } from '../../logic/apis/client'
import useDebounce from '../../shared/hooks/useDebounce'
import { NormalInput } from '../../shared/normalInput/NormalInput'
import dayjs from 'dayjs'
import RightArrow from '../../assets/newIcons/rightArrow.svg'
import { getDepartments } from '../../logic/apis/department'
import { SharedDateAndTime } from '../../shared/date/SharedDateAndTime'
import {
  getFormattedLeadSrcData,
  getLeadSrcDropData,
  getLeadSrcDropdownId,
  getLeadSrcDropdownName,
} from '../leadSource/LeadSource'
import AutoCompleteIndentation from '../../shared/autoCompleteIndentation/AutoCompleteIndentation'
import { getContactById, getSearchedContact } from '../../logic/apis/contact'
// import AutoCompleteAddress from '../../shared/autoCompleteAdress/AutoCompleteAddress'

export interface I_LeadSource {
  _id: string
  name: string
  channelId: string
  deleted: boolean
  channelName: string[]
}

export interface I_SalesPerson {
  _id: string
  email: string
  name: string
  // username: string
  company: string
  user: string
  invited: boolean
  roleId: string
  deleted: boolean
}

export interface I_Position {
  _id: string
  position: string
  permissions: any
  deleted: boolean
  createdBy: string
}

export interface I_Client {
  _id: string
  isBusiness: boolean
  name: string
  firstName: string
  lastName: string
  street: string
  city: string
  state: string
  zip: number
  phone: number
  leadSourceName: string
  leadSourceName: string
  businessName: string
  notes: string
  createdBy: string
  deleted: boolean
}

interface ICategoryModal {
  onClose: () => void
  onComplete: () => void
  referrerDropdownData: any
  setReferrerValue: any
  refererres: any
  clientAutoFill: any
  setClientData?: any
  clientData?: any
  // addressLoading: any
  // clientDropdown: any
  setShowAddNewClientModal: React.Dispatch<React.SetStateAction<boolean>>
  setShowEditClientModal: React.Dispatch<React.SetStateAction<boolean>>
  detailsUpdate: any
  addNewClientModal: any
  setClientName: any
  createdClient: any
  isLead?: boolean
  leadInfo?: any
}
const AddOpportunityModal = (props: ICategoryModal) => {
  const {
    onClose,
    onComplete,
    referrerDropdownData,
    setReferrerValue,
    refererres,
    clientAutoFill,
    setClientData,
    clientData,
    // addressLoading,
    // clientDropdown,
    setShowAddNewClientModal,
    setShowEditClientModal,
    detailsUpdate,
    addNewClientModal,
    setClientName,
    createdClient,
    isLead,
    leadInfo,
  } = props
  const [projectTypesDrop, setProjectTypesDrop] = useState<any>([])
  // const [initialvalues, setInitialValues] = useState({
  //   newLeadDate: isLead ? leadInfo?.date : new Date().toISOString(),
  //   leadSourceName: isLead ? leadInfo?.leadSourceName : '',
  //   distance: 0,
  //   duration: 0,
  //   salesPerson: '',
  //   leadCost: '0',
  //   street: '',
  //   referredBy: isLead ? leadInfo?.referredBy : '',
  //   questions: [],
  //   naDate: '',
  //   naTime: '',
  //   oppType: isLead ? getNameFromId(leadInfo?.workType, projectTypesDrop) : '',
  //   oppNotes: '',
  //   notes: '',
  //   client: '',
  //   city: '',
  //   state: '',
  //   zip: '',
  // })

  const navigate = useNavigate()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, companySettingForAll } = globalSelector.company

  const [toggleGoogleAddressInput, setToggleGoogleAddressInput] = useState<boolean>(false)
  // const [showAddCityModal, setShowAddCityModal] = useState<boolean>(false)
  const [addressLoading, setAddressLoading] = useState<boolean>(isLead ? false : true)
  // const [detailsUpdate, setDetailsUpdate] = useState(false)
  // const [addNewClientModal, setShowAddNewClientModal] = useState(false)
  const [projectTypes, setProjectTypes] = useState<any>([])
  // const [clientAutoFill, setClientAutoFill] = useState<any>({})
  // const [clientName, setClientName] = useState<any>([])
  const [workType, setWorkType] = useState('')
  const [lat, setLat] = useState('')
  const [long, setLong] = useState('')
  const [distance, setDistance] = useState(0)
  const [duration, setDuration] = useState(0)
  const [loading, setLoading] = useState(false)
  const [stages, setStages] = useState<I_Stage[]>([])
  const [clientDropdown, setClientDrop] = useState<I_Client[]>([])
  const [clientloader, setClientLoader] = useState<boolean>(false)
  const [cityDropdown, setCityDropdown] = useState<string[]>([])
  const [leadsrcDrop, setLeadsrcDrop] = useState<I_LeadSource[]>([])
  const [leadSrcData, setLeadSrcData] = useState([])

  const [clientLoading, setClientLoading] = useState(false)
  const [salesPersonDrop, setSalesPersonDrop] = useState<I_SalesPerson[]>([])
  const [distanceError, setDistanceError] = useState('')
  const [clientAddress, setClientAddress] = useState('')
  const [defaultStage, setDefaultStage] = useState('') // will be stage id
  const [searchValue, setSearchValue] = useState<string>('')
  const [clientOnSelect, setClientOnSelect] = useState<string>('')
  const [selectedType, setSelectedType] = useState<string>(isLead ? leadInfo?.workTypeName : '')
  const [salesPersonByClient, setSalesPersonByClient] = useState<any>([])
  const [addressInputType, setAddressInputType] = useState<'custom' | 'google'>()
  const [officeDrop, setOfficeDrop] = useState<any[]>([])

  const initialvalues = {
    newLeadDate: isLead ? leadInfo?.date : '',
    oppDate: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
    needsAssessmentDate: '',
    oppType: isLead ? leadInfo?.workTypeName : '',
    oppNotes: '',
    notes: '',
    client: '',
    contactId: '',
    street: isLead ? leadInfo?.street : '',
    city: isLead ? leadInfo?.city : '',
    state: isLead ? leadInfo?.state : '',
    zip: isLead ? leadInfo?.zip : '',
    CSRAssigned: isLead ? leadInfo?.CSRAssigned : '',
    leadSourceName: isLead ? leadInfo?.leadSourceName : '',
    distance: 0,
    duration: 0,
    salesPerson: '',
    leadCost: '0',
    oppLat: '',
    oppLong: '',
    referredBy: isLead ? leadInfo?.referredBy : '',
    questions: [],
    naDate: '',
    naTime: '',
  }

  const debouncedSearch = useDebounce(searchValue, 500)
  // useEffect(() => {
  //   if (clientOnSelect !== '') {
  //     fetchSalesPersonByClient()
  //   }
  // }, [clientOnSelect])

  // useEffect(() => {
  //   if (leadSrcData?.length) {
  //     const data = getLeadSrcDropData(leadSrcData)

  //     setLeadsrcDrop(data)
  //   }
  // }, [leadSrcData?.length])

  // const fetchSalesPersonByClient = async () => {
  //   try {
  //     const clientId = getIdFromName(clientOnSelect, clientDropdown)
  //     const res = await getSalesPersonDetailsByClient(clientId)
  //     if (isSuccess(res)) {
  //       setSalesPersonByClient(res?.data?.data)
  //     }
  //   } catch (error) {
  //     console.log(error)
  //   } finally {
  //   }
  // }
  // useEffect(() => {
  //   if (stage?.length) {
  //     stage.forEach((stage: I_Stage) => {
  //       if (stage.code === 'opp') setDefaultStage(stage._id)
  //     })
  //   }
  // }, [stage])
  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false, StageGroupEnum.Sales)
      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const formatStages: I_Stage[] = new Array(stages.length)
        stages.forEach((stage: I_Stage) => {
          formatStages[stage.sequence - 1] = stage
          if (stage.code === 'opp') setDefaultStage(stage._id)
        })

        setStages(formatStages)
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }

  const getClientsData = async () => {
    try {
      setClientLoader(true)
      let receivedData: any = []
      const response = await getSearchedContact(debouncedSearch, {
        fields: {
          fullName: 1,
          firstName: 1,
          lastName: 1,
          leadSourceId: 1,
          campaignId: 1,
          distance: 1,
          referredBy: 1,
          street: 1,
          city: 1,
          state: 1,
          zip: 1,
        },
      })

      if (isSuccess(response)) {
        let statusRes = response?.data?.data?.contacts
        statusRes.forEach((res: any, index: number) => {
          receivedData.push({
            ...res,
            name: `${res?.fullName?.trim()}`,
            status: '-',
            address: res?.street,
            phone: formatPhoneNumber(res?.phone, ''),
            isBusiness: res?.isBusiness,
            clientId: res._id,
          })
        })
        setClientDrop(receivedData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      notify('Failed to fetch clients!', 'error')
      console.log('Contact fetch failed', err)
    } finally {
      setAddressLoading(false)
      setClientLoader(false)
    }
  }
  useEffect(() => {
    if (!isLead) {
      getClientsData()
    }
  }, [debouncedSearch])

  useEffect(() => {
    initFetch()
    // initFetchReferrers()
  }, [])

  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name }: { _id: string; name: string }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
        }))
        setProjectTypes(projectType)
        setProjectTypesDrop(object)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const handleSubmit = async (values: typeof initialvalues) => {
    // ----------------------lead date compare with na date---------------------
    // const oppDateDayStartOf = startOfDate(values.oppDate)
    // values.oppDate = startOfDate(values.oppDate)

    if (values?.needsAssessmentDate) {
      if (new Date(values.oppDate) > new Date(values?.needsAssessmentDate)) {
        notify(
          `Appointment date must be after Opportunity date ${dayjsFormat(values.oppDate, 'MMM DD, YYYY @ hh:mm A')}`,
          'error'
        )
        throw new Error('date error')
      }
    }

    if (isLead) {
      if (new Date(values.oppDate) < new Date(values.newLeadDate)) {
        notify(
          `Opportunity date must be after Lead date: ${dayjsFormat(values.newLeadDate, 'MMM DD, YYYY @ hh:mm A')}`,
          'error'
        )
        throw new Error('date error')
      }
    }

    if (values?.street && values?.city && values?.zip && values?.state) {
      const latLong = await getLatLongFromAddress(
        `${values?.street || ''}, ${values?.city || ''}, ${values?.state || ''} ${values?.zip || ''}, USA`
      )
      values.oppLat = latLong?.lat || ''
      values.oppLong = latLong?.lng || ''
    }

    // ----------------------lead date compare with na date---------------------

    const type = getIdFromName(values.oppType || workType, projectTypesDrop)
    const referredById = getIdFromName(values?.referredBy, refererres)
    const leadSourceId = getIdFromName(values?.leadSourceName, leadSrcData)
    const CSRAssignedId = getIdFromName(values?.CSRAssigned, officeDrop)

    const formattedData = values?.questions?.length
      ? values?.questions
          ?.map((question) => {
            const stateName: any = question
              ?.replace(/\s+/g, '-') // Removes all spaces
              ?.replace(/\?/g, '') // Removes all question marks

            // Get the corresponding value from Formik's state
            const answer = values?.[stateName]?.trim() || ''

            // Format the question and answer as needed
            return answer ? `${question} **${answer}**\n` : `${question} \n`
          })
          ?.join('')
      : ''

    const foundClient = clientDropdown?.find((client: any) => client?.name === values?.client)

    // Join all strings with a newline character
    values.oppNotes =
      (formattedData ? `${formattedData}\n` : '') +
      `Comments/Notes: ${values?.notes ? `**${values?.notes?.trim()}**` : ''}\n` +
      `${foundClient?.notes ? `Contact Notes: **${foundClient?.notes?.trim()}**` : ''}`
    // Do something with formattedData, like sending it to an API or displaying it
    const { notes, questions, naDate, naTime, contactId, ...restObject }: any = values
    if (questions?.length) {
      // Extract question keys (stateNames) and remove them from restObject
      const questionKeys = questions.map((question) => {
        return question
          ?.replace(/\s+/g, '-') // Replace spaces with hyphens
          ?.replace(/\?/g, '') // Remove question marks
      })

      // Remove the question keys from restObject
      questionKeys.forEach((key) => {
        delete restObject?.[key]
      })
    }
    // if (naDate && naTime) {
    //   let updateDate = startOfDate(dayjsFormat(naDate, 'YYYY-MM-DD'))
    //   let currDate: any = combineDateTime(new Date(updateDate), new Date(parseTimeString(naTime))).toISOString()
    //   restObject.needsAssessmentDate = currDate
    // }
    setLoading(true)
    try {
      // const [city, state] = values.addrCityState.split(',')
      const isBusiness = getBooleanFromName(values.client, clientDropdown)

      const { salesPerson } = values
      let salesPersonId = ''

      salesPersonDrop.forEach((sp) => {
        if (salesPerson === sp.name) {
          salesPersonId = sp._id
        }
      })
      values.distance = values.distance ? Number(values.distance) : 0
      values.duration = values.duration ? Number(values.duration) : 0

      const code = getValueByKeyAndMatch('code', values?.leadSourceName, 'name', leadSrcData)
      // console.log({ createdClient, foundClient, restObject }, values?.CSRAssigned, officeDrop, CSRAssignedId)

      const result = getLeadSrcDropdownId(values?.leadSourceName, leadSrcData)
      const campaignId = result?.campaignId || undefined
      const campaignName = result?.campaignName || undefined
      if (isLead) {
        const res = await convertToOpportunity({
          ...restObject,
          contactId: createdClient?._id || foundClient?._id,
          comments: [],
          oppDate: new Date(values.oppDate),
          // newLeadDate: new Date(values.newLeadDate),
          needsAssessmentDate: values?.needsAssessmentDate ? new Date(values?.needsAssessmentDate) : undefined,
          companyAddress: companySettingForAll?.address,
          companyLang: companySettingForAll?.longitude,
          companyLat: companySettingForAll?.latitude,
          createdBy: currentMember._id,
          firstName: hasValues(createdClient)
            ? createdClient?.firstName
            : hasValues(foundClient)
            ? foundClient?.firstName
            : '',
          lastName: hasValues(createdClient)
            ? createdClient?.lastName
            : hasValues(foundClient?.lastName)
            ? foundClient?.lastName
            : '',
          csrId: CSRAssignedId,
          leadSource: result?.leadSourceName,
          oppLat: `${values?.oppLat}` || undefined,
          oppLong: `${values?.oppLong}` || undefined,
          leadSourceId: result?.leadSourceId ? result?.leadSourceId : undefined,
          oppType: type,
          referredBy: referredById !== '' ? referredById : undefined,
          salesPerson: salesPersonId,
          selfGen: code === 'selfGen' ? true : false,
          stage: defaultStage,
          // converted: isLead ? true : false,
          leadId: isLead ? leadInfo?.leadId : undefined,
          currentDate: new Date().toISOString(),
          campaignId,
          campaignName,
        })
        if (isSuccess(res)) {
          await deleteLead(leadInfo?.leadId)
          navigate(`/sales/opportunity/${res?.data?.data?.oppId}`)
        }
      } else {
        // console.log(startOfDate(values.newLeadDate), 'xfxcvcv', values.newLeadDate)

        const response = await createOpportunity({
          ...restObject,
          contactId: createdClient?._id || foundClient?._id,
          comments: [],
          oppDate: new Date(values.oppDate),
          newLeadDate: new Date(values.oppDate),
          needsAssessmentDate: values?.needsAssessmentDate ? new Date(values?.needsAssessmentDate) : undefined,
          companyAddress: companySettingForAll?.address,
          companyLang: companySettingForAll?.longitude,
          companyLat: companySettingForAll?.latitude,
          createdBy: currentMember._id,
          leadSource: result?.leadSourceName,
          firstName: hasValues(createdClient)
            ? createdClient?.firstName
            : hasValues(foundClient)
            ? foundClient?.firstName
            : '',
          lastName: hasValues(createdClient)
            ? createdClient?.lastName
            : hasValues(foundClient?.lastName)
            ? foundClient?.lastName
            : '',
          csrId: CSRAssignedId,
          oppLat: `${values?.oppLat}` || undefined,
          oppLong: `${values?.oppLong}` || undefined,
          leadSourceId: result?.leadSourceId ? result?.leadSourceId : undefined,
          oppType: type,
          referredBy: referredById !== '' ? referredById : undefined,
          salesPerson: salesPersonId,
          selfGen: code === 'selfGen' ? true : false,
          stage: defaultStage,
          // converted: isLead ? true : false,
          leadId: isLead ? leadInfo?.leadId : undefined,
          currentDate: new Date().toISOString(),
          campaignId,
          campaignName,
        })

        setLoading(false)
        if (isSuccess(response)) {
          notify('Created new opportunity!', 'success')
          onComplete()
        } else throw new Error(response?.data?.message)
      }
    } catch (err: any) {
      setLoading(false)
      notify(err?.message ?? 'Something went wrong!', 'error')
      console.log('failed new lead submit', err)
    }
  }

  const fetchClientById = async (clId: string) => {
    setClientLoading(true)
    try {
      const clientResponse = await getContactById(clId!)
      if (isSuccess(clientResponse)) {
        setClientData(clientResponse?.data?.data?.contact)
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setClientLoading(false)
    }
  }

  const getLeadSrcData = async () => {
    try {
      const leadSourceResponse = await getLeadSources({ limit: '100', active: true }, false)
      if (leadSourceResponse?.data?.statusCode === 200) {
        let statusRes = leadSourceResponse?.data?.data?.leadSource
        setLeadSrcData(statusRes)
      } else notify(leadSourceResponse?.data?.message, 'error')
    } catch (err) {
      // notify('Failed to fetch lead sources!', 'error')
      console.log('Lead source fetch error', err)
    }
  }

  const getPositionMembers = async (positionId: string) => {
    try {
      // const response = await getPositionMembersById({ positionId }, false) // NHR-1570
      const response = await getSalesPersonAndPM() // NHR-1570
      if (isSuccess(response)) {
        // setSalesPersonDrop(response?.data?.data?.memberData) // NHR-1570
        setSalesPersonDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const getPositions = async () => {
    try {
      const response = await getPosition({ deleted: false }, false)
      if (isSuccess(response)) {
        const positions: I_Position[] = response?.data?.data?.position
        // let salesPersonIdx = 0
        // positions.forEach((position: any, idx) => {
        //   if (position.symbol === 'SalesPerson') {
        //     salesPersonIdx = idx
        //     return
        //   }
        // })
        // getPositionMembers(positions[salesPersonIdx]._id)
        let salesPersonIdx: string[] = []
        positions.forEach((position: any, idx) => {
          if (position.symbol === 'SalesPerson') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'GeneralManager') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'SalesManager') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'RRTech') {
            salesPersonIdx.push(position?._id)
            return
          }
        })
        getPositionMembers(salesPersonIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  const getPositionsCSR = async () => {
    try {
      const response = await getDepartments({ deleted: false }, false)
      if (isSuccess(response)) {
        const departments: I_Position[] = response?.data?.data?.department

        let officePersonIdx: string[] = []
        departments.forEach((department: any, idx) => {
          if (department.name === 'Office') {
            officePersonIdx.push(department?._id)
            return
          }
        })
        getPositionMembersForDepartment(officePersonIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  const getPositionMembersForDepartment = async (departmentId: string) => {
    try {
      // const response = await getPositionMembersById({ departmentId }, false) // NHR-1567

      const response = await getSalesPersonAndPM(departmentId) // NHR-1567
      if (isSuccess(response)) {
        // setOfficeDrop(response?.data?.data?.memberData) // NHR-1567
        setOfficeDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const isPayPerLead = (leadSrc: any) => {
    return leadSrc && leadSrc?.channelName === 'defOnlinePayPerLead' // channel
  }

  const getObjectFromKey = (Objs: Array<any>, key: string, value: string) => {
    const [obj] = Objs.filter((obj) => obj[key] === value)
    return obj
  }

  useEffect(() => {
    getLeadSrcData()
    getPositions()
    getStagesData()
    getPositionsCSR()
  }, [detailsUpdate])

  const handleClientSelect = (name: string, clients: typeof clientDropdown, setFieldValue: any) => {
    let clientData: I_Client | undefined
    clients.forEach((client) => {
      if (client.name === name) {
        clientData = client
        return
      }
    })
    // if (clientData) {
    //   clientData
    //   console.log({ clientData, option: `${clientData?.city}, ${clientData?.state}` }, 'CLIENT DATA')
    //   // setFieldValue('')
    // }
  }

  const newLeadSchema = Yup.object().shape({
    oppDate: Yup.string().required('Required'),
    oppType: Yup.string().required('Required'),
    notes: Yup.string(),
    client: Yup.string().required('Required'),
    leadSourceName: Yup.string().required('Required'),
    CSRAssigned: Yup.string().required('Required'),
    duration: Yup.number().when(['street', 'city', 'state', 'zip'], {
      is: (street, city, state, zip) => !street && !city && !state && !zip,
      then: Yup.number().notRequired(),
      otherwise: Yup.number().required('Please enter Duration').min(1, 'Please enter Duration'),
    }),
    salesPerson: Yup.string().required('Required'),
  })

  return (
    <>
      <ModalContainer>
        <ModalHeaderContainer>
          <SharedStyled.FlexRow>
            <img src={UnitSvg} alt="modal icon" />
            <SharedStyled.FlexCol>
              <ModalHeader>Add Opportunity</ModalHeader>
            </SharedStyled.FlexCol>
          </SharedStyled.FlexRow>
          <CrossContainer
            onClick={() => {
              onClose()
            }}
          >
            <CrossIcon />
          </CrossContainer>
        </ModalHeaderContainer>
        <SharedStyled.SettingModalContentContainer>
          <>
            <Formik
              initialValues={initialvalues}
              onSubmit={handleSubmit}
              validationSchema={newLeadSchema}
              validateOnChange={true}
              validateOnBlur={false}
              enableReinitialize={false}
            >
              {({ errors, touched, values, validateForm, setFieldValue }) => {
                console.log({ values, errors })
                useEffect(() => {
                  if (stages?.length && officeDrop?.length && !isLead) {
                    setFieldValue(
                      'CSRAssigned',
                      getValueByKeyAndMatch('name', stages?.[0]?.defaultCsrId, '_id', officeDrop)
                    )
                  }
                }, [stages, officeDrop, isLead])

                useEffect(() => {
                  if (Object.entries(clientAutoFill).length) {
                    if (isLead) {
                      setFieldValue('client', clientAutoFill?.clientName || '')
                      if (leadInfo?.street || leadInfo?.city || leadInfo?.zip || leadInfo?.state) {
                        setClientAddress(
                          `${leadInfo?.street || ''}, ${leadInfo?.city || ''}, ${leadInfo?.state || ''} ${
                            leadInfo?.zip || ''
                          }, USA`
                        )
                      } else {
                        setFieldValue('street', clientAutoFill?.sStreet)
                        setFieldValue('city', clientAutoFill?.sCity)
                        setFieldValue('state', clientAutoFill?.sState)
                        setFieldValue('zip', clientAutoFill?.sZip)
                        setClientAddress(
                          `${clientAutoFill?.sStreet || ''}, ${clientAutoFill?.sCity || ''}, ${
                            clientAutoFill?.sState || ''
                          } ${clientAutoFill?.sZip || ''}, USA`
                        )
                      }
                    } else {
                      // const res = getLeadSrcDropdownId(clientAutoFill?.sLeadSource, leadSrcData)
                      const sourceName = getLeadSrcDropdownName(
                        clientAutoFill?.sCampaignId || clientAutoFill?.sLeadSource,
                        leadSrcData
                      )?.sourceName

                      setFieldValue('client', clientAutoFill?.clientName || '')
                      setFieldValue('street', clientAutoFill?.sStreet)
                      setFieldValue('city', clientAutoFill?.sCity)
                      setFieldValue('state', clientAutoFill?.sState)
                      setFieldValue('zip', clientAutoFill?.sZip)
                      // setFieldValue('distance', clientAutoFill?.distance)
                      // setFieldValue('duration', clientAutoFill?.duration)
                      // setFieldValue('leadSourceName', getNameFrom_Id(clientAutoFill?.sLeadSource, leadSrcData))
                      setFieldValue('leadSourceName', sourceName)
                      setFieldValue('referredBy', getNameFrom_Id(clientAutoFill?.referredBy, refererres))
                      // setSalesPersonByClient({})
                      setClientOnSelect('')
                      if (
                        clientAutoFill?.sState &&
                        clientAutoFill?.sCity &&
                        clientAutoFill?.sZip &&
                        clientAutoFill?.sState
                      ) {
                        setClientAddress(
                          `${clientAutoFill?.sStreet || ''}, ${clientAutoFill?.sCity || ''}, ${
                            clientAutoFill?.sState || ''
                          } ${clientAutoFill?.sZip || ''}, USA`
                        )
                      }
                    }
                    // setDuration(clientAutoFill?.duration || 0)
                  }
                }, [clientAutoFill])

                useEffect(() => {
                  if (clientAddress !== '') {
                    getDistanceAndDuration(companySettingForAll?.address, clientAddress)
                      .then(({ distance, duration }: any) => {
                        const distanceInfo = Math.ceil(Number(distance / 1609.34))
                        const durationInfo = Math.round(duration / 60) ?? 0
                        setFieldValue('distance', distanceInfo)
                        setFieldValue('duration', durationInfo)
                      })
                      .catch((error) => {
                        console.error(error)
                      })
                  } else {
                    setFieldValue('distance', 0)
                    setFieldValue('duration', 0)
                  }
                  // }
                }, [clientAddress])
                console.log({ clientAddress })
                useEffect(() => {
                  setClientName(values.client)
                  setSearchValue(values.client)
                }, [values.client])

                useEffect(() => {
                  if (duration > 0) {
                    setFieldValue('duration', duration)
                  }
                }, [duration])

                useEffect(() => {
                  if (values.oppType) {
                    setWorkType(values.oppType)
                  }
                }, [values.oppType])

                useEffect(() => {
                  if (workType) {
                    setFieldValue('oppType', workType)
                  }
                }, [workType])

                useEffect(() => {
                  if (selectedType) {
                    const result = projectTypes?.find((v) => v.name === selectedType)?.questions || []
                    setFieldValue('questions', result)
                  }
                }, [selectedType, projectTypes])

                useEffect(() => {
                  if (values.contactId !== '') {
                    fetchClientById(values.contactId)
                  }
                }, [values.contactId])

                return (
                  <LoadScript
                    googleMapsApiKey={getConfig().googleAddressApiKey}
                    //  @ts-ignore
                    libraries={['places']}
                    loadingElement={<SLoader height={35} width={100} isPercent />}
                  >
                    <Styled.NewLeadContainer>
                      <Form className="form">
                        <SharedStyled.Content
                          overflow={'unset'}
                          disableBoxShadow={true}
                          noPadding={true}
                          gap="8px"
                          width="100%"
                        >
                          <SharedDateAndTime
                            value={values.oppDate}
                            labelName={
                              isLead ? 'Date/Time converted to Opportunity *' : 'Date/Time Opportunity came in *'
                            }
                            stateName="oppDate"
                            setFieldValue={setFieldValue}
                            error={touched.oppDate && errors.oppDate ? true : false}
                          />

                          <SharedStyled.Text fontSize="16px" fontWeight="700" textAlign="left" margin="8px auto 0px 0">
                            Contact Info
                          </SharedStyled.Text>

                          {isLead ? (
                            <>
                              <Button
                                onClick={() => setShowAddNewClientModal(true)}
                                style={{ alignSelf: 'baseline' }}
                                type="button"
                                className="fit"
                              >
                                Add Contact
                              </Button>
                              <SharedStyled.FlexBox gap="10px" width="100%">
                                <div style={{ width: '100%' }}>
                                  <SharedStyled.Text textDecoration="underline" fontWeight="600">
                                    Lead Info
                                  </SharedStyled.Text>
                                  <br />
                                  <div>
                                    <SharedStyled.Text fontWeight="400">
                                      <span style={{ fontFamily: Nue.regular, color: 'red' }}>
                                        {leadInfo?.firstName + ' ' + leadInfo?.lastName || ''}
                                      </span>
                                    </SharedStyled.Text>
                                  </div>
                                  {(leadInfo.street !== '' ||
                                    leadInfo.city !== '' ||
                                    leadInfo.state !== '' ||
                                    leadInfo.zip !== '') && (
                                    <div style={{ width: '100%' }}>
                                      <SharedStyled.Text fontWeight="400">
                                        <span style={{ fontFamily: Nue.regular, color: 'red' }}>
                                          {leadInfo.street !== '' ? leadInfo.street : ''}
                                        </span>
                                      </SharedStyled.Text>
                                      <br />
                                      <SharedStyled.Text fontWeight="400">
                                        {/* <b>City : </b> */}
                                        <span style={{ fontFamily: Nue.regular, color: 'red' }}>
                                          {leadInfo.city || '--'},&nbsp;
                                        </span>
                                        {/* <b>State : </b> */}
                                        <span style={{ fontFamily: Nue.regular, color: 'red' }}>
                                          {leadInfo.state || '--'},&nbsp;
                                        </span>
                                        {/* <b>Zip : </b> */}
                                        <span style={{ fontFamily: Nue.regular, color: 'red' }}>
                                          {leadInfo.zip || '--'}
                                        </span>
                                      </SharedStyled.Text>
                                      &emsp;
                                    </div>
                                  )}
                                  <SharedStyled.Text fontWeight="400">
                                    <span style={{ fontFamily: Nue.regular, color: 'red' }}>
                                      {formatPhoneNumber(leadInfo?.phone, '') || ''} | {leadInfo?.email || ''}
                                    </span>
                                  </SharedStyled.Text>
                                  &emsp;
                                </div>

                                {Object.entries(clientAutoFill)?.length ? (
                                  <>
                                    <div style={{ margin: 'auto 0' }}>
                                      <img src={RightArrow} alt={''} />
                                    </div>
                                    <div style={{ width: '100%' }}>
                                      <div>
                                        {' '}
                                        <SharedStyled.Text fontWeight="400">
                                          <SharedStyled.Text textDecoration="underline" fontWeight="600">
                                            Contact Info
                                          </SharedStyled.Text>
                                          <br />
                                          <span style={{ fontFamily: Nue.regular, color: 'green' }}>
                                            {' '}
                                            {clientAutoFill?.clientName || ''}
                                          </span>
                                        </SharedStyled.Text>
                                      </div>
                                      {(clientAutoFill.sStreet !== '' ||
                                        clientAutoFill.sCity !== '' ||
                                        clientAutoFill.sState !== '' ||
                                        clientAutoFill.sZip !== '') && (
                                        <div style={{ width: '100%' }}>
                                          <SharedStyled.Text fontWeight="400">
                                            <span style={{ fontFamily: Nue.regular, color: 'green' }}>
                                              {clientAutoFill.sStreet !== '' ? clientAutoFill.sStreet : ''}
                                            </span>
                                          </SharedStyled.Text>
                                          <br />
                                          <SharedStyled.Text fontWeight="400">
                                            {/* <b>City : </b> */}
                                            <span style={{ fontFamily: Nue.regular, color: 'green' }}>
                                              {clientAutoFill.sCity || '--'},&nbsp;
                                            </span>
                                            {/* <b>State : </b> */}
                                            <span style={{ fontFamily: Nue.regular, color: 'green' }}>
                                              {clientAutoFill.sState || '--'},&nbsp;
                                            </span>
                                            {/* <b>Zip : </b> */}
                                            <span style={{ fontFamily: Nue.regular, color: 'green' }}>
                                              {clientAutoFill.sZip || '--'}
                                            </span>
                                          </SharedStyled.Text>
                                          &emsp;
                                        </div>
                                      )}
                                      <SharedStyled.Text fontWeight="400">
                                        <span style={{ fontFamily: Nue.regular, color: 'green' }}>
                                          {formatPhoneNumber(clientAutoFill?.phone, '') || ''}{' '}
                                          {clientAutoFill?.email ? '|' : ''} {clientAutoFill?.email || ''}
                                        </span>
                                      </SharedStyled.Text>
                                      &emsp;
                                    </div>
                                    {/* <div>
                                      <Button
                                        type="button"
                                        onClick={() => {
                                          setClientData(createdClient)
                                          setShowEditClientModal(true)
                                        }}
                                      >
                                        Edit
                                      </Button>
                                    </div> */}
                                  </>
                                ) : null}
                              </SharedStyled.FlexBox>
                            </>
                          ) : (
                            <>
                              <AutoComplete
                                labelName="Select a contact *"
                                stateName="client"
                                error={touched.client && errors.client ? true : false}
                                setFieldValue={setFieldValue}
                                options={getKeysFromObjects(clientDropdown, 'name').map((client) => client.trim())}
                                value={values.client || ''}
                                showAddOption
                                addNewText="Add contact"
                                autoFillData={clientDropdown}
                                // setDuration={setDuration}
                                onAddClick={() => {
                                  setShowAddNewClientModal(true)
                                }}
                                className="clientHeight"
                                refererres={refererres}
                                leadSrcData={leadSrcData}
                                setClientAddress={setClientAddress}
                                apiSearch={true}
                                searchLoader={clientloader}
                                setValueOnClick={setClientOnSelect}
                              />
                            </>
                          )}

                          {clientLoading ? (
                            <SharedStyled.FlexCol gap="5px">
                              <SLoader height={15} width={100} isPercent />
                              <SLoader height={5} width={100} isPercent />
                              <SLoader height={5} width={100} isPercent />
                              <SLoader height={5} width={100} isPercent />
                            </SharedStyled.FlexCol>
                          ) : hasValues(clientData) ? (
                            <SharedStyled.FlexCol gap="10px">
                              <SharedStyled.FlexRow justifyContent="space-between">
                                <div>
                                  <SharedStyled.Text fontSize="14px">
                                    {formatPhoneNumber(clientData.phone, '') || '--'}
                                  </SharedStyled.Text>
                                  <br />{' '}
                                  <SharedStyled.Text fontSize="14px">{clientData.email || '--'}</SharedStyled.Text>
                                </div>
                                <div>
                                  <SharedStyled.Text fontSize="14px">
                                    {clientData.street !== '' ? clientData.street : ''}
                                    <br />
                                    {/* <b>City : </b> */}
                                    {clientData.city},&nbsp;
                                    {/* <b>State : </b> */}
                                    {clientData.state},&nbsp;
                                    {/* <b>Zip : </b> */}
                                    {clientData.zip}
                                  </SharedStyled.Text>
                                </div>
                                <div>
                                  <Button type="button" onClick={() => setShowEditClientModal(true)}>
                                    Edit
                                  </Button>
                                </div>
                              </SharedStyled.FlexRow>

                              {clientData?.contacts?.length > 0 ? (
                                <>
                                  <SharedStyled.FlexCol>
                                    {clientData?.contacts?.map(
                                      (
                                        {
                                          firstName,
                                          lastName,
                                          phone,
                                          email,
                                        }: {
                                          firstName: string
                                          lastName: string
                                          phone: string
                                          email: string
                                        },
                                        index: number
                                      ) => (
                                        <SharedStyled.FlexRow
                                          width="95%"
                                          margin="0 0 0 auto"
                                          justifyContent="space-between"
                                        >
                                          <div>
                                            <SharedStyled.Text fontSize="12px">
                                              {index + 1}. {firstName || ''} {lastName || ''}
                                            </SharedStyled.Text>
                                          </div>
                                          <div>
                                            <SharedStyled.Text fontSize="12px">
                                              {formatPhoneNumber(phone, '')}
                                            </SharedStyled.Text>
                                          </div>
                                          <div>
                                            <SharedStyled.Text fontSize="12px">{email}</SharedStyled.Text>
                                          </div>
                                        </SharedStyled.FlexRow>
                                      )
                                    )}
                                  </SharedStyled.FlexCol>
                                </>
                              ) : (
                                <></>
                              )}
                            </SharedStyled.FlexCol>
                          ) : (
                            <></>
                          )}

                          <Styled.AddressContainer>
                            {
                              <h2 className="sub-heading">
                                Address:{' '}
                                <SharedStyled.BlueEdit
                                  onClick={() => {
                                    setToggleGoogleAddressInput(true)
                                    setAddressInputType('google')
                                  }}
                                >
                                  {toggleGoogleAddressInput ? '' : 'Edit'}
                                </SharedStyled.BlueEdit>
                              </h2>
                            }

                            {toggleGoogleAddressInput ? (
                              <Styled.GoogleSearchBox>
                                {addressInputType === 'custom' ? (
                                  <Styled.AddressCont gap="12px" id="custom">
                                    <SharedStyled.FlexCol gap="4px">
                                      <SharedStyled.FlexRow className="input">
                                        <div title="Street" id="street">
                                          <InputWithValidation
                                            labelName="Street"
                                            stateName="street"
                                            error={touched.street && errors.street ? true : false}
                                            twoInput={true}
                                          />
                                        </div>
                                      </SharedStyled.FlexRow>

                                      <SharedStyled.FlexRow className="input" justifyContent="space-between">
                                        <div id="city">
                                          <InputWithValidation
                                            labelName="City"
                                            stateName="city"
                                            error={touched.city && errors.city ? true : false}
                                            twoInput={true}
                                          />
                                        </div>

                                        <div id="state">
                                          <CustomSelect
                                            dropDownData={companySettingForAll?.workingStates || []}
                                            setValue={() => {}}
                                            stateName="state"
                                            value={values.state}
                                            // error={touched.weekStartDay && errors.weekStartDay ? true : false}
                                            setFieldValue={setFieldValue}
                                            labelName="State"
                                            innerHeight="52px"
                                            margin="10px 0 0 0"
                                          />
                                        </div>

                                        <div id="zip">
                                          <InputWithValidation
                                            labelName="Zip"
                                            stateName="zip"
                                            error={touched.zip && errors.zip ? true : false}
                                            twoInput={true}
                                          />
                                        </div>
                                      </SharedStyled.FlexRow>

                                      <SharedStyled.FlexRow
                                        style={{ width: '100%' }}
                                        margin="4px 0 0 0"
                                        flexWrap="wrap"
                                        justifyContent="space-between"
                                      >
                                        <SharedStyled.FlexRow width="max-content">
                                          <Button
                                            // className="delete"
                                            type="button"
                                            width="max-content"
                                            onClick={() => {
                                              setToggleGoogleAddressInput(false)
                                              setAddressInputType('google')
                                            }}
                                          >
                                            Save
                                          </Button>
                                          <Button
                                            className="delete"
                                            type="button"
                                            width="max-content"
                                            onClick={() => {
                                              setToggleGoogleAddressInput(false)
                                              setAddressInputType('google')
                                            }}
                                          >
                                            Cancel
                                          </Button>
                                        </SharedStyled.FlexRow>
                                        <Button
                                          type="button"
                                          onClick={() => {
                                            setAddressInputType('google')
                                            setToggleGoogleAddressInput(true)
                                          }}
                                          className="gray"
                                          width="max-content"
                                        >
                                          Google
                                        </Button>
                                      </SharedStyled.FlexRow>
                                    </SharedStyled.FlexCol>
                                  </Styled.AddressCont>
                                ) : (
                                  <Styled.AddressCont gap="12px" className="google" id="google">
                                    <SharedStyled.FlexCol margin="10px 0 0 0" width="100%">
                                      <AutoCompleteAddress
                                        setFieldValue={setFieldValue}
                                        street={'street'}
                                        city={'city'}
                                        state={'state'}
                                        zip={'zip'}
                                        distance={'distance'}
                                        duration={'duration'}
                                        sourceAddress={companySettingForAll?.address}
                                        companyLatLong={companySettingForAll}
                                        setLat={setLat}
                                        setLong={setLong}
                                        setDistance={setDistance}
                                        setDuration={setDuration}
                                        noLoadScript={true}
                                      />
                                      <SharedStyled.FlexRow
                                        style={{ width: '100%' }}
                                        margin="4px 0 0 0"
                                        flexWrap="wrap"
                                        justifyContent="space-between"
                                      >
                                        <SharedStyled.FlexRow width="max-content">
                                          <Button
                                            // className="delete"
                                            type="button"
                                            width="max-content"
                                            onClick={() => {
                                              setToggleGoogleAddressInput(false)
                                              setAddressInputType('google')
                                            }}
                                          >
                                            Save
                                          </Button>
                                          <Button
                                            className="delete"
                                            type="button"
                                            width="max-content"
                                            onClick={() => {
                                              setToggleGoogleAddressInput(false)
                                            }}
                                          >
                                            Cancel
                                          </Button>
                                        </SharedStyled.FlexRow>
                                        <Button
                                          type="button"
                                          onClick={() => {
                                            setAddressInputType('custom')
                                            setToggleGoogleAddressInput(true)
                                          }}
                                          className="gray"
                                          width="max-content"
                                        >
                                          Custom
                                        </Button>
                                      </SharedStyled.FlexRow>
                                    </SharedStyled.FlexCol>
                                  </Styled.AddressCont>
                                )}
                              </Styled.GoogleSearchBox>
                            ) : (
                              !addressLoading &&
                              (values.street !== '' ||
                                values.city !== '' ||
                                values.state !== '' ||
                                values.zip !== '') && (
                                <div style={{ width: '100%' }}>
                                  <SharedStyled.Text fontWeight="400">
                                    <span style={{ fontFamily: Nue.regular }}>
                                      {values.street !== '' ? values.street : ''}
                                    </span>
                                  </SharedStyled.Text>
                                  <br />
                                  <SharedStyled.Text fontWeight="400">
                                    {/* <b>City : </b> */}
                                    <span style={{ fontFamily: Nue.regular }}>{values.city},&nbsp;</span>
                                    {/* <b>State : </b> */}
                                    <span style={{ fontFamily: Nue.regular }}>{values.state},&nbsp;</span>
                                    {/* <b>Zip : </b> */}
                                    <span style={{ fontFamily: Nue.regular }}>{values.zip}</span>
                                  </SharedStyled.Text>
                                  &emsp;
                                </div>
                              )
                            )}
                          </Styled.AddressContainer>

                          {values.street && values.city && values.state && values.zip && (
                            <SharedStyled.TwoInputDiv>
                              <InputWithValidation
                                labelName="Drive Time"
                                stateName="duration"
                                twoInput={true}
                                disabled={addressInputType !== 'custom'}
                                error={touched.duration && errors.duration ? true : false}
                              />
                              <InputWithValidation
                                labelName="Distance"
                                stateName="distance"
                                twoInput={true}
                                disabled={addressInputType !== 'custom'}
                                error={touched.distance && errors.distance ? true : false}
                              />
                            </SharedStyled.TwoInputDiv>
                          )}

                          {/* <CustomSelect
                            labelName="Lead source *"
                            stateName="leadSourceName"
                            error={touched.leadSourceName && errors.leadSourceName ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.leadSourceName}
                            dropDownData={getKeysFromObjects(leadSrcData, 'name')?.sort()}
                            setValue={() => {}}
                            innerHeight="52px"
                            className="top"
                            // testId
                          /> */}

                          {leadSrcData?.length ? (
                            <AutoCompleteIndentation
                              labelName="Lead Source*"
                              stateName={`leadSourceName`}
                              isLeadSource
                              dropdownHeight="300px"
                              error={touched.leadSourceName && errors.leadSourceName ? true : false}
                              borderRadius="0px"
                              setFieldValue={setFieldValue}
                              options={mergeSourceAndCampaignNames(leadSrcData)}
                              formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                              value={values.leadSourceName!}
                              setValueOnClick={(val: string) => {
                                setFieldValue('leadSourceName', val)
                              }}
                              className="material-autocomplete"
                              isIndentation={true}
                            />
                          ) : null}

                          {/* hardcoded code */}
                          {values.leadSourceName === 'Referral' && (
                            <SharedStyled.FlexBox width="100%" justifyContent="end">
                              <CustomSelect
                                labelName="Referrer"
                                stateName="referredBy"
                                error={touched.referredBy && errors.referredBy ? true : false}
                                setFieldValue={setFieldValue}
                                setValue={setReferrerValue}
                                value={values.referredBy}
                                dropDownData={referrerDropdownData}
                                innerHeight="52px"
                                className="top"
                                maxWidth="95%"
                              />
                            </SharedStyled.FlexBox>
                          )}

                          {isLead ? (
                            <></>
                          ) : (
                            <CustomSelect
                              labelName="CSR Assigned"
                              stateName="CSRAssigned"
                              error={touched.CSRAssigned && errors.CSRAssigned ? true : false}
                              setFieldValue={setFieldValue}
                              value={values.CSRAssigned}
                              dropDownData={getKeysFromObjects(officeDrop, 'name')}
                              setValue={() => {}}
                              innerHeight="52px"
                              margin="10px 0 0 0"
                            />
                          )}
                          <SharedStyled.Text
                            fontSize="16px"
                            fontWeight="700"
                            textAlign="left"
                            margin="8px auto -10px 0"
                          >
                            Project Info
                          </SharedStyled.Text>
                          <CustomSelect
                            labelName="Type of work requested *"
                            stateName="oppType"
                            error={!workType && touched.oppType && errors.oppType ? true : false}
                            setFieldValue={setFieldValue}
                            value={values.oppType || workType}
                            dropDownData={projectTypesDrop.map(({ name }: { name: string }) => name)}
                            setValue={setSelectedType}
                            innerHeight="52px"
                            margin="10px 0 0 0"
                          />

                          <SharedStyled.FlexCol width="96%" margin="0 0 0 auto">
                            {values?.questions?.map((question, index) => {
                              const stateName = question?.replace(/\s+/g, '-').replace(/\?/g, '') // Removes spaces and question marks to create a valid name
                              return (
                                <InputWithValidation
                                  key={index}
                                  labelName={question}
                                  stateName={stateName}
                                  error={false}
                                />
                              )
                            })}

                            <Styled.TextArea
                              component="textarea"
                              placeholder="Comments/Notes"
                              as={Field}
                              name="notes"
                              marginTop="8px"
                              height="52px"
                              stateName="notes"
                              labelName="Opportunity Notes"
                              error={touched.notes && errors.notes ? true : false}
                            />
                          </SharedStyled.FlexCol>

                          {/* TODO: Change later */}
                          <SharedStyled.FlexRow width="100%" alignItems="flex-start" gap="20px"></SharedStyled.FlexRow>

                          {leadSrcData?.length &&
                            isPayPerLead(getObjectFromKey(leadSrcData, 'name', values.leadSourceName)) && (
                              <InputWithValidation
                                labelName="Lead Cost"
                                stateName="leadCost"
                                error={touched.leadCost && errors.leadCost ? true : false}
                                twoInput={true}
                              />
                            )}

                          <div style={{ width: '100%' }}>
                            <SharedStyled.Text
                              fontSize="16px"
                              fontWeight="700"
                              textAlign="left"
                              margin="8px auto -10px 0"
                            >
                              Set Appointment
                            </SharedStyled.Text>
                            {/* {hasValues(salesPersonByClient) ? (
                              <div>
                                <SharedStyled.Text margin="0 0 -10px 0" textAlign="left" color={colors.green}>
                                  Previous Sales Person: {salesPersonByClient?.salesPerson?.name}
                                </SharedStyled.Text>
                              </div>
                            ) : null} */}

                            <CustomSelect
                              labelName="Sales person assigned *"
                              stateName="salesPerson"
                              error={touched.salesPerson && errors.salesPerson ? true : false}
                              setFieldValue={setFieldValue}
                              value={values.salesPerson}
                              dropDownData={getKeysFromObjects(salesPersonDrop, 'name')}
                              setValue={() => {}}
                              innerHeight="52px"
                              margin="10px 0 0 0"
                            />

                            <SharedStyled.TwoInputDiv>
                              <SharedDateAndTime
                                value={values.needsAssessmentDate}
                                labelName={'Needs Assessment Date/Time'}
                                stateName="needsAssessmentDate"
                                setFieldValue={setFieldValue}
                                error={touched.needsAssessmentDate && errors.needsAssessmentDate ? true : false}
                              />
                            </SharedStyled.TwoInputDiv>
                          </div>

                          <SharedStyled.FlexBox
                            width="100%"
                            alignItems="center"
                            gap="20px"
                            wrap="wrap"
                            marginTop="25px"
                          >
                            <Button
                              disabled={loading}
                              onClick={() => {
                                errors.client && isLead ? notify('Contact is Required', 'error') : null
                                errors.duration
                                  ? notify('Duration is required. Please add a valid address.', 'error')
                                  : null
                              }}
                              isLoading={loading}
                              type="submit"
                              className="fit"
                            >
                              Save Opportunity
                            </Button>
                            <Button type="button" className="fit delete" onClick={onClose}>
                              Cancel
                            </Button>
                          </SharedStyled.FlexBox>
                        </SharedStyled.Content>
                      </Form>
                      {/* </SharedStyled.ContentContainer> */}
                    </Styled.NewLeadContainer>
                  </LoadScript>
                )
              }}
            </Formik>
          </>
        </SharedStyled.SettingModalContentContainer>
      </ModalContainer>
    </>
  )
}

export default AddOpportunityModal
