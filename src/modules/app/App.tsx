import { useDispatch } from 'react-redux'
import 'regenerator-runtime/runtime'
import { ThemeProvider } from 'styled-components'
import { useEffect } from 'react'
import { Navigate, useLocation, useNavigate } from 'react-router-dom'
import 'react-toastify/dist/ReactToastify.css'

import { isAuthenticated, refreshAccessToken } from '../../logic/apis/auth'
import { signinPath } from '../../logic/paths'
import { setIsLoggedIn } from '../../logic/redux/actions/auth'
import { GlobalStyle } from '../../styles/globalStyle'
import { getTheme, Themes } from '../../styles/theme'
import './App.css'
import ErrorBoundary from './errorBoundary/ErrorBoundary'
import NewRoute from './routes/NewRoute'
import ScrollToTop from './scrollToTop/ScrollToTop'
import { useAppSelector } from '../../logic/redux/reduxHook'
import { StorageKey } from '../../shared/helpers/constants'
import { getDataFromLocalStorage, isSuccess, notify } from '../../shared/helpers/util'
import { createCompanyName } from './Newsidebar/Sidebar'

export const App = () => {
  const currentTheme = { ...getTheme(Themes.BASIC), selected: Themes.BASIC }
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { currentCompany } = useAppSelector((state) => state.company)

  const planType = currentCompany?.planType

  useEffect(() => {
    if (planType) {
      localStorage.setItem(StorageKey.plan, JSON.stringify(planType))
    }
  }, [planType])

  useEffect(() => {
    const checkForLastPassIcons = () => {
      const inputFields = document.querySelectorAll('input[type="password"], input[type="text"]')

      inputFields.forEach((input) => {
        const lastPassIcon = input?.closest('div')?.querySelector('[data-lastpass-icon-root]')

        if (lastPassIcon) {
          localStorage.setItem(StorageKey.lastPassInstalled, JSON.stringify(true))
        }
      })
    }

    const observer = new MutationObserver(() => {
      checkForLastPassIcons()
    })

    observer.observe(document.body, { childList: true, subtree: true })

    return () => {
      observer.disconnect()
    }
  }, [])
  /**
   * refetch when time expires
   */
  // useEffect(() => {
  //   const refreshTokenIfNeeded = async () => {
  //     const expiresIn = localStorage.getItem('tokenExpireAt')
  //     const refereshToken: string = localStorage.getItem('refreshtoken') || ''

  //     // Ensure expiration timestamp is present
  //     if (!expiresIn) {
  //       // notify('No expiration timestamp found.', 'error')
  //       return
  //     }

  //     const expirationTimestamp = Number(expiresIn)
  //     const currentTimestamp = Date.now()
  //     const timeDifference = expirationTimestamp - currentTimestamp
  //     // console.log({ timeDifference, expirationTimestamp })
  //     // Check for 3 minutes
  //     // if (timeDifference < 15000) {
  //     if (timeDifference < 180000) {
  //       try {
  //         // notify('Token is about to expire. Refreshing...', 'info')

  //         const resp = await refreshAccessToken(refereshToken)
  //         // console.log({ resp })
  //         if (isSuccess(resp)) {
  //           // notify('Token refreshed successfully.', 'success')
  //           window.localStorage.setItem('token', JSON.stringify(resp?.data?.access_token))
  //           window.localStorage.setItem('tokenExpireAt', JSON.stringify(resp?.data?.user?.exp))
  //         } else if (resp?.data?.statusCode === 401 || resp?.status === 401) {
  //           // notify('Refresh token expired or invalid. Logging out...', 'warning')
  //           window.localStorage.clear()
  //           navigate('/')
  //         }
  //       } catch (error) {
  //         console.error('Error refreshing token:', error)
  //         // window.localStorage.clear()
  //         // navigate('/')
  //       }
  //     }
  //   }

  //   refreshTokenIfNeeded()
  //   // const intervalId = setInterval(refreshTokenIfNeeded, 30000) // Check every 1 minutes
  //   const intervalId = setInterval(refreshTokenIfNeeded, 240000) // Check every 4 minutes
  //   return () => clearInterval(intervalId)
  // }, [])

  return (
    <ThemeProvider theme={currentTheme}>
      <GlobalStyle />
      <ScrollToTop />
      <ErrorBoundary>
        <NewRoute />
      </ErrorBoundary>
    </ThemeProvider>
  )
}
