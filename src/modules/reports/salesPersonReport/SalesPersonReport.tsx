import { <PERSON>, useNavigate, useParams, useLocation } from 'react-router-dom'
import * as SharedStyled from '../../../styles/styled'
import * as Styled from './style'
import {
  extractDatesFromString,
  getCurrentWeekNumberAndYear,
  getKeysFromObjects,
  getWeeksInRange,
  isSuccess,
  joinString,
  notify,
  dayjsFormat,
  formatNumberToCommaS,
  truncateParagraph,
  formattedDateForRange,
  getDataFromLocalStorage,
  getEnumValue,
  renderClientName,
  getValueByKeyAndMatch,
} from '../../../shared/helpers/util'
import { useEffect, useState } from 'react'
import { Formik } from 'formik'
import { getCustomReport, getSalesReport } from '../../../logic/apis/report'
import { useSelector } from 'react-redux'
import * as Yup from 'yup'
import { InputWithValidation } from '../../../shared/inputWithValidation/InputWithValidation'
import CustomSelect from '../../../shared/customSelect/CustomSelect'
import { I_Position } from '../../newLead/NewLead'
import { getPosition } from '../../../logic/apis/position'
import { getCheckpoint, getPositionMembersById, getStages } from '../../../logic/apis/sales'
import { I_SalesPerson } from '../../newLead/NewLead'
import { SharedDate } from '../../../shared/date/SharedDate'
import { Table } from '../../../shared/table/Table'
import { I_Stage } from '../../opportunity/components/assessmentForm/AssessmentForm'
import { I_Checkpoint } from '../../crmSettings/CrmSettings'
import Button from '../../../shared/components/button/Button'
import { ButtonWrapper } from '../projectReport/style'
import { Nue, StageGroupEnum, StorageKey } from '../../../shared/helpers/constants'
import DropdownWithCheckboxes from '../../../shared/dropdownWithCheckboxes/DropdownWithCheckboxes'
import { SLoader } from '../../../shared/components/loader/Loader'
import { getDepartments } from '../../../logic/apis/department'

const filterSchema = Yup.object().shape({
  date: Yup.string().required('Required'),
  time: Yup.string().required('Required'),
  scheduledBy: Yup.string(),
})

// const ACTION_KEYS = [
//   'thisWeek',
//   'prevWeek',
//   'lastWeek'
// ]

const COLUMNS_1 = [
  {
    Header: ' ',
    accessor: 'label',
  },
  {
    Header: ' ',
    accessor: 'data',
  },
]

const COLUMNS_2 = [
  {
    Header: 'Client Name',
    accessor: 'clientName',
  },
  {
    Header: 'Date',
    accessor: 'date',
  },
  {
    Header: 'City',
    accessor: 'city',
  },
  {
    Header: 'Source',
    accessor: 'source',
  },
  {
    Header: 'Cost',
    accessor: 'cost',
  },
]

interface ITableData {
  label: string
  data: string
}
interface ITableData2 {
  clientName: string
  date: string
  city: string
  source: string
  cost: string
}

const SalesPersonReport = () => {
  const navigate = useNavigate()

  const [salesPersonDrop, setSalesPersonDrop] = useState<I_SalesPerson[]>([])
  const [checkpoints, setCheckpoints] = useState<I_Checkpoint[]>([])
  const [stages, setStages] = useState<I_Stage[]>([])
  const [checkpointsLowerCase, setCheckpointsLowerCase] = useState<I_Checkpoint[]>([])
  const [filterData, setFilterData] = useState({
    salesPerson: '',
    dateRange: '',
    selectedOptions: [],
  })
  const [weeksOptions, setWeeksOptions] = useState<string[]>([])
  const [tableData1, setTableData1] = useState<ITableData[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [optionLoader, setOptionLoader] = useState(false)
  const [actionToggle, setActionToggle] = useState(false)
  const [toggleHeading, setToggleHeading] = useState<{ [key: string]: boolean }>({})
  const [changeOrderSection, setChangeOrderSection] = useState<{ [key: string]: boolean }>({})
  const [officeDrop, setOfficeDrop] = useState<any[]>([])

  const location = useLocation()

  const params = new URLSearchParams(location.search)
  const paramSalesPerson = params.get('salesPerson')
  const paramDateRange = params.get('dateRange')
  const selectedParamOptions = salesPersonDrop
    .filter((item) => paramSalesPerson?.includes(item._id))
    ?.map(({ name, _id }: { name: string; _id: string }) => ({ name: name, _id: _id }))

  const [initValues, setInitialValues] = useState({
    salesPerson: '',
    dateRange: '',
    selectedOptions: selectedParamOptions || [],
  })
  const [data, setData] = useState<any>({})

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, positionDetails, currentMember } = globalSelector.company

  useEffect(() => {
    // const selectedParamOptions = salesPersonDrop
    //   .filter((item) => paramSalesPerson?.includes(item._id))
    //   ?.map(({ name, _id }: { name: string; _id: string }) => ({ name: name, _id: _id }))
    if (paramDateRange && paramSalesPerson) {
      if (
        paramDateRange !== '' &&
        paramSalesPerson !== ''
        // && salesPersonDrop.length > 0
      ) {
        setInitialValues((prev) => ({ ...prev, dateRange: paramDateRange }))
        if (salesPersonDrop.length > 0) {
          handleFilterData({ selectedOptions: selectedParamOptions || [], dateRange: paramDateRange })
        }
      }
    }
  }, [location.search])

  //For assigning salesPerson to prevent auto call on every date change
  useEffect(() => {
    // const params = new URLSearchParams(location.search)
    // const paramSalesPerson = params.get('salesPerson')
    // const selectedParamOptions = salesPersonDrop
    //   .filter((item) => paramSalesPerson?.includes(item._id))
    //   ?.map(({ name, _id }: { name: string; _id: string }) => ({ name: name, _id: _id }))
    if (salesPersonDrop.length > 0) {
      setInitialValues((prev) => ({ ...prev, selectedOptions: selectedParamOptions || [] }))
    }
  }, [salesPersonDrop])

  const handleDateSelection = (salesPersonId: string, selectedDateRange: string) => {
    const params = new URLSearchParams()
    params.append('salesPerson', salesPersonId)
    params.append('dateRange', selectedDateRange)

    const newURL = `${window.location.pathname}?${params.toString()}`
    window.history.pushState({}, '', newURL)
    // Perform any other actions needed when start and end dates are selected
  }

  useEffect(() => {
    // const now = new Date()
    // const twentyWeeksAgo = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 140)
    // const weeksObj = getWeeksInRange(twentyWeeksAgo, now)
    // const weeksOptionsArr = weeksObj.map((weekData) => `${weekData.startDate} to ${weekData.endDate}`).reverse()
    const weeksOptionsArr = formattedDateForRange(false)
    setWeeksOptions(weeksOptionsArr)
  }, [])

  // useEffect(() => {
  //   if (stages.length) {
  //     handleFilterData(initValues)
  //   }
  // }, [stages])

  useEffect(() => {
    if (currentMember?._id) {
      fetchCheckpoints()
    }
  }, [currentMember])

  const fetchCheckpoints = async () => {
    try {
      const response = await getCheckpoint(false, StageGroupEnum.Sales)
      if (response?.statusCode === 200 || response?.status === 200) {
        const checkpointsArr: I_Checkpoint[] = response.data.data.checkpoint
        // const checkPointsObj = checkpointsArr.reduce((prev, cur) => {
        //   return { ...prev, [cur.name]: cur }
        // }, {})

        const localCheckptsArr = new Array(checkpointsArr.length)
        checkpointsArr.forEach((checkpnt) => {
          localCheckptsArr[checkpnt.sequence - 1] = checkpnt
        })
        let checkpointsArrLowerCase: I_Checkpoint[] = response.data.data.checkpoint.map((item: any) => {
          // Remove spaces and convert name to lowercase
          const updatedName = item.name.replace(/\s/g, '').toLowerCase()

          // Return the updated object
          return {
            ...item,
            name: updatedName,
          }
        })
        setCheckpoints(localCheckptsArr)
        setCheckpointsLowerCase(checkpointsArrLowerCase)
      } else {
        throw new Error(response?.data.message ?? 'Something went wrong!')
      }
    } catch (err) {
      console.log('CHECKPOINTS Fetch failed', err)
    }
  }

  const sortObjectKeysBySequence = (response1: any, response2: any) => {
    const keysArray = Object.keys(response2)

    // Sort keysArray based on the sequence order from response1
    keysArray.sort((a, b) => {
      const sequenceA = response1?.find((item: any) => item.name.toLowerCase() === a.toLowerCase())?.sequence || 0
      const sequenceB = response1?.find((item: any) => item.name.toLowerCase() === b.toLowerCase())?.sequence || 0
      return sequenceA - sequenceB
    })

    // Create a new object with sorted keys
    const sortedResponse2: any = {}
    keysArray?.forEach((key) => {
      sortedResponse2[key] = response2[key.toLocaleLowerCase()]
    })
    const sortedData: any = {}
    keysArray.forEach((key) => {
      if (response2[key]) {
        sortedData[key] = response2[key]
        const matchingResponse1 = response1.find((item: any) => item.name.toLowerCase() === key.toLowerCase())
        if (matchingResponse1) {
          // Add symbol from response1 to response2
          sortedData[key].symbol = matchingResponse1.symbol
        }
      }
    })

    return sortedData
  }
  const getPositionMembers = async (startDate: string, endDate: string, positionId: string) => {
    try {
      // const params = new URLSearchParams(location.search)
      // const paramDateRange = params.get('dateRange')
      // console.log({ paramDateRange })
      const response = await getPositionMembersById({ positionId, startDate, endDate }, false)
      if (isSuccess(response)) {
        // return response?.data?.data?.memberData
        if (positionDetails?.symbol === 'SalesPerson' || positionDetails?.symbol === 'RRTech') {
          const filterResponse = response?.data?.data?.memberData?.filter(
            (member: any) => member?._id === currentMember?._id
          )

          setSalesPersonDrop(filterResponse)
        } else {
          setSalesPersonDrop(response?.data?.data?.memberData)
        }
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    } finally {
      setOptionLoader(false)
    }
  }

  // const getPositionMembersForSalesManager = async (positionId: string) => {
  //   try {
  //     const response = await getPositionMembersById({ companyId: currentCompany._id, positionId }, false)
  //     if (isSuccess(response)) {
  //       // setSalesManagerDrop(response?.data?.data?.memberData)
  //       return response?.data?.data?.memberData
  //     } else notify(response?.data?.message, 'error')
  //   } catch (err) {
  //     console.log('GET POSITION MEMBERS FAILED', err)
  //   }
  // }

  const getPositions = async (startDate: string, endDate: string) => {
    try {
      const response = await getPosition({ deleted: false }, false)
      if (isSuccess(response)) {
        const positions: I_Position[] = response?.data?.data?.position || []
        let salesPersonIdx: string[] = []
        let salesManagerIdx = -1

        positions.forEach((position: any, idx) => {
          if (position.symbol === 'SalesPerson') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'GeneralManager') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'SalesManager') {
            salesPersonIdx.push(position?._id)
            return
          }
          if (position.symbol === 'RRTech') {
            salesPersonIdx.push(position?._id)
            return
          }
        })
        await getPositionMembers(startDate, endDate, salesPersonIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      setOptionLoader(false)
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  useEffect(() => {
    getStagesData()
    getPositionsOffice()
  }, [])

  const getPositionsOffice = async () => {
    try {
      const response = await getDepartments({ deleted: false }, false)
      if (isSuccess(response)) {
        console.log({ response })
        const departments: I_Position[] = response?.data?.data?.department

        let officePersonIdx: string[] = []
        departments.forEach((department: any, idx) => {
          if (department.name === 'Office') {
            officePersonIdx.push(department?._id)
            return
          }
        })
        getPositionForOfficeMembers(officePersonIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  const getPositionForOfficeMembers = async (departmentId: string) => {
    try {
      const response = await getPositionMembersById({ departmentId }, false)
      if (isSuccess(response)) {
        setOfficeDrop(response?.data?.data?.memberData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false)
      // const stagesRes = await getStages({ companyId: currentCompany._id }, false, operationsFlag)
      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const formatStages: I_Stage[] = new Array(stages.length)
        stages.forEach((stage: I_Stage) => {
          formatStages[stage.sequence - 1] = stage
        })
        setStages(stages)
        // setStages(formatStages)
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }
  const handleFilterData = async (values: typeof initValues) => {
    try {
      setIsLoading(true)
      //filter salesperson _id for url param from api response
      const salesPersonId: string = values.selectedOptions
        ?.filter((v) => salesPersonDrop?.map((v) => v._id)?.includes(v._id))
        ?.map((v) => v._id)
        .join(',')

      handleDateSelection(salesPersonId, values.dateRange)
      // salesPersonDrop?.forEach((sp) => {
      //   if (sp.name === values.salesPerson) salesPersonId = sp._id
      // })
      const [startDate, endDate] = extractDatesFromString(values.dateRange)

      // Parse the input date string into a Date object
      const startDateParsedDate = new Date(startDate)

      // Extract the year, month, and day from the Date object
      const startYear = startDateParsedDate.getFullYear()
      const startMonth = String(startDateParsedDate.getMonth() + 1).padStart(2, '0') // Months are zero-based, so add 1 and pad with 0 if needed
      const startDay = String(startDateParsedDate.getDate()).padStart(2, '0')

      // Format the date as "yyyy-mm-dd"
      const startFormattedDate = `${startYear}-${startMonth}-${startDay}`
      // Parse the input date string into a Date object
      const endDateParsedDate = new Date(endDate)

      // Extract the year, month, and day from the Date object
      const endYear = endDateParsedDate.getFullYear()
      const endMonth = String(endDateParsedDate.getMonth() + 1).padStart(2, '0') // Months are zero-based, so add 1 and pad with 0 if needed
      const endDay = String(endDateParsedDate.getDate()).padStart(2, '0')

      // Format the date as "yyyy-mm-dd"
      const endFormattedDate = `${endYear}-${endMonth}-${endDay}`

      const response = await getSalesReport({
        salesPersonId,
        endDate: endFormattedDate,
        startDate: startFormattedDate,
      })
      setFilterData(values)
      if (isSuccess(response)) {
        setData(response?.data)
        const data = response?.data
        const stagesTd1: ITableData[] = []
        let moreTd1 = [
          { label: 'Actions Completed:', data: data.actions.thisWeekNum },
          { label: 'Actions Completed:', data: data.actions.thisWeekOANum },
        ]
        stagesTd1.push(...moreTd1)
        stagesTd1.push(
          ...checkpoints?.map((chk) => {
            return { label: chk.name, data: data[joinString(chk.name)]?.num ?? 0 }
          })
        )
        stagesTd1.push({ label: 'lost', data: data.checkpoints.Lost.num })
        setTableData1(stagesTd1)
      } else throw new Error(response?.data?.message)
    } catch (err) {
      console.log('handleFilterData err', err)
    } finally {
      setIsLoading(false)
    }
  }

  const toggleCount = (type: string) => {
    setToggleHeading((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }
  return (
    <>
      <SharedStyled.Content
        maxWidth="1270px"
        width="100%"
        disableBoxShadow={true}
        noPadding={true}
        alignItems="center"
        overflow=""
        gap="10px"
        fontFamily={Nue.regular}
      >
        <SharedStyled.FlexBox flexDirection="column" width="100%">
          <SharedStyled.SectionTitle textAlign="center">
            Sales Person Report for{' '}
            {getCurrentWeekNumberAndYear(
              filterData?.dateRange?.split('to')[0]?.trim(),
              filterData?.dateRange?.split('to')[1]?.trim()
            )}
          </SharedStyled.SectionTitle>
          <SharedStyled.HorizontalDivider />
        </SharedStyled.FlexBox>
        <SharedStyled.FlexBox flexDirection="column" justifyContent="center" alignItems="center">
          <div>
            <Styled.SubHeading>{filterData.dateRange ? `${filterData.dateRange}` : ''}</Styled.SubHeading>
          </div>
          {/* <div>
            {initValues?.selectedOptions?.map((v, index: number) => (
              <SharedStyled.Text key={index} fontSize="20px" textAlign="center" fontWeight="500" margin="0">
                {v?.name || ''},{' '}
              </SharedStyled.Text>
            ))}
          </div> */}
        </SharedStyled.FlexBox>
        <div style={{ width: '100%', padding: '0 30px' }}>
          <Formik
            initialValues={initValues}
            onSubmit={handleFilterData}
            validationSchema={filterSchema}
            validateOnChange={true}
            validateOnBlur={false}
            enableReinitialize={true}
          >
            {(formik) => {
              const { handleSubmit, errors, values, setFieldValue, touched, resetForm } = formik
              useEffect(() => {
                if (currentMember?._id && positionDetails?.symbol && values.dateRange !== '') {
                  const [startDate, endDate] = extractDatesFromString(values.dateRange)
                  setInitialValues((prev) => ({ ...prev, dateRange: values.dateRange }))
                  setOptionLoader(true)
                  getPositions(startDate, endDate)
                }
              }, [positionDetails, currentMember, values.dateRange])

              return (
                <SharedStyled.FlexBox
                  width="100%"
                  gap="10px"
                  alignItems="center"
                  column="column"
                  justifyContent="center"
                >
                  {/* <CustomSelect
                    dropDownData={getKeysFromObjects(salesPersonDrop, 'name')}
                    setFieldValue={setFieldValue}
                    stateName="salesPerson"
                    labelName="Sales Person"
                    value={values.salesPerson}
                    margin="8px 0 0 0"
                    setValue={() => {}}
                    error={touched.salesPerson && errors.salesPerson ? true : false}
                  /> */}

                  <div>
                    <CustomSelect
                      labelName="Date Range"
                      error={false}
                      value={values.dateRange}
                      dropDownData={weeksOptions}
                      setValue={() => {}}
                      setFieldValue={setFieldValue}
                      margin="10px 0 0 0"
                      stateName="dateRange"
                    />
                  </div>
                  {optionLoader ? (
                    <div>
                      <SLoader margin="10px 0 0 0" height={52} width={200} />
                    </div>
                  ) : (
                    <DropdownWithCheckboxes
                      options={salesPersonDrop?.map((v) => ({ name: v.name, _id: v._id })) || []}
                      formik={formik}
                      // selectedOptions={values?.selectedSalesPerson}
                    />
                  )}
                  {/* <SharedDate
                      value={values.startDate}
                      labelName="From"
                      stateName="startDate"
                      error={touched.startDate && errors.startDate ? true : false}
                      setFieldValue={setFieldValue}
                    />
                    <SharedDate
                      value={values.endDate}
                      labelName="To"
                      stateName="endDate"
                      error={touched.endDate && errors.endDate ? true : false}
                      min={values.startDate}
                      setFieldValue={setFieldValue}
                    /> */}
                  <Styled.KPIButtonContainer>
                    <Button
                      width="maxx-content"
                      onClick={() => {
                        handleFilterData(values)
                        // resetForm()
                      }}
                      disabled={values.selectedOptions?.length === 0 || values.dateRange === ''}
                      isLoading={isLoading}
                    >
                      Run Report
                    </Button>
                  </Styled.KPIButtonContainer>
                </SharedStyled.FlexBox>
              )
            }}
          </Formik>
        </div>
      </SharedStyled.Content>
      {Object.keys(data).length ? (
        <Styled.ReportMainContainer>
          <SharedStyled.FlexBox justifyContent="space-around">
            <Styled.ReportWrapper>
              <div style={{ margin: '10px 0' }}>
                <SharedStyled.Text fontSize="18px">
                  Total Sales: ${formatNumberToCommaS(Number(data?.checkpoints?.Sale?.SaleVol))}
                </SharedStyled.Text>
              </div>
              <Styled.ReportLgSubHeading>
                {`New Opportunity > Needs Assessment`}:{' '}
                {data?.conversion?.['New Opportunity > Needs Assessment']?.toFixed(2)}%
              </Styled.ReportLgSubHeading>
              <Styled.ReportLgSubHeading>
                {'Needs Assessment > Presentation'}: {data?.conversion?.['Needs Assessment > Presentation']?.toFixed(2)}
                %
              </Styled.ReportLgSubHeading>
              <Styled.ReportLgSubHeading>
                {'Presentation > Sale'}: {data?.conversion?.['Presentation > Sale']?.toFixed(2)}%
              </Styled.ReportLgSubHeading>
              <Styled.ReportLgSubHeading>
                {'New Opportunity > Sales'}: {data?.conversion?.['New Opportunity > Sales']?.toFixed(2)}%
              </Styled.ReportLgSubHeading>
            </Styled.ReportWrapper>

            <Styled.ReportWrapper>
              <Styled.ReportLgSubHeading>
                Profit Score:&nbsp;{formatNumberToCommaS(data?.checkpoints?.Sale?.SaleScore)}{' '}
              </Styled.ReportLgSubHeading>
              <Styled.ReportLgSubHeading>
                Discounts:&nbsp;${formatNumberToCommaS(data?.checkpoints?.Sale?.SaletDis)}{' '}
                {`(${formatNumberToCommaS(data?.checkpoints?.Sale?.SaleDiscountPercent)}%)`}
              </Styled.ReportLgSubHeading>
              <Styled.ReportLgSubHeading>
                Fin. Fees:&nbsp;${formatNumberToCommaS(data?.checkpoints?.Sale?.SaletFin)}
              </Styled.ReportLgSubHeading>
              {/* <Styled.ReportLgSubHeading>
                Total Volume:&nbsp;${formatNumberToCommaS(Number(data?.checkpoints?.Sale?.SaleVol))}
              </Styled.ReportLgSubHeading> */}

              <Styled.ReportLgSubHeading>
                Opportunities w/ Overdue Actions:&nbsp;
                {data?.numOverdue}
              </Styled.ReportLgSubHeading>
              <Styled.ReportLgSubHeading>
                Opportunities w/ No Action:&nbsp;
                {data?.numNoAction}
              </Styled.ReportLgSubHeading>
              {data?.totalHours ? (
                <Styled.ReportLgSubHeading>
                  Hours Worked:&nbsp;
                  {data?.totalHours?.toFixed(2)}
                </Styled.ReportLgSubHeading>
              ) : null}
            </Styled.ReportWrapper>
          </SharedStyled.FlexBox>

          <Styled.TableContainerMain>
            <thead>
              <tr>
                <th scope="col"></th>
                <th scope="col">This week</th>
                <th scope="col">Last week</th>
                <th scope="col">Prev week</th>
                {/* <th scope="col">This month</th> */}
              </tr>
            </thead>
            <tbody>
              <tr>
                <th>Actions Completed:</th>
                <td style={{ cursor: 'pointer' }} onClick={() => setActionToggle((prevToggle) => !prevToggle)}>
                  {data?.actions.thisWeekNum}
                </td>
                <td>{data?.actions.lastWeekNum}</td>
                <td>{data?.actions.prevWeekNum}</td>
                {/* <td></td> */}
              </tr>
              <tr>
                <th>Opps w/ Actions:</th>
                <td style={{ cursor: 'pointer' }} onClick={() => setActionToggle((prevToggle) => !prevToggle)}>
                  {data?.actions.thisWeekOANum}
                </td>
                <td>{data?.actions.lastWeekOANum}</td>
                <td>{data?.actions.prevWeekOANum}</td>
                {/* <td></td> */}
              </tr>
              {Object.keys(data?.checkpoints).map((key: any) => {
                const checkpoint = data?.checkpoints[key]
                return (
                  <tr>
                    <th>{key}:</th>
                    <td>
                      {checkpoint?.num}{' '}
                      <span style={{ color: '#2fac2f' }}>
                        {checkpoint?.selfGenCount > 0 && `(${checkpoint?.selfGenCount})`}
                      </span>
                    </td>
                    <td>
                      {checkpoint?.lastWeek}{' '}
                      <span style={{ color: '#2fac2f' }}>
                        {checkpoint?.lasWeekSelfGenCount > 0 && `(${checkpoint?.lasWeekSelfGenCount})`}
                      </span>
                    </td>
                    <td>
                      {checkpoint?.prevWeek}{' '}
                      <span style={{ color: '#2fac2f' }}>
                        {checkpoint?.prevWeekSelfGenCount > 0 && `(${checkpoint?.prevWeekSelfGenCount})`}
                      </span>
                    </td>
                    {/* <td></td> */}
                  </tr>
                )
              })}
            </tbody>
          </Styled.TableContainerMain>

          {actionToggle && (
            <>
              <Styled.ReportHeading fontSize="32px" textAlign="center">
                Actions
              </Styled.ReportHeading>
              <Styled.ReportGridBox>
                <div>
                  <Styled.ReportHeading>Actions Completed: {data?.actions?.thisWeekNum}</Styled.ReportHeading>
                  <Styled.TableContainer>
                    <Styled.TableHeading column={'repeat(4,1fr)'}>
                      <Styled.TableTitle>Type</Styled.TableTitle>
                      <Styled.TableTitle>Action Taken</Styled.TableTitle>
                      <Styled.TableTitle>Done</Styled.TableTitle>
                      <Styled.TableTitle>Opportunity</Styled.TableTitle>
                    </Styled.TableHeading>
                    {data?.actions?.thisWeek
                      ?.sort((a: any, b: any) => new Date(a?.completedAt) - new Date(b?.completedAt))
                      ?.map((val: any) => {
                        return (
                          <>
                            <tr>
                              <Styled.TableContent column={'repeat(4,1fr)'}>
                                <Styled.CrewReportTableContentLabel>{val?.type}</Styled.CrewReportTableContentLabel>
                                <Styled.CrewReportTableContentLabel>{val?.body}</Styled.CrewReportTableContentLabel>
                                <Styled.CrewReportTableContentLabel>
                                  {dayjsFormat(val?.completedAt, 'M/D/YY')}
                                </Styled.CrewReportTableContentLabel>
                                <Styled.CrewReportTableContentLabel>{val?.name}</Styled.CrewReportTableContentLabel>
                              </Styled.TableContent>
                            </tr>
                          </>
                        )
                      })}
                  </Styled.TableContainer>
                </div>

                <div>
                  <Styled.ReportHeading>Opps w/ Actions: {data?.actions?.thisWeekOANum}</Styled.ReportHeading>
                  <Styled.TableContainer>
                    <Styled.TableHeading>
                      <Styled.TableTitle>Client name</Styled.TableTitle>
                      <Styled.TableTitle>Date</Styled.TableTitle>
                      <Styled.TableTitle>City</Styled.TableTitle>
                      <Styled.TableTitle>Source</Styled.TableTitle>
                    </Styled.TableHeading>
                    {data?.actions?.thisWeekOA
                      ?.sort((a: any, b: any) => {
                        const dateA = a?.oppDate ? new Date(a?.oppDate)?.toISOString()?.split('T')[0] : ''
                        const dateB = b?.oppDate ? new Date(b?.oppDate)?.toISOString()?.split('T')[0] : ''

                        if (dateA < dateB) return -1
                        if (dateA > dateB) return 1

                        // If the dates are the same or missing, sort by last name
                        return (a?.clientId?.lastName || '').localeCompare(b?.clientId?.lastName || '')
                      })
                      ?.map((val: any) => {
                        const matchedObject: any = stages.find((item) => item._id === val?.stage)
                        return (
                          <>
                            <tr>
                              <Styled.TableContent
                                as={Link}
                                to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${val?._id}`}
                                key={val?._id}
                                pointer="pointer"
                                selfGen={val?.selfGen}
                                // onClick={() => getpageById(val?.stage, val?._id)}
                              >
                                <Styled.CrewReportTableContentLabel>
                                  {renderClientName(
                                    val?.clientId?.isBusiness,
                                    val?.clientId?.firstName,
                                    val?.clientId?.lastName
                                  )}
                                </Styled.CrewReportTableContentLabel>
                                <Styled.CrewReportTableContentLabel>
                                  {dayjsFormat(val?.oppDate, 'M/D/YY')}
                                </Styled.CrewReportTableContentLabel>
                                <Styled.CrewReportTableContentLabel>{val?.city}</Styled.CrewReportTableContentLabel>
                                <Styled.CrewReportTableContentLabel>
                                  {val?.leadSource}
                                </Styled.CrewReportTableContentLabel>
                              </Styled.TableContent>
                            </tr>
                          </>
                        )
                      })}
                  </Styled.TableContainer>
                </div>
              </Styled.ReportGridBox>
            </>
          )}

          {Object.keys(sortObjectKeysBySequence(checkpointsLowerCase, data?.checkpoints)).map((key: string) => {
            const checkpoint = data?.checkpoints[key]
            const types = checkpoint?.types
            return checkpoint.symbol === 'saleDate' ? (
              <>
                <Styled.ReportWrapper>
                  <Styled.ReportHeading className="title" fontSize="21px" textAlign="center">
                    Total {key}: {checkpoint?.num || 0}{' '}
                    <span style={{ color: '#2fac2f' }}>
                      {checkpoint?.selfGenCount > 0 && `(${checkpoint?.selfGenCount})`}
                    </span>
                    <br />
                  </Styled.ReportHeading>
                  <Styled.ReportHeading fontSize={'16px'} textAlign="center" className="margin">
                    Total Volume: ${formatNumberToCommaS(checkpoint?.SaleVol)}
                  </Styled.ReportHeading>

                  <Styled.ReportGridBox>
                    {types?.map((type: any, index: number) => {
                      return (
                        <>
                          <div style={{ width: '100%' }}>
                            <Styled.ReportHeading fontSize={'16px'} className="margin">
                              <span
                                onClick={() => {
                                  toggleCount(`${type?.name}-${key}`)
                                }}
                                className="cursor"
                              >
                                {!toggleHeading[`${type?.name}-${key}`] ? <>&#9654;</> : <>&#9660;</>}
                                {type?.name} {key}: {type?.num}{' '}
                                <span style={{ color: '#2fac2f' }}>
                                  {type?.selfGenCount > 0 && `(${type?.selfGenCount})`}
                                </span>{' '}
                                | Total Volume: ${formatNumberToCommaS(type?.vol)}
                              </span>
                              <br />
                            </Styled.ReportHeading>
                            {toggleHeading[`${type?.name}-${key}`] ? (
                              <Styled.TableContainer>
                                <Styled.TableHeading column={`2fr repeat(3, 1fr)`}>
                                  <Styled.TableTitle>Client Name</Styled.TableTitle>
                                  <Styled.TableTitle>Date</Styled.TableTitle>
                                  <Styled.TableTitle>CSR</Styled.TableTitle>
                                  {/* <Styled.TableTitle>Source</Styled.TableTitle> */}
                                  <Styled.TableTitle className="right-align">Ttl Volume</Styled.TableTitle>
                                </Styled.TableHeading>
                                {type?.opps
                                  ?.sort((a: any, b: any) => {
                                    const dateA = a?.saleDate ? new Date(a?.saleDate)?.toISOString()?.split('T')[0] : ''
                                    const dateB = b?.saleDate ? new Date(b?.saleDate)?.toISOString()?.split('T')[0] : ''

                                    if (dateA < dateB) return -1
                                    if (dateA > dateB) return 1

                                    // If the dates are the same or missing, sort by last name
                                    return (a?.clientId?.lastName || '').localeCompare(b?.clientId?.lastName || '')
                                  })
                                  ?.map((opp: any) => {
                                    const matchedObject: any = stages.find((item) => item._id === opp?.stage)
                                    return (
                                      <>
                                        <tr>
                                          <Styled.TableContent
                                            // as={Link}
                                            // to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${opp?._id}`}
                                            key={opp?._id}
                                            pointer="pointer"
                                            column={`2fr repeat(3, 1fr)`}
                                            selfGen={opp?.selfGen}
                                            className={
                                              opp?.changeOrders?.filter(
                                                (itm: any) => itm?.signedBySales && !itm?.deleted
                                              )?.length
                                                ? 'change-orders'
                                                : ''
                                            }
                                            isOpen={changeOrderSection[opp?._id]}
                                            // onClick={() => getpageById(opp?.stage, opp?._id)}

                                            onClick={() => {
                                              setChangeOrderSection((prev) => ({
                                                ...prev,
                                                [opp?._id]: !prev[opp?._id],
                                              }))
                                            }}
                                          >
                                            <Link
                                              to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${opp?._id}`}
                                            >
                                              <Styled.CrewReportTableContentLabel>
                                                {renderClientName(
                                                  opp?.clientId?.isBusiness,
                                                  opp?.clientId?.firstName,
                                                  opp?.clientId?.lastName
                                                )}
                                              </Styled.CrewReportTableContentLabel>
                                            </Link>
                                            <Styled.CrewReportTableContentLabel>
                                              {dayjsFormat(opp?.saleDate, 'M/D/YY')}
                                            </Styled.CrewReportTableContentLabel>
                                            <Styled.CrewReportTableContentLabel>
                                              {getValueByKeyAndMatch('name', opp?.csrId, '_id', officeDrop) || '--'}
                                            </Styled.CrewReportTableContentLabel>
                                            {/* <Styled.CrewReportTableContentLabel>
                                              {opp?.leadSource}
                                            </Styled.CrewReportTableContentLabel> */}
                                            <Styled.CrewReportTableContentLabel className="right-align">
                                              ${formatNumberToCommaS(opp?.soldValue)}
                                            </Styled.CrewReportTableContentLabel>
                                          </Styled.TableContent>
                                        </tr>

                                        {changeOrderSection[opp?._id] ? (
                                          <>
                                            {opp?.changeOrders?.map((itm: any) => (
                                              <>
                                                {itm?.signedBySales && !itm?.deleted ? (
                                                  <tr>
                                                    <Styled.TableContent
                                                      // as={Link}
                                                      // to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${opp?._id}`}
                                                      key={opp?._id}
                                                      pointer="pointer"
                                                      column={`2fr repeat(4, 1fr)`}
                                                      selfGen={opp?.selfGen}
                                                      className={'nested'}
                                                      // onClick={() => getpageById(opp?.stage, opp?._id)}
                                                    >
                                                      <Styled.CrewReportTableContentLabel
                                                        as={Link}
                                                        to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${
                                                          opp?._id
                                                        }`}
                                                      >
                                                        {renderClientName(
                                                          opp?.clientId?.isBusiness,
                                                          opp?.clientId?.firstName,
                                                          opp?.clientId?.lastName
                                                        )}
                                                      </Styled.CrewReportTableContentLabel>
                                                      <Styled.CrewReportTableContentLabel>
                                                        {itm?.date ? dayjsFormat(itm?.date, 'M/D/YY') : '--'}
                                                      </Styled.CrewReportTableContentLabel>
                                                      <Styled.CrewReportTableContentLabel>
                                                        {opp?.city}
                                                      </Styled.CrewReportTableContentLabel>
                                                      <Styled.CrewReportTableContentLabel>
                                                        Modification
                                                      </Styled.CrewReportTableContentLabel>
                                                      <Styled.CrewReportTableContentLabel className="right-align">
                                                        ${formatNumberToCommaS(itm?.jobCost)}
                                                      </Styled.CrewReportTableContentLabel>
                                                    </Styled.TableContent>
                                                  </tr>
                                                ) : null}
                                              </>
                                            ))}
                                          </>
                                        ) : null}
                                      </>
                                    )
                                  })}
                              </Styled.TableContainer>
                            ) : null}
                          </div>
                        </>
                      )
                    })}
                  </Styled.ReportGridBox>
                </Styled.ReportWrapper>
              </>
            ) : checkpoint.symbol === 'oppDate' ? (
              <>
                <Styled.ReportWrapper>
                  <Styled.ReportHeading className="title" fontSize="21px" textAlign="center">
                    Total {key}: {checkpoint?.num || 0}{' '}
                    <span style={{ color: '#2fac2f' }}>
                      {checkpoint?.selfGenCount > 0 && `(${checkpoint?.selfGenCount})`}
                    </span>
                  </Styled.ReportHeading>
                  <Styled.ReportGridBox>
                    {types
                      ?.filter((v: any) => v.name !== 'All')
                      ?.map((type: any, index: number) => {
                        return (
                          <>
                            <div style={{ width: '100%' }}>
                              <Styled.ReportHeading fontSize={'16px'} className="margin">
                                <span
                                  onClick={() => {
                                    toggleCount(`${type?.name}-${key}`)
                                  }}
                                  className="cursor"
                                >
                                  {!toggleHeading[`${type?.name}-${key}`] ? <>&#9654;</> : <>&#9660;</>}
                                  {type?.name} {key}: {type?.num || 0}{' '}
                                  <span style={{ color: '#2fac2f' }}>
                                    {type?.selfGenCount > 0 && `(${type?.selfGenCount})`}
                                  </span>
                                </span>
                              </Styled.ReportHeading>

                              {toggleHeading[`${type?.name}-${key}`] ? (
                                <>
                                  {/* <>
                                    {checkpoint['leadSources'][type?.name].length > 0 &&
                                      checkpoint['leadSources'][type?.name]?.map((v: any) => (
                                        <Styled.TableContainer>
                                          <Styled.TableContent column={`2fr repeat(2, 1fr)`}>
                                            <Styled.CrewReportTableContentLabel>
                                              {v?.id}
                                            </Styled.CrewReportTableContentLabel>
                                            <Styled.CrewReportTableContentLabel>
                                              {v?.num || '--'}{' '}
                                              <span style={{ color: '#2fac2f' }}>
                                                {v?.selfGenCount > 0 && `(${v?.selfGenCount})`}
                                              </span>
                                            </Styled.CrewReportTableContentLabel>
                                            <Styled.CrewReportTableContentLabel>
                                              ${formatNumberToCommaS(v?.cost) || '--'}
                                            </Styled.CrewReportTableContentLabel>
                                          </Styled.TableContent>
                                        </Styled.TableContainer>
                                      ))}
                                  </> */}

                                  <Styled.TableContainer>
                                    <Styled.TableHeading column={`2fr repeat(4, 1fr)`}>
                                      <Styled.TableTitle>Client Name</Styled.TableTitle>
                                      <Styled.TableTitle>Date</Styled.TableTitle>
                                      <Styled.TableTitle>City</Styled.TableTitle>
                                      <Styled.TableTitle>Source</Styled.TableTitle>
                                      <Styled.TableTitle className="right-align">Cost</Styled.TableTitle>
                                    </Styled.TableHeading>
                                    {type?.opps
                                      ?.sort((a: any, b: any) => {
                                        const dateA = a?.oppDate
                                          ? new Date(a?.oppDate)?.toISOString()?.split('T')[0]
                                          : ''
                                        const dateB = b?.oppDate
                                          ? new Date(b?.oppDate)?.toISOString()?.split('T')[0]
                                          : ''

                                        if (dateA < dateB) return -1
                                        if (dateA > dateB) return 1

                                        // If the dates are the same or missing, sort by last name
                                        return (a?.clientId?.lastName || '').localeCompare(b?.clientId?.lastName || '')
                                      })
                                      ?.map((opp: any) => {
                                        const matchedObject: any = stages.find((item) => item._id === opp?.stage)
                                        return (
                                          <>
                                            <tr>
                                              <Styled.TableContent
                                                as={Link}
                                                to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${
                                                  opp?._id
                                                }`}
                                                key={opp?._id}
                                                column={`2fr repeat(4, 1fr)`}
                                                selfGen={opp?.selfGen}
                                                pointer="pointer"
                                                // onClick={() => getpageById(opp?.stage, opp?._id)}
                                              >
                                                <Styled.CrewReportTableContentLabel>
                                                  {renderClientName(
                                                    opp?.clientId?.isBusiness,
                                                    opp?.clientId?.firstName,
                                                    opp?.clientId?.lastName
                                                  )}
                                                </Styled.CrewReportTableContentLabel>
                                                <Styled.CrewReportTableContentLabel>
                                                  {dayjsFormat(opp?.oppDate, 'M/D/YY')}
                                                </Styled.CrewReportTableContentLabel>
                                                <Styled.CrewReportTableContentLabel>
                                                  {opp?.city}
                                                </Styled.CrewReportTableContentLabel>
                                                <Styled.CrewReportTableContentLabel>
                                                  {opp?.leadSource}
                                                </Styled.CrewReportTableContentLabel>
                                                <Styled.CrewReportTableContentLabel className="right-align">
                                                  ${formatNumberToCommaS(opp?.leadCost)}
                                                </Styled.CrewReportTableContentLabel>
                                              </Styled.TableContent>
                                            </tr>
                                          </>
                                        )
                                      })}
                                  </Styled.TableContainer>
                                </>
                              ) : null}
                            </div>
                          </>
                        )
                      })}
                  </Styled.ReportGridBox>
                </Styled.ReportWrapper>
              </>
            ) : (
              key !== 'Lost' && (
                <Styled.ReportWrapper>
                  <Styled.ReportHeading className="title" fontSize="21px" textAlign="center">
                    Total {key}: {checkpoint?.num || 0}{' '}
                    <span style={{ color: '#2fac2f' }}>
                      {checkpoint?.selfGenCount > 0 && `(${checkpoint?.selfGenCount})`}
                    </span>
                  </Styled.ReportHeading>
                  <Styled.ReportGridBox>
                    {types?.map((type: any, index: number) => {
                      return (
                        <>
                          <div style={{ width: '100%' }}>
                            <Styled.ReportHeading fontSize={'16px'} className="margin">
                              <span
                                onClick={() => {
                                  toggleCount(`${type?.name}-${key}`)
                                }}
                                className="cursor"
                              >
                                {!toggleHeading[`${type?.name}-${key}`] ? <>&#9654;</> : <>&#9660;</>}
                                {type?.name} {key}: {type?.num || 0}{' '}
                                <span style={{ color: '#2fac2f' }}>
                                  {type?.selfGenCount > 0 && `(${type?.selfGenCount})`}
                                </span>
                              </span>
                            </Styled.ReportHeading>
                            {toggleHeading[`${type?.name}-${key}`] ? (
                              <Styled.TableContainer>
                                <Styled.TableHeading>
                                  <Styled.TableTitle>Client Name</Styled.TableTitle>
                                  <Styled.TableTitle>Date</Styled.TableTitle>
                                  <Styled.TableTitle>City</Styled.TableTitle>
                                  <Styled.TableTitle>Source</Styled.TableTitle>
                                  {/* <Styled.TableTitle>Cost</Styled.TableTitle> */}
                                </Styled.TableHeading>
                                {type?.opps
                                  ?.sort((a: any, b: any) => {
                                    const dateA = a[checkpoint?.symbol]
                                      ? new Date(a[checkpoint?.symbol]).toISOString().split('T')[0]
                                      : ''
                                    const dateB = b[checkpoint?.symbol]
                                      ? new Date(b[checkpoint?.symbol]).toISOString().split('T')[0]
                                      : ''

                                    if (dateA < dateB) return -1
                                    if (dateA > dateB) return 1

                                    // If the dates are the same or missing, sort by last name
                                    return (a?.clientId?.lastName || '')?.localeCompare(b?.clientId?.lastName || '')
                                  })
                                  ?.map((opp: any) => {
                                    const matchedObject: any = stages.find((item) => item._id === opp?.stage)
                                    return (
                                      <>
                                        <tr>
                                          <Styled.TableContent
                                            as={Link}
                                            to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${opp?._id}`}
                                            key={opp?._id}
                                            selfGen={opp?.selfGen}
                                            pointer="pointer"
                                            // onClick={() => getpageById(opp?.stage, opp?._id)}
                                          >
                                            <Styled.CrewReportTableContentLabel>
                                              {renderClientName(
                                                opp?.clientId?.isBusiness,
                                                opp?.clientId?.firstName,
                                                opp?.clientId?.lastName
                                              )}
                                            </Styled.CrewReportTableContentLabel>
                                            <Styled.CrewReportTableContentLabel>
                                              {dayjsFormat(opp[checkpoint.symbol], 'M/D/YY')}
                                            </Styled.CrewReportTableContentLabel>
                                            <Styled.CrewReportTableContentLabel>
                                              {opp?.city}
                                            </Styled.CrewReportTableContentLabel>
                                            <Styled.CrewReportTableContentLabel>
                                              {opp?.leadSource}
                                            </Styled.CrewReportTableContentLabel>
                                            {/* <Styled.CrewReportTableContentLabel>
                                    {opp?.leadCost}
                                  </Styled.CrewReportTableContentLabel> */}
                                          </Styled.TableContent>
                                        </tr>
                                      </>
                                    )
                                  })}
                              </Styled.TableContainer>
                            ) : null}
                          </div>
                        </>
                      )
                    })}
                  </Styled.ReportGridBox>
                </Styled.ReportWrapper>
              )
            )
          })}

          {data?.checkpoints['Lost'] && (
            <>
              <Styled.ReportWrapper>
                <Styled.ReportHeading fontSize="21px" className="title" textAlign="center">
                  Lost Opportunities: {data?.checkpoints['Lost']?.num || 0}{' '}
                  <span style={{ color: '#2fac2f' }}>
                    {data?.checkpoints['Lost']?.selfGenCount > 0 && `(${data?.checkpoints['Lost']?.selfGenCount})`}
                  </span>
                </Styled.ReportHeading>
                <Styled.ReportGridBox column="1fr">
                  {data?.checkpoints['Lost']?.types?.map((type: any, index: number) => {
                    return (
                      <>
                        <div style={{ width: '100%' }}>
                          <Styled.ReportHeading fontSize={'16px'} className="margin">
                            <span
                              onClick={() => {
                                toggleCount(`${type?.name}-Lost`)
                              }}
                              className="cursor"
                            >
                              {!toggleHeading[`${type?.name}-Lost`] ? <>&#9654;</> : <>&#9660;</>}
                              {type?.name} Lost: {type?.num || 0}{' '}
                              <span style={{ color: '#2fac2f' }}>
                                {type?.selfGenCount > 0 && `(${type?.selfGenCount})`}
                              </span>
                            </span>
                          </Styled.ReportHeading>
                          {toggleHeading[`${type?.name}-Lost`] ? (
                            <Styled.TableContainer>
                              <Styled.TableHeading column={`2fr repeat(7, 1fr)`}>
                                <Styled.TableTitle>Lost Opportunities</Styled.TableTitle>
                                <Styled.TableTitle>Source</Styled.TableTitle>
                                <Styled.TableTitle className="right-align">Cost</Styled.TableTitle>
                                <Styled.TableTitle></Styled.TableTitle>
                                <Styled.TableTitle>Date lost</Styled.TableTitle>
                                <Styled.TableTitle>Reason lost</Styled.TableTitle>
                                <Styled.TableTitle>Cred req</Styled.TableTitle>
                                <Styled.TableTitle>Cred rec</Styled.TableTitle>
                              </Styled.TableHeading>
                              {/* new Date(a?.oppDate) - new Date(b?.oppDate) || a.lastName.localeCompare(b.lastName) */}
                              {type?.opps
                                ?.sort((a: any, b: any) => {
                                  const dateA = a?.lostDate ? new Date(a.lostDate).toISOString().split('T')[0] : ''
                                  const dateB = b?.lostDate ? new Date(b.lostDate).toISOString().split('T')[0] : ''

                                  if (dateA < dateB) return -1
                                  if (dateA > dateB) return 1

                                  // If the dates are the same or missing, sort by last name
                                  return (a?.clientId?.lastName || '').localeCompare(b?.clientId?.lastName || '')
                                })
                                ?.map((opp: any) => {
                                  const matchedObject: any = stages.find((item) => item._id === opp?.stage)
                                  return (
                                    <>
                                      <tr>
                                        <Styled.TableContent
                                          as={Link}
                                          to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${opp?._id}`}
                                          key={opp?._id}
                                          column={`2fr repeat(7, 1fr)`}
                                          selfGen={opp?.selfGen}
                                          pointer="pointer"
                                          // onClick={() => getpageById(opp?.stage, opp?._id)}
                                        >
                                          <Styled.CrewReportTableContentLabel>
                                            {renderClientName(
                                              opp?.clientId?.isBusiness,
                                              opp?.clientId?.firstName,
                                              opp?.clientId?.lastName
                                            )}
                                          </Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel>
                                            {opp?.leadSource}
                                          </Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel className="right-align">
                                            ${formatNumberToCommaS(opp?.leadCost)}
                                          </Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel></Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel>
                                            {dayjsFormat(opp?.lostDate, 'M/D/YY')}
                                          </Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel>
                                            <SharedStyled.TooltipContainer
                                              positionLeft="0"
                                              positionBottom="0"
                                              positionLeftDecs="40px"
                                              positionBottomDecs="25px"
                                            >
                                              <span className="tooltip-content">{opp?.lostReason}</span>
                                              {truncateParagraph(opp?.lostReason, 15)}
                                            </SharedStyled.TooltipContainer>
                                            {/* {opp?.lostReason} */}
                                          </Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel>
                                            {opp?.creditRequested}
                                          </Styled.CrewReportTableContentLabel>
                                          <Styled.CrewReportTableContentLabel>
                                            {opp?.creditReceived}
                                          </Styled.CrewReportTableContentLabel>
                                        </Styled.TableContent>
                                      </tr>
                                    </>
                                  )
                                })}
                            </Styled.TableContainer>
                          ) : null}
                        </div>
                      </>
                    )
                  })}
                </Styled.ReportGridBox>
              </Styled.ReportWrapper>
            </>
          )}
        </Styled.ReportMainContainer>
      ) : (
        <SharedStyled.ContentHeader textAlign="center" margin="40px 0 0 0" fontWeight="500">
          Please choose sales person and date range!
        </SharedStyled.ContentHeader>
      )}
      <ButtonWrapper>
        <Button width="max-content" onClick={() => navigate(`/reports`)}>
          Back To Reports
        </Button>
      </ButtonWrapper>
    </>
  )
}

{
  /* <Styled.ReportWrapper>
                  <Styled.ReportHeading>
                    Total {key} : {checkpoint?.num}
                  </Styled.ReportHeading>
                  <Styled.ReportGridBox>
                    {types?.map((type: any, index: number) => {
                      return (
                        <>
                          <div style={{ width: '100%' }}>
                            <Styled.ReportContent>
                              {type?.name} {key}: {type?.RR}
                            </Styled.ReportContent>
                            <Styled.TableContainer>
                              <Styled.TableHeading>
                                <Styled.TableTitle>Client Name</Styled.TableTitle>
                                <Styled.TableTitle>Date</Styled.TableTitle>
                                <Styled.TableTitle>City</Styled.TableTitle>
                                <Styled.TableTitle>Source</Styled.TableTitle>
                                <Styled.TableTitle>Cost</Styled.TableTitle>
                              </Styled.TableHeading>
                              <Styled.TableContent>
                                <Styled.CrewReportTableContentLabel>
                                  {type?.opps?.lastName},{type?.opps?.firstName}
                                </Styled.CrewReportTableContentLabel>
                                <Styled.CrewReportTableContentLabel>
                                  {type?.opps?.oppDate}
                                </Styled.CrewReportTableContentLabel>
                                <Styled.CrewReportTableContentLabel>
                                  {type?.opps?.city}
                                </Styled.CrewReportTableContentLabel>
                                <Styled.CrewReportTableContentLabel>
                                  {type?.opps?.leadSource}
                                </Styled.CrewReportTableContentLabel>
                                <Styled.CrewReportTableContentLabel>
                                  {type?.opps?.leadCost}
                                </Styled.CrewReportTableContentLabel>
                              </Styled.TableContent>
                            </Styled.TableContainer>
                          </div>
                        </>
                      )
                    })}
                  </Styled.ReportGridBox>
                </Styled.ReportWrapper> */
}

export default SalesPersonReport
