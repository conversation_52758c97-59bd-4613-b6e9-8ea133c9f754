import { Form, Formik } from 'formik'
import { Fragment, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { Link, useNavigate, useParams } from 'react-router-dom'

import { getProductionReport } from '../../../logic/apis/report'
import { SharedDate } from '../../../shared/date/SharedDate'
import * as Styled from './style'
import Button from '../../../shared/components/button/Button'
import { FlexCol, SectionTitle } from '../../../styles/styled'
import { Nue, StorageKey } from '../../../shared/helpers/constants'
import { getStages } from '../../../logic/apis/sales'
import { dayjsFormat, formatNumberToCommaS, getDataFromLocalStorage, isSuccess } from '../../../shared/helpers/util'
import { I_Stage } from '../../opportunity/components/assessmentForm/AssessmentForm'
import * as SharedStyled from '../../../styles/styled'
import CheckboxList from '../../track/components/CheckboxList'
import { CaretSvg } from '../../../shared/helpers/images'
import { DropdownButton, DropdownContainer, DropdownContent } from '../../../shared/dropdownWithCheckboxes/style'

const calculateScore = (dataObj: { volume: number; material: number; laborTotal: number; commission: number }) => {
  let budgetScore =
    (dataObj?.volume - dataObj?.material - dataObj?.laborTotal - dataObj?.commission) / dataObj?.laborTotal
  budgetScore = budgetScore * 10 - 20
  return budgetScore
}

const getClassName = (valueOne: number, valueTwo: number) => {
  if (valueOne <= valueTwo) {
    return 'green'
  } else {
    return 'red'
  }
}

const convertToPercent = (val: number) => {
  const value = val < 1 ? (val * 100)?.toFixed() : val?.toFixed()
  return `${value}%`
}

export const getSelectedFilters = (obj: any): any[] => {
  return (
    obj &&
    Object.entries(obj)
      ?.map(([k, v]) => (v ? k : null))
      ?.filter(Boolean)
  )
}

export const handleFilterChange = (selectedItems: { [key: string]: boolean }, fn: any) => {
  fn(selectedItems)
}

export const checkIfFilterIsApplied = (filters: Record<string, any>) => {
  return filters && Object.values(filters).some((value) => (Array.isArray(value) ? value.length > 0 : Boolean(value)))
}

const ProductionReport = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [stages, setStages] = useState<I_Stage[]>([])

  const navigate = useNavigate()
  const dropdownRef = useRef<HTMLDivElement>(null)
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [productionReport, setProductionReport] = useState<any>([])
  const [buttonCall, setbuttonCall] = useState(false)
  const [crewTypeSelected, setCrewTypeSelected] = useState<any>({})
  const [projectTypeSelected, setProjectTypeSelected] = useState<any>({})
  const [projectManagerSelected, setProjectManagerSelected] = useState<any>({})
  const [packagesSelected, setPackagesSelected] = useState<any>({})

  const [salesPersonSelected, setSalesPersonSelected] = useState<any>({})
  const [initialValues, setInitialValues] = useState({
    startDate: '',
    endDate: '',
  })
  const [showFilter, setShowFilter] = useState(false)
  const uniqueValues = useMemo(() => {
    const projects = new Set()
    const crews = new Set()
    const projectManager = new Set()
    const salesPerson = new Set()
    const packages = new Map<string, { name: string; _id: string }>()

    productionReport?.types?.forEach((type: { name: string; opps: any[]; num: number }) => {
      if (type.name === 'Warranty' && type.num) {
        projects.add('Warranty')
        type.opps.forEach((opp) => {
          opp.acceptedProjectDetails.name = 'Warranty'
        })
      }
      type.opps?.forEach((opp) => {
        opp?.acceptedProjectDetails?.name && projects.add(opp?.acceptedProjectDetails?.name)
        crews.add(opp?.workingCrew?.name ?? 'None')
        projectManager.add(opp?.projectManagerDetails?.name ?? 'None')
        salesPerson.add(opp?.salesPersonDetails?.name ?? 'None')

        if (opp?.packages?.length) {
          opp?.packages.forEach((itm: any) => {
            packages.set(itm?.name, itm)
          })
        }
      })
    })

    const uniquePackages: {
      name: string
      _id: string
    }[] = []

    packages?.forEach((val) => {
      uniquePackages.push(val)
    })

    return {
      projects: [...projects]?.map((itm) => ({ name: itm, _id: itm })),
      crews: [...crews]
        ?.map((itm) => ({ name: itm, _id: itm }))
        ?.sort((a: any, b: any) => {
          if (a.name === 'None') return 1
          if (b.name === 'None') return -1
          return a.name?.localeCompare(b.name)
        }),
      projectManager: [...projectManager]

        ?.map((itm) => ({ name: itm, _id: itm }))
        ?.sort((a: any, b: any) => {
          if (a.name === 'None') return 1
          if (b.name === 'None') return -1
          return a.name?.localeCompare(b.name)
        }),
      salesPerson: [...salesPerson]

        ?.map((itm) => ({ name: itm, _id: itm }))
        ?.sort((a: any, b: any) => {
          if (a.name === 'None') return 1
          if (b.name === 'None') return -1
          return a.name?.localeCompare(b.name)
        }),
      packages: uniquePackages
        ?.sort((a: any, b: any) => a.order - b.order)
        ?.map((itm: any) => ({ name: itm?.name, _id: itm?.name })),
    }
  }, [productionReport])

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const paramStartDate = params.get('startDate')
    const paramEndDate = params.get('endDate')
    if (paramStartDate && paramEndDate) {
      if (paramStartDate !== '' && paramEndDate !== '') {
        setInitialValues({ startDate: paramStartDate, endDate: paramEndDate })
        if (!buttonCall) {
          handleSubmitForm({ startDate: paramStartDate, endDate: paramEndDate })
        }
      }
    }
  }, [location.search])

  const handleDateSelection = (selectedStartDate: string, selectedEndDate: string) => {
    const params = new URLSearchParams()
    params.append('startDate', selectedStartDate)
    params.append('endDate', selectedEndDate)
    const newURL = `${window.location.pathname}?${params.toString()}`
    window.history.pushState({}, '', newURL)
    // Perform any other actions needed when start and end dates are selected
  }

  const handleSubmitForm = async (values: any) => {
    const paramsData: any = {
      endDate: values.endDate,
      startDate: values.startDate,
    }
    handleDateSelection(values.startDate, values.endDate)
    try {
      setIsLoading(true)
      const productionData = await getProductionReport(paramsData)

      setProductionReport(productionData)
    } catch (error) {
      console.log('Error', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    getStagesData()
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowFilter(false)
      }
    }

    if (showFilter) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showFilter])

  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false)
      // const stagesRes = await getStages({ companyId: currentCompany._id }, false, operationsFlag)
      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const formatStages: I_Stage[] = new Array(stages.length)
        stages.forEach((stage: I_Stage) => {
          formatStages[stage.sequence - 1] = stage
        })
        setStages(stages)
        // setStages(formatStages)
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }

  // const getpageById = (projectId: string, oppId: string, event: any) => {
  //   if (event.button === 1) {
  //     window.open(
  //       `${window?.location?.origin}/reports/projectReport?pId=${projectId}&oppId=${oppId}`,
  //       '_blank'
  //     )
  //   } else {
  //     // If a matching object is found, return its name property
  //     navigate(`/reports/projectReport?pId=${projectId}&oppId=${oppId}`)
  //   }
  // }

  const projectTypeFilters = getSelectedFilters(projectTypeSelected)
  const crewTypeFilters = getSelectedFilters(crewTypeSelected)
  const salesPersonTypeFilters = getSelectedFilters(salesPersonSelected)
  const projectManagerTypeFilters = getSelectedFilters(projectManagerSelected)
  const packageTypeFilters = getSelectedFilters(packagesSelected)

  const filters = {
    projectTypeFilters,
    crewTypeFilters,
    salesPersonTypeFilters,
    projectManagerTypeFilters,
    packageTypeFilters,
  }

  const filteredOps = useMemo(() => {
    return productionReport?.types
      ?.map((type: { name: string; opps: any[]; [key: string]: any }) => {
        let totalGrossProfit = 0
        let totalGross = 0
        let materialTotal = 0
        let materialTotalBudget = 0
        let laborTotal = 0
        let laborTotalBudget = 0
        let totalVolume = 0
        let totalVolumeBudget = 0
        let totalCommission = 0
        let totalBudgetComission = 0
        let totalScore = 0
        let totalBudgetScore = 0
        let totalExtras = 0
        let totalHrs = 0
        let totalBudgetHrs = 0

        const hasMatchingOpp = !type.opps?.length
          ? []
          : type.opps
              .filter((opp) => {
                const projectMatch =
                  projectTypeFilters.length === 0 || projectTypeFilters.includes(opp?.acceptedProjectDetails?.name)
                const crewMatch =
                  crewTypeFilters.length === 0 || crewTypeFilters.includes(opp?.workingCrew?.name ?? 'None')
                const salesPersonMatch =
                  salesPersonTypeFilters.length === 0 ||
                  salesPersonTypeFilters.includes(opp?.salesPersonDetails?.name ?? 'None')
                const projectManagerMatch =
                  projectManagerTypeFilters.length === 0 ||
                  projectManagerTypeFilters.includes(opp?.projectManagerDetails?.name ?? 'None')

                const packageMatch =
                  packageTypeFilters.length === 0 ||
                  opp?.packages?.filter((itm: any) => packageTypeFilters.includes(itm?.name)).length > 0

                return crewMatch && salesPersonMatch && projectManagerMatch && projectMatch && packageMatch
              })
              ?.map((itm) => {
                totalGrossProfit += itm?.grossProfit
                totalGross += itm?.gross
                materialTotal += itm?.actual?.mTotal
                materialTotalBudget += itm?.budget?.mTotal
                laborTotal += itm?.actual?.lTotal
                laborTotalBudget += itm?.budget?.lTotal
                totalVolume += itm?.actual?.total
                totalVolumeBudget += itm?.budget?.total
                totalCommission += itm?.actual?.commission
                totalBudgetComission += itm?.budget?.commission
                totalExtras += itm?.extraAmount
                totalHrs += itm?.actual?.hrs
                totalBudgetHrs += itm?.budget?.ttlHours
                totalScore = calculateScore({
                  commission: totalCommission,
                  laborTotal: laborTotal,
                  material: materialTotal,
                  volume: totalVolume,
                })
                totalBudgetScore = calculateScore({
                  commission: totalBudgetComission,
                  laborTotal: laborTotalBudget,
                  material: materialTotalBudget,
                  volume: totalVolumeBudget,
                })
                return itm
              })

        // re-calculate the total percentage values based on filter

        let materialTotalBudgetPercent = materialTotalBudget / totalVolumeBudget

        let materialTotalPercent = materialTotal / totalVolume
        let laborTotalBudgetPercent = laborTotalBudget / totalVolumeBudget

        let laborTotalPercent = laborTotal / totalVolume
        let totalBudgetComissionPercent = totalBudgetComission / totalVolumeBudget

        let totalComissionPercent = totalCommission / totalVolume
        let totalGrossPercent = totalGross / totalVolume
        let totalGrossProfitPercent = totalGrossProfit / totalVolumeBudget

        return !!hasMatchingOpp?.length
          ? ({
              ...type,
              opps: hasMatchingOpp,
              totalGrossProfit,
              totalGross,
              materialTotal,
              materialTotalBudget,
              laborTotal,
              laborTotalBudget,
              totalVolume,
              totalVolumeBudget,
              totalCommission,
              totalBudgetComission,
              materialTotalBudgetPercent,
              materialTotalPercent,
              laborTotalBudgetPercent,
              laborTotalPercent,
              totalBudgetComissionPercent,
              totalComissionPercent,
              totalGrossPercent,
              totalGrossProfitPercent,
              totalScore,
              totalBudgetScore,
              totalExtras,
              totalHrs,
              totalBudgetHrs,
            } as any)
          : null
      })
      ?.filter(Boolean)
  }, [projectTypeFilters, crewTypeFilters])

  const renderData = useMemo(() => {
    const calculateTotal = (array: any[], key: string): number =>
      array?.reduce((acc, item) => acc + (item[key] || 0), 0)

    const calculatePercent = (value: number, total: number): number => (total ? value / total : 0)

    const totalValues = {
      totalVolume: calculateTotal(filteredOps, 'totalVolume'),
      totalVolumeBudget: calculateTotal(filteredOps, 'totalVolumeBudget'),
      totalExtras: calculateTotal(filteredOps, 'totalExtras'),
      totalGrossProfit: calculateTotal(filteredOps, 'totalGrossProfit'),
      totalGross: calculateTotal(filteredOps, 'totalGross'),
      totalCommission: calculateTotal(filteredOps, 'totalCommission'),
      totalBudgetComission: calculateTotal(filteredOps, 'totalBudgetComission'),
      materialTotal: calculateTotal(filteredOps, 'materialTotal'),
      materialTotalBudget: calculateTotal(filteredOps, 'materialTotalBudget'),
      laborTotal: calculateTotal(filteredOps, 'laborTotal'),
      laborTotalBudget: calculateTotal(filteredOps, 'laborTotalBudget'),
      totalHrs: calculateTotal(filteredOps, 'totalHrs'),
      totalBudgetHrs: calculateTotal(filteredOps, 'totalBudgetHrs'),
    }

    return {
      num: checkIfFilterIsApplied(filters)
        ? filteredOps?.reduce((acc: number, type: any) => acc + (type?.opps?.length || 0), 0)
        : productionReport?.num,
      ...totalValues,
      totalGrossPercent: calculatePercent(totalValues.totalGross, totalValues.totalVolumeBudget),
      totalGrossProfitPercent: calculatePercent(totalValues.totalGrossProfit, totalValues.totalVolume),
      totalComissionPercent: calculatePercent(totalValues.totalCommission, totalValues.totalVolume),
      totalBudgetComissionPercent: calculatePercent(totalValues.totalBudgetComission, totalValues.totalVolumeBudget),
      materialTotalPercent: calculatePercent(totalValues.materialTotal, totalValues.totalVolume),
      materialTotalBudgetPercent: calculatePercent(totalValues.materialTotalBudget, totalValues.totalVolumeBudget),
      laborTotalPercent: calculatePercent(totalValues.laborTotal, totalValues.totalVolume),
      laborTotalBudgetPercent: calculatePercent(totalValues.laborTotalBudget, totalValues.totalVolumeBudget),
      totalScore: calculateScore({
        commission: totalValues.totalCommission,
        laborTotal: totalValues.laborTotal,
        material: totalValues.materialTotal,
        volume: totalValues.totalVolume,
      }),
      totalBudgetScore: calculateScore({
        commission: totalValues.totalBudgetComission,
        laborTotal: totalValues.laborTotalBudget,
        material: totalValues.materialTotalBudget,
        volume: totalValues.totalVolumeBudget,
      }),
      types: filteredOps,
    }
  }, [filteredOps, filters, productionReport])

  return (
    <div style={{ fontFamily: `${Nue.regular}` }}>
      <SectionTitle margin="0 0 20px 0">Production Report</SectionTitle>
      <Formik enableReinitialize={true} initialValues={initialValues} onSubmit={handleSubmitForm}>
        {({ values, setFieldValue, touched, errors }) => (
          <Form>
            <SharedStyled.FlexBox width="100%" gap="10px" alignItems="flex-end" alignItemsM="start" column="column">
              <div>
                <Styled.DateLabel>Date Start:</Styled.DateLabel>
                <div>
                  <SharedDate
                    value={values.startDate}
                    labelName="From"
                    stateName="startDate"
                    error={touched.startDate && errors.startDate ? true : false}
                    setFieldValue={setFieldValue}
                  />
                </div>
              </div>
              <div>
                <Styled.DateLabel>Date End:</Styled.DateLabel>
                <div>
                  <SharedDate
                    value={values.endDate}
                    labelName="To"
                    stateName="endDate"
                    error={touched.endDate && errors.endDate ? true : false}
                    min={values.startDate}
                    setFieldValue={setFieldValue}
                  />
                </div>
              </div>
              <Styled.KPIButtonContainer>
                <Button
                  type="submit"
                  isLoading={isLoading}
                  onClick={() => setbuttonCall(true)}
                  disabled={values.endDate === '' || values.startDate === ''}
                  width="max-content"
                  height="52px"
                >
                  Run Report
                </Button>
              </Styled.KPIButtonContainer>

              <div style={{ marginLeft: 'auto' }}>
                <DropdownContainer ref={dropdownRef}>
                  <DropdownButton
                    disabled={uniqueValues.projects?.length === 0}
                    type="button"
                    onClick={() => setShowFilter((p) => !p)}
                    style={{ width: '300px' }}
                  >
                    <div className="selection">
                      Filter the production report{' '}
                      <img className={showFilter ? 'rotate' : ''} src={CaretSvg} alt="chevron" />
                    </div>
                  </DropdownButton>
                  {showFilter ? (
                    <DropdownContent style={{ width: '300px', maxHeight: '500px', overflowY: 'scroll' }}>
                      <Styled.FilterContainer
                        gap="0px"
                        justifyContent="flex-start"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <CheckboxList
                          className="small"
                          title="Project Types"
                          data={uniqueValues.projects}
                          checkedItems={projectTypeSelected}
                          onSelectionChange={(val) => {
                            handleFilterChange(val, setProjectTypeSelected)
                          }}
                          allText="All"
                          isCheckedAll={!projectTypeFilters?.length}
                        />
                        <CheckboxList
                          checkedItems={crewTypeSelected}
                          title="Crews"
                          className="small"
                          data={uniqueValues.crews}
                          onSelectionChange={(val) => {
                            handleFilterChange(val, setCrewTypeSelected)
                          }}
                          allText="All"
                          isCheckedAll={!crewTypeFilters?.length}
                        />
                        <CheckboxList
                          checkedItems={salesPersonSelected}
                          title="Salesperson"
                          className="small"
                          data={uniqueValues.salesPerson}
                          onSelectionChange={(val) => {
                            handleFilterChange(val, setSalesPersonSelected)
                          }}
                          allText="All"
                          isCheckedAll={!salesPersonTypeFilters?.length}
                        />
                        <CheckboxList
                          className="small"
                          checkedItems={projectManagerSelected}
                          title="Project Manager"
                          data={uniqueValues.projectManager}
                          allText="All"
                          isCheckedAll={!projectManagerTypeFilters?.length}
                          onSelectionChange={(val) => {
                            handleFilterChange(val, setProjectManagerSelected)
                          }}
                        />
                        <CheckboxList
                          className="small"
                          checkedItems={packagesSelected}
                          title="Packages"
                          data={uniqueValues.packages}
                          allText="All"
                          isCheckedAll={!packageTypeFilters?.length}
                          onSelectionChange={(val) => {
                            handleFilterChange(val, setPackagesSelected)
                          }}
                        />
                      </Styled.FilterContainer>
                    </DropdownContent>
                  ) : null}
                </DropdownContainer>
              </div>
            </SharedStyled.FlexBox>
            {values.startDate && values.endDate && (
              <Styled.ProductionReportHeading>
                From {dayjsFormat(values.startDate, 'M/D/YY')} to {dayjsFormat(values.endDate, 'M/D/YY')}
              </Styled.ProductionReportHeading>
            )}
          </Form>
        )}
      </Formik>

      <FlexCol gap="10px">
        {renderData?.types?.length !== 0 ? (
          <>
            {renderData?.num ? (
              <>
                <Styled.ProductionReportSubHeading>
                  Average Job Size: ${formatNumberToCommaS(renderData?.totalVolume / renderData?.num)}
                </Styled.ProductionReportSubHeading>
                <Styled.ProductionReportSubHeading>
                  Completed Jobs: {renderData?.num} | ${formatNumberToCommaS(renderData?.totalVolume)}
                </Styled.ProductionReportSubHeading>
              </>
            ) : null}
            <Styled.TableMainContainer>
              {renderData?.types?.map((value: any, index: number) => (
                <Fragment key={index}>
                  {value?.opps?.length ? (
                    <>
                      {value?.num ? (
                        <Styled.TableContentLabel className="title">
                          Completed {value?.name}: {value?.opps?.length}
                        </Styled.TableContentLabel>
                      ) : null}
                      {value?.num ? (
                        <>
                          <Styled.TableContainer key={index}>
                            <Styled.TableHeading column="2fr 1fr 2fr repeat(2,1.5fr) 2fr 2fr 2fr 2fr 1fr 1fr">
                              <Styled.TableTitle>PO#</Styled.TableTitle>
                              <Styled.TableTitle>Done</Styled.TableTitle>
                              <Styled.TableTitle>Crew</Styled.TableTitle>
                              <Styled.TableTitle className="right-align">Volume</Styled.TableTitle>
                              <Styled.TableTitle className="right-align">EXTRAS</Styled.TableTitle>
                              <Styled.TableTitle className="right-align">GROSS PROFIT</Styled.TableTitle>
                              <Styled.TableTitle className="right-align">COMMISSION</Styled.TableTitle>
                              {/* <Styled.TableTitle className="right-align"></Styled.TableTitle> */}
                              <Styled.TableTitle className="right-align">Materials</Styled.TableTitle>
                              <Styled.TableTitle className="right-align">Labor</Styled.TableTitle>
                              <Styled.TableTitle className="right-align">HRS</Styled.TableTitle>
                              <Styled.TableTitle className="right-align">PS</Styled.TableTitle>
                              {/* <Styled.TableTitle>COP</Styled.TableTitle> */}
                            </Styled.TableHeading>
                            {value?.opps
                              ?.sort(
                                (a: any, b: any) =>
                                  new Date(a?.jobCompletedDate)?.getTime() - new Date(b?.jobCompletedDate)?.getTime()
                              )
                              ?.map((opp: any, idx: number) => (
                                <>
                                  <tr key={idx}>
                                    <Styled.TableContent
                                      as={Link}
                                      to={`/reports/projectReport?pId=${opp?.acceptedProjectId}&oppId=${opp?._id}`}
                                      key={opp?._id}
                                      pointer="pointer"
                                      // onMouseDown={(e: any) => getpageById(opp?.project?._id, opp?._id, e)}
                                      column="2fr 1fr 2fr repeat(2,1.5fr) 2fr 2fr 2fr 2fr 1fr 1fr"
                                    >
                                      <Styled.TableContentLabel>
                                        {/* P0# */}
                                        {opp?.PO}-{opp?.num} <br />
                                        <span className="small">{opp?.salesPersonDetails?.name ?? '--'}</span>
                                      </Styled.TableContentLabel>
                                      <Styled.TableContentLabel>
                                        {/* Done */}
                                        {dayjsFormat(opp?.jobCompletedDate, 'M/D/YY')}
                                      </Styled.TableContentLabel>
                                      <Styled.TableContentLabel>
                                        {/* Crew */}
                                        {opp?.workingCrew?.name ?? '--'}
                                        <br />
                                        <span className="small">{opp?.projectManagerDetails?.name ?? '--'}</span>
                                      </Styled.TableContentLabel>
                                      <Styled.TableContentLabel className="right-align">
                                        {/* Volume */}${formatNumberToCommaS(opp?.actual?.total) ?? '--'} <br />
                                        <span className="small">
                                          ${formatNumberToCommaS(opp?.budget?.total) ?? '--'}
                                        </span>
                                      </Styled.TableContentLabel>

                                      <Styled.TableContentLabel className="right-align">
                                        {/*  EXTRAS */}${formatNumberToCommaS(opp?.extraAmount) ?? '--'}
                                      </Styled.TableContentLabel>
                                      <Styled.TableContentLabel
                                        className={`right-align ${getClassName(opp?.gross, opp?.grossProfit)}`}
                                      >
                                        {/* GP */}${formatNumberToCommaS(opp?.grossProfit) || 0} (
                                        {opp?.actualGrossPercent}%)
                                        <br />
                                        <span className="small">
                                          ${formatNumberToCommaS(opp?.gross) || 0} ({opp?.grossPercent}%)
                                        </span>
                                      </Styled.TableContentLabel>
                                      <Styled.TableContentLabel className="right-align ">
                                        {/* Comm */}${formatNumberToCommaS(opp?.actual?.commission) ?? '--'} (
                                        {opp?.commisionPercent}%)
                                        <br />
                                        <span className="small">
                                          ${formatNumberToCommaS(opp?.budget?.commission) ?? '--'} (
                                          {opp?.commisionBudgetPercent}%)
                                        </span>
                                      </Styled.TableContentLabel>
                                      {/* <Styled.TableContentLabel className="right-align"></Styled.TableContentLabel> */}
                                      <Styled.TableContentLabel
                                        className={`right-align ${getClassName(
                                          opp?.actual?.mTotal,
                                          opp?.budget?.mTotal
                                        )}`}
                                      >
                                        {/* Materials  */}${formatNumberToCommaS(opp?.actual?.mTotal) || 0} (
                                        {opp?.actual?.mTotalP}%)
                                        <br />
                                        <span className="small">
                                          ${formatNumberToCommaS(opp?.budget?.mTotal) || 0} ({opp?.budget?.mTotalP}%)
                                        </span>
                                      </Styled.TableContentLabel>
                                      <Styled.TableContentLabel
                                        className={`right-align ${getClassName(
                                          opp?.actual?.lTotal,
                                          opp?.budget?.lTotal
                                        )}`}
                                      >
                                        {/* Labor  */}${formatNumberToCommaS(opp?.actual?.lTotal) || 0} (
                                        {opp?.actual?.lTotalP}%)
                                        <br />
                                        <span className="small">
                                          ${formatNumberToCommaS(opp?.budget?.lTotal) || 0} ({opp?.budget?.lTotalP}%)
                                        </span>
                                      </Styled.TableContentLabel>
                                      <Styled.TableContentLabel
                                        className={`right-align ${getClassName(
                                          opp?.actual?.hrs,
                                          opp?.budget?.ttlHours
                                        )}`}
                                      >
                                        {/*HRS */}
                                        {opp?.actual?.hrs?.toFixed(2)}
                                        <br />
                                        <span className="small">{opp?.budget?.ttlHours?.toFixed(2)}</span>
                                      </Styled.TableContentLabel>
                                      <Styled.TableContentLabel
                                        className={`right-align ${getClassName(opp?.budgetScore, opp?.score)}`}
                                      >
                                        {/* Score  */}
                                        {opp?.score ? opp?.score?.toFixed(1) : '--'}
                                        <br />
                                        <span className="small">
                                          {opp?.budgetScore ? '--' : opp?.budgetScore?.toFixed(1)}
                                        </span>
                                      </Styled.TableContentLabel>
                                      {/* <Styled.TableContentLabel>
                              ${formatNumberToCommaS(opp?.actual?.left) || 0} - {opp?.actual?.leftP || 0}%
                            </Styled.TableContentLabel> */}
                                    </Styled.TableContent>
                                  </tr>
                                </>
                              ))}
                            {/* <Styled.TableHeading
                              className="total"
                              column="2fr 1fr 2fr repeat(2,1.5fr) 2fr 2fr 2fr 2fr 1fr 1fr"
                            >
                              <td style={{ alignSelf: 'center' }}>
                                <SharedStyled.Text fontSize="20px" style={{ fontFamily: Nue.medium }}>
                                  TOTAL&nbsp;:
                                </SharedStyled.Text>
                              </td>
                              <Styled.TableContentLabel></Styled.TableContentLabel>
                              <Styled.TableContentLabel></Styled.TableContentLabel>

                              <Styled.TableContentLabel className="right-align">
                                ${formatNumberToCommaS(value?.totalVolume) ?? '--'}
                                <br />
                                <span className="small">${formatNumberToCommaS(value?.totalVolumeBudget) ?? '--'}</span>
                              </Styled.TableContentLabel>
                              <Styled.TableContentLabel className="right-align">
                                ${formatNumberToCommaS(value?.totalExtras) ?? '--'}
                              </Styled.TableContentLabel>

                              <Styled.TableContentLabel
                                className={`right-align ${getClassName(value?.totalGross, value?.totalGrossProfit)}`}
                              >
                                ${formatNumberToCommaS(value?.totalGrossProfit) ?? '--'} (
                                {convertToPercent(value?.totalGrossProfitPercent)})
                                <br />
                                <span className="small">
                                  ${formatNumberToCommaS(value?.totalGross) ?? '--'} (
                                  {convertToPercent(value?.totalGrossPercent)})
                                </span>
                              </Styled.TableContentLabel>
                              <Styled.TableContentLabel className="right-align">
                                ${formatNumberToCommaS(value?.totalCommission) ?? '--'} (
                                {convertToPercent(value?.totalComissionPercent)})
                                <br />
                                <span className="small">
                                  ${formatNumberToCommaS(value?.totalBudgetComission) ?? '--'} (
                                  {convertToPercent(value?.totalBudgetComissionPercent)})
                                </span>
                              </Styled.TableContentLabel>
                              <Styled.TableContentLabel
                                className={`right-align ${getClassName(
                                  value?.materialTotal,
                                  value?.materialTotalBudget
                                )}`}
                              >
                                ${formatNumberToCommaS(value?.materialTotal) ?? '--'} (
                                {convertToPercent(value?.materialTotalPercent)})
                                <br />
                                <span className="small">
                                  ${formatNumberToCommaS(value?.materialTotalBudget) ?? '--'} (
                                  {convertToPercent(value?.materialTotalBudgetPercent)})
                                </span>
                              </Styled.TableContentLabel>
                              <Styled.TableContentLabel
                                className={`right-align ${getClassName(value?.laborTotal, value?.laborTotalBudget)}`}
                              >
                                ${formatNumberToCommaS(value?.laborTotal) ?? '--'} (
                                {convertToPercent(value?.laborTotalPercent)})
                                <br />
                                <span className="small">
                                  ${formatNumberToCommaS(value?.laborTotalBudget) ?? '--'} (
                                  {convertToPercent(value?.laborTotalBudgetPercent)})
                                </span>
                              </Styled.TableContentLabel>
                              <Styled.TableContentLabel
                                className={`right-align ${getClassName(value?.totalHrs, value?.totalBudgetHrs)}`}
                              >
                                {value?.totalHrs?.toFixed(2) ?? '--'}
                                <br />
                                <span className="small">{value?.totalBudgetHrs?.toFixed(2) ?? '--'}</span>
                              </Styled.TableContentLabel>
                              <Styled.TableContentLabel
                                className={`right-align ${getClassName(value?.totalBudgetScore, value?.totalScore)}`}
                              >
                                {value?.totalScore?.toFixed(1) ?? '--'}
                                <br />
                                <span className="small">{value?.totalBudgetScore?.toFixed(1) ?? '--'}</span>
                              </Styled.TableContentLabel>
                            </Styled.TableHeading> */}
                          </Styled.TableContainer>
                        </>
                      ) : null}
                    </>
                  ) : null}
                </Fragment>
              ))}

              {/* Grand Totals */}

              {renderData?.num ? (
                <Styled.TableContainer>
                  <Styled.TableHeading className="total" column="2fr 1fr 2fr repeat(2,1.5fr) 2fr 2fr 2fr 2fr 1fr 1fr">
                    <td style={{ alignSelf: 'center' }}>
                      <SharedStyled.Text fontSize="20px" style={{ fontFamily: Nue.medium }}>
                        TOTAL&nbsp;:
                      </SharedStyled.Text>
                    </td>
                    <Styled.TableContentLabel></Styled.TableContentLabel>
                    <Styled.TableContentLabel></Styled.TableContentLabel>

                    <Styled.TableContentLabel className="right-align">
                      ${formatNumberToCommaS(renderData?.totalVolume) ?? '--'}
                      <br />
                      <span className="small">${formatNumberToCommaS(renderData?.totalVolumeBudget) ?? '--'}</span>
                    </Styled.TableContentLabel>
                    <Styled.TableContentLabel className="right-align">
                      ${formatNumberToCommaS(renderData?.totalExtras) ?? '--'}
                    </Styled.TableContentLabel>

                    <Styled.TableContentLabel
                      className={`right-align ${getClassName(renderData?.totalGross, renderData?.totalGrossProfit)}`}
                    >
                      ${formatNumberToCommaS(renderData?.totalGrossProfit) ?? '--'} (
                      {convertToPercent(renderData?.totalGrossProfitPercent)})
                      <br />
                      <span className="small">
                        ${formatNumberToCommaS(renderData?.totalGross) ?? '--'} (
                        {convertToPercent(renderData?.totalGrossPercent)})
                      </span>
                    </Styled.TableContentLabel>
                    <Styled.TableContentLabel className="right-align">
                      ${formatNumberToCommaS(renderData?.totalCommission) ?? '--'} (
                      {convertToPercent(renderData?.totalComissionPercent)})
                      <br />
                      <span className="small">
                        ${formatNumberToCommaS(renderData?.totalBudgetComission) ?? '--'} (
                        {convertToPercent(renderData?.totalBudgetComissionPercent)})
                      </span>
                    </Styled.TableContentLabel>
                    <Styled.TableContentLabel
                      className={`right-align ${getClassName(
                        renderData?.materialTotal,
                        renderData?.materialTotalBudget
                      )}`}
                    >
                      ${formatNumberToCommaS(renderData?.materialTotal) ?? '--'} (
                      {convertToPercent(renderData?.materialTotalPercent)})
                      <br />
                      <span className="small">
                        ${formatNumberToCommaS(renderData?.materialTotalBudget) ?? '--'} (
                        {convertToPercent(renderData?.materialTotalBudgetPercent)})
                      </span>
                    </Styled.TableContentLabel>
                    <Styled.TableContentLabel
                      className={`right-align ${getClassName(renderData?.laborTotal, renderData?.laborTotalBudget)}`}
                    >
                      ${formatNumberToCommaS(renderData?.laborTotal) ?? '--'} (
                      {convertToPercent(renderData?.laborTotalPercent)})
                      <br />
                      <span className="small">
                        ${formatNumberToCommaS(renderData?.laborTotalBudget) ?? '--'} (
                        {convertToPercent(renderData?.laborTotalBudgetPercent)})
                      </span>
                    </Styled.TableContentLabel>
                    <Styled.TableContentLabel
                      className={`right-align ${getClassName(renderData?.totalHrs, renderData?.totalBudgetHrs)}`}
                    >
                      {renderData?.totalHrs?.toFixed(2) ?? '--'}
                      <br />
                      <span className="small">{renderData?.totalBudgetHrs?.toFixed(2) ?? '--'}</span>
                    </Styled.TableContentLabel>
                    <Styled.TableContentLabel
                      className={`right-align ${getClassName(renderData?.totalBudgetScore, renderData?.totalScore)}`}
                    >
                      {renderData?.totalScore?.toFixed(1) ?? '--'}
                      <br />
                      <span className="small">{renderData?.totalBudgetScore?.toFixed(1) ?? '--'}</span>
                    </Styled.TableContentLabel>
                  </Styled.TableHeading>
                </Styled.TableContainer>
              ) : null}
            </Styled.TableMainContainer>
            <Styled.TableMainContainer></Styled.TableMainContainer>
          </>
        ) : (
          <SharedStyled.Text
            as={'h1'}
            style={{
              alignSelf: 'center',
              fontSize: '30px',
              fontFamily: Nue.bold,
            }}
          >
            No data found
          </SharedStyled.Text>
        )}
      </FlexCol>
    </div>
  )
}

export default ProductionReport
