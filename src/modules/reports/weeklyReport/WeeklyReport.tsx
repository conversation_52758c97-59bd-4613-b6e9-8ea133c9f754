import { Link, useNavigate, useParams } from 'react-router-dom'
import * as SharedStyled from '../../../styles/styled'
import * as Styled from './style'
import CustomSelect from '../../../shared/customSelect/CustomSelect'
import { Fragment, useEffect, useState } from 'react'
import { Table } from '../../../shared/table/Table'
import { getWeeklySalesReport } from '../../../logic/apis/report'
import { useSelector } from 'react-redux'
import {
  convertKeyToStr,
  convertStrToKey,
  dayjsFormat,
  extractDatesFromString,
  formatNumberToCommaS,
  getStageIdFromId,
  getWeeksFromDate,
  getWeeksInRange,
  isSuccess,
  truncateParagraph,
  formattedDateForRange,
  getDataFromLocalStorage,
  getEnumValue,
  renderClientName,
} from '../../../shared/helpers/util'
import { I_Stage } from '../../opportunity/components/assessmentForm/AssessmentForm'
import { getCheckpoint, getStages } from '../../../logic/apis/sales'
import { I_Checkpoint } from '../../crmSettings/CrmSettings'
import Button from '../../../shared/components/button/Button'
import { Nue, StageGroupEnum, StorageKey } from '../../../shared/helpers/constants'

const TypesKeys = {
  ['roof-repair']: 'Roof Repair',
  ['new-roof']: 'New Roof',
}

interface IColumnData {
  Header: string
  accessor: string
}

interface AnyKey {
  [key: string]: string
}

interface ISalesActions {
  num: number
  opps: any[]
  actions: any[]
  types: ISalesType
  salesPeople: any[]
}

interface ISalesType {
  id: string
  num: number
  opps: any[]
  actions: ISalesActions
  salesPeople: any[]
}

interface ISalesPeople {
  id: string
  stage: string
  num: any[]
  selfGen: any[]
  opps: any[]
  actions: any[]
  sold: any[]
  salesPersonName: string
}

const WeeklyReport = () => {
  const navigate = useNavigate()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [weekReport, setWeekReport] = useState('')
  const [weeksOptions, setWeeksOptions] = useState<string[]>([])
  const [checkpoints, setCheckpoints] = useState<I_Checkpoint[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [numData, setNumData] = useState<{ [key: string]: boolean }>({})
  const [toggleHeading, setToggleHeading] = useState<{ [key: string]: boolean }>({})

  const [data, setData] = useState<any>([])
  const [convData, setConvData] = useState<any>([])
  const [columns1, setColumns1] = useState<IColumnData[]>([])
  const [columns2, setColumns2] = useState<IColumnData[]>([])
  const [columnHeading, setColumnHeading] = useState<string[]>([])
  const [stages, setStages] = useState<I_Stage[]>([])

  // useEffect(() => {
  //   if (weeksOptions.length) {
  //     const params = new URLSearchParams(location.search)
  //     const paramWeekReport = params.get('weekReport')
  //     if (!paramWeekReport) {
  //       setWeekReport(weeksOptions[1])
  //       handleDateSelection(weeksOptions[1])
  //       const [startDate, endDate] = extractDatesFromString(weeksOptions[1])
  //       initFetch(startDate, endDate)
  //     }
  //   }
  // }, [weeksOptions])

  // useEffect(() => {
  //   if (weeksOptions.length) {
  //     setWeekReport(weeksOptions[1])
  //   }
  // }, [weeksOptions])

  const initFetch = async (startDate: Date, endDate: Date) => {
    setIsLoading(true)
    try {
      const response = await getWeeklySalesReport({
        endDate: endDate ? endDate.toISOString() : new Date().toISOString(),
        startDate: startDate
          ? startDate.toISOString()
          : new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      })

      if (isSuccess(response)) {
        // fetchCheckpoints()
        setIsLoading(false)
        // set data
        setData(response.data.data.report)
        setConvData(response.data.data.conversion)
        let cols: IColumnData[] = []

        cols.push({ Header: 'Type', accessor: 'type' })

        const weeksString = getWeeksFromDate(endDate, 4)
        // const weeksString = reportPeriods(startDate)
        console.log({ weeksString }, startDate)
        setColumnHeading(weeksString)
        weeksString.forEach((ws) => {
          cols.push({ Header: ws, accessor: ws })
        })
        setColumns1(cols)
        setColumns2([{ Header: 'Sales Person', accessor: 'salesPerson' }, ...cols.slice(1)])
      } else {
        throw new Error(response?.data?.message)
      }
    } catch (err) {
      setIsLoading(false)
      console.log('WeeklyReport initFetch err', err)
    }
  }

  useEffect(() => {
    getStagesData()
  }, [])

  const getStagesData = async () => {
    try {
      const stagesRes = await getStages({}, false)
      // const stagesRes = await getStages({ companyId: currentCompany._id }, false, operationsFlag)
      if (isSuccess(stagesRes)) {
        const { stage: stages } = stagesRes.data.data
        const formatStages: I_Stage[] = new Array(stages.length)
        stages.forEach((stage: I_Stage) => {
          formatStages[stage.sequence - 1] = stage
        })
        setStages(stages)
        // setStages(formatStages)
      } else throw new Error(stagesRes?.data?.message)
    } catch (err) {
      console.log('Err stages data', err)
    }
  }

  const getpageById = (id: string, oppId: string) => {
    const matchedObject: any = stages.find((item) => item._id === id)
    // If a matching object is found, return its name property
    if (matchedObject) {
      navigate(`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${oppId}`)
    } else {
      // If no matching object is found, return null or an appropriate default value
      return null
    }
  }

  const sortObjectKeysBySequence = (response1: any, response2: any) => {
    const keysArray = Object.keys(response2)
    // Sort keysArray based on the sequence order from response1
    keysArray.sort((a, b) => {
      const sequenceA = response1?.find((item: any) => item.name.toLowerCase() === a.toLowerCase())?.sequence || 0
      const sequenceB = response1?.find((item: any) => item.name.toLowerCase() === b.toLowerCase())?.sequence || 0
      return sequenceA - sequenceB
    })

    // Create a new object with sorted keys
    const sortedResponse2: any = {}
    keysArray?.forEach((key) => {
      sortedResponse2[key] = response2[key]
      // sortedResponse2[key] = response2[key.toLowerCase()]
    })

    const sortedData: any = {}
    keysArray.forEach((key) => {
      if (response2[key]) {
        sortedData[key] = response2[key]
        // const matchingResponse1 = response1.find((item: any) => item.name.toLowerCase() === key.toLowerCase())
        const matchingResponse1 = response1.find(
          (item: any) => item.name.replace(/\s+/g, '').toLowerCase() === key.replace(/\s+/g, '').toLowerCase()
        )
        if (matchingResponse1) {
          // Add symbol from response1 to response2
          sortedData[key].symbol = matchingResponse1.symbol
        }
      }
    })
    return sortedData
  }

  useEffect(() => {
    fetchCheckpoints()
  }, [])

  const fetchCheckpoints = async () => {
    try {
      const response = await getCheckpoint(false, StageGroupEnum.Sales)
      if (response?.statusCode === 200 || response?.status === 200) {
        let checkpointsArr: I_Checkpoint[] = response.data.data.checkpoint.map((item: any) => {
          // Remove spaces and convert name to lowercase
          const updatedName = item.name.replace(/\s/g, '').toLowerCase()

          // Return the updated object
          return {
            ...item,
            name: updatedName,
          }
        })
        setCheckpoints(checkpointsArr)
      } else {
        throw new Error(response?.data.message ?? 'Something went wrong!')
      }
    } catch (err) {
      console.log('CHECKPOINTS Fetch failed', err)
    }
  }

  useEffect(() => {
    // const now = new Date()
    // const twentyWeeksAgo = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 203)
    // const weeksObj = getWeeksInRange(twentyWeeksAgo, now)
    // const weeksOptionsArr = weeksObj
    //   .map((weekData) => `W${weekData.week}: ${weekData.startDate} to ${weekData.endDate}`)
    //   .reverse()
    const weeksOptionsArr = formattedDateForRange(true)
    console.log({ weeksOptionsArr })
    setWeeksOptions(weeksOptionsArr)
    setWeekReport(weeksOptionsArr[1])
    const params = new URLSearchParams(location.search)
    const paramWeekReport = params.get('weekReport')
    if (paramWeekReport) {
      if (paramWeekReport !== '') {
        setWeekReport(paramWeekReport)
        const [startDate, endDate] = extractDatesFromString(paramWeekReport)
        initFetch(startDate, endDate)
        // handleSubmitForm(paramWeekReport)
      }
    } else {
      // const [startDate, endDate] = extractDatesFromString(weeksOptionsArr[0])
      // initFetch(startDate, endDate)
    }
  }, [])

  const runReportClick = () => {
    handleDateSelection(weekReport)
    console.log({ weekReport })
    const [startDate, endDate] = extractDatesFromString(weekReport)
    initFetch(startDate, endDate)
  }

  const handleDateSelection = (weekReport: string) => {
    const params = new URLSearchParams()
    params.append('weekReport', weekReport)
    const newURL = `${window.location.pathname}?${params.toString()}`
    window.history.pushState({}, '', newURL)
    // Perform any other actions needed when start and end dates are selected
  }

  const toPascalCase = (inputString: string) => {
    // Split the input string into words
    const words = inputString.split(/\s+/)

    // Capitalize the first letter of each word and join them back
    const pascalCaseString = words
      .map((word: any) => {
        // Capitalize the first letter of the word and concatenate with the rest of the word
        return word.charAt(0).toUpperCase() + word.slice(1)
      })
      .join('')

    return pascalCaseString
  }

  const toggleTableData = (type: string) => {
    setNumData((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  const toggleCount = (type: string) => {
    setToggleHeading((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  return (
    <SharedStyled.Content
      maxWidth="1270px"
      width="100%"
      disableBoxShadow={true}
      noPadding={true}
      alignItems="flex-start"
      overflow=""
      // gap="40px"
    >
      <SharedStyled.FlexBox flexDirection="column" gap="10px" alignItems="center" width="100%">
        <SharedStyled.ContentHeader style={{ fontSize: '2.18rem' }} fontWeight="500" textAlign="center">
          Weekly Sales Report
        </SharedStyled.ContentHeader>
        <SharedStyled.HorizontalDivider />
        <SharedStyled.ContentHeader style={{ fontSize: '1.75rem', fontWeight: 500 }}>
          {weekReport}
        </SharedStyled.ContentHeader>
        <SharedStyled.FlexBox width="100%" padding="0 20px" gap="10px" maxWidth="1000px">
          <CustomSelect
            labelName="Choose a week..."
            error={false}
            value={weekReport}
            dropDownData={weeksOptions}
            setValue={setWeekReport}
            innerHeight="45px"
            margin="10px 0 0 0"
            stateName=""
          />

          <Button isLoading={isLoading} maxWidth="120px" onClick={runReportClick}>
            Run Report
          </Button>
        </SharedStyled.FlexBox>
      </SharedStyled.FlexBox>

      <>
        <SharedStyled.Text
          style={{ cursor: 'pointer' }}
          width="100%"
          margin="20px 0"
          textAlign="center"
          fontSize="1.25rem"
          fontWeight="600"
        >
          {' '}
          <span onClick={() => toggleCount('Conversion Rates')}>
            {!toggleHeading[`Conversion Rates`] ? <>&#9654;</> : <>&#9660;</>}
          </span>
          &nbsp; Conversion Rates
        </SharedStyled.Text>
        {
          <SharedStyled.FlexCol gap="10px">
            {Object?.entries?.(convData || {})?.length ? (
              <SharedStyled.FlexBox width="100%" justifyContent="space-around">
                <SharedStyled.Text fontWeight="600" fontSize="16px">
                  Lead Conversion: {convData?.['New Leads']?.['New Leads > New Opportunity']}%
                </SharedStyled.Text>

                <SharedStyled.Text fontWeight="600" fontSize="16px">
                  Opportunity Conversion: {convData?.['New Opportunity']?.['New Opportunity > Presentation']}%
                </SharedStyled.Text>

                <SharedStyled.Text fontWeight="600" fontSize="16px">
                  Sales Ratio: {convData?.['Presentation']?.['Presentation > Sale']}%
                </SharedStyled.Text>
              </SharedStyled.FlexBox>
            ) : null}

            {toggleHeading['Conversion Rates'] ? (
              <SharedStyled.FlexBox width="100%" justifyContent="center" alignItems="flex-start">
                {Object?.entries?.(convData || {})?.map(([stage, transitions]) => (
                  <SharedStyled.FlexCol>
                    {Object?.entries?.(transitions || {})?.map(([transition, value]) => (
                      <SharedStyled.Text fontSize="14px">
                        {transition}: {value}%
                      </SharedStyled.Text>
                    ))}
                  </SharedStyled.FlexCol>
                ))}
              </SharedStyled.FlexBox>
            ) : null}
          </SharedStyled.FlexCol>
        }
      </>

      {data['actions'] && (
        <>
          <Styled.ReportMainContainer>
            <Styled.ReportHeading pointer="pointer">{'Actions'}</Styled.ReportHeading>

            <>
              <Styled.TableContainer>
                <Styled.TableHeading>
                  <Styled.TableTitle>Type</Styled.TableTitle>
                  <Styled.TableTitle className="right-align">{columnHeading[0]}</Styled.TableTitle>
                  <Styled.TableTitle className="right-align">{columnHeading[1]}</Styled.TableTitle>
                  <Styled.TableTitle className="right-align">{columnHeading[2]}</Styled.TableTitle>
                  <Styled.TableTitle className="right-align">{columnHeading[3]}</Styled.TableTitle>
                </Styled.TableHeading>

                <Styled.TableContent>
                  <Styled.TableContentLabel>
                    <b>Actions Completed</b>
                  </Styled.TableContentLabel>
                  {data['actions']?.num?.map((val: any, index: number) => (
                    <Styled.TableContentLabel className="right-align" key={index}>
                      <b>{val}</b>{' '}
                      {/* <span style={{ color: 'blue' }}>
                        {data['actions']?.converted?.[index] > 0 && ` (${data['actions']?.converted?.[index]})`}
                      </span> */}
                      <span style={{ color: '#2fac2f' }}>
                        {data['actions']?.selfGen[index] > 0 && `(${data['actions']?.selfGen[index]})`}
                      </span>
                      <br />
                    </Styled.TableContentLabel>
                  ))}
                </Styled.TableContent>

                {data['actions']?.types?.map((value: any, index: number) => (
                  <Styled.TableContent key={index}>
                    <Styled.TableContentLabel>{value?.name}</Styled.TableContentLabel>
                    {value?.num?.map((val: any, index: number) => (
                      <Styled.TableContentLabel className="right-align" key={index}>
                        {val}
                        <br />
                        {value?.sold && (
                          <>{value?.sold?.[index] !== 0 ? <>${formatNumberToCommaS(value?.sold?.[index])}</> : '$--'}</>
                        )}
                      </Styled.TableContentLabel>
                    ))}
                  </Styled.TableContent>
                ))}
              </Styled.TableContainer>

              <Styled.ReportMainContainer>
                <Styled.ReportSubHeading>
                  {' '}
                  <span
                    onClick={() => {
                      toggleCount(`Sales-Person-Actions`)
                    }}
                  >
                    {!toggleHeading[`Sales-Person-Actions`] ? <>&#9654;</> : <>&#9660;</>}
                    &nbsp;Sales Person Totals
                  </span>
                </Styled.ReportSubHeading>
              </Styled.ReportMainContainer>

              {toggleHeading[`Sales-Person-Actions`] ? (
                <Styled.TableContainer>
                  <Styled.TableHeading>
                    <Styled.TableTitle>Sales Person</Styled.TableTitle>
                    <Styled.TableTitle className="right-align">{columnHeading[0]}</Styled.TableTitle>
                    <Styled.TableTitle className="right-align">{columnHeading[1]}</Styled.TableTitle>
                    <Styled.TableTitle className="right-align">{columnHeading[2]}</Styled.TableTitle>
                    <Styled.TableTitle className="right-align">{columnHeading[3]}</Styled.TableTitle>
                  </Styled.TableHeading>

                  {data['actions']?.salesPeople?.map((sales: any, index1: number) => (
                    <Styled.TableContent key={index1}>
                      <Styled.TableContentLabel>{sales?.salesPersonName}</Styled.TableContentLabel>
                      {sales.num.map((num: any, index: number) => (
                        <Styled.TableContentLabel className="right-align" key={index}>
                          {num}{' '}
                          {/* <span style={{ color: 'blue' }}>
                            {sales?.converted?.[index] > 0 && ` (${sales?.converted?.[index]})`}
                          </span> */}
                          <span style={{ color: '#2fac2f' }}>
                            {sales?.selfGen[index] > 0 && `(${sales?.selfGen[index]})`}
                          </span>
                        </Styled.TableContentLabel>
                      ))}
                    </Styled.TableContent>
                  ))}
                </Styled.TableContainer>
              ) : null}

              <Styled.TableFlexContainer>
                {data['actions']?.types?.map((type: any, index2: number) => (
                  <div style={{ width: '100%' }} key={index2}>
                    <Styled.ReportSubHeading>
                      {' '}
                      <span
                        onClick={() => {
                          toggleCount(`${type?.name} ${'Actions'}`)
                        }}
                      >
                        {!toggleHeading[`${type?.name} ${'Actions'}`] ? <>&#9654;</> : <>&#9660;</>}
                        &nbsp;{`${type?.name} ${'Actions'} ${`${type?.count}`}`}{' '}
                        <span style={{ color: '#2fac2f' }}>{type?.selfGen[0] > 0 && `(${type?.selfGen[0]})`}</span>
                      </span>
                    </Styled.ReportSubHeading>
                    {toggleHeading[`${type?.name} ${'Actions'}`] ? (
                      <Styled.TableContainer>
                        <Styled.TableHeading>
                          <Styled.TableTitle>Sales Person</Styled.TableTitle>

                          <Styled.TableTitle className="right-align">{columnHeading[0]}</Styled.TableTitle>
                          <Styled.TableTitle className="right-align">{columnHeading[1]}</Styled.TableTitle>
                          <Styled.TableTitle className="right-align">{columnHeading[2]}</Styled.TableTitle>
                          <Styled.TableTitle className="right-align">{columnHeading[3]}</Styled.TableTitle>
                        </Styled.TableHeading>

                        {type?.salesPeople?.map((sales: any, indexP: number) => (
                          <Fragment key={indexP}>
                            <Styled.TableContent>
                              <Styled.TableContentLabel>{sales?.salesPersonName}</Styled.TableContentLabel>
                              {sales?.num.map((num: any, index: number) => (
                                <Fragment key={index}>
                                  <Styled.TableContentLabel
                                    className="right-align"
                                    pointer={num > 0 ? 'pointer' : ''}
                                    onClick={() =>
                                      toggleTableData(
                                        num > 0 ? type?.name + 'actions' + sales?.salesPersonName + indexP + index : ''
                                      )
                                    }
                                  >
                                    {/* num > 0 &&  */}
                                    {num}
                                    <br />
                                    {sales?.sold && (
                                      <>
                                        {sales?.sold?.[index] !== 0 ? (
                                          <>${formatNumberToCommaS(sales?.sold?.[index])}</>
                                        ) : (
                                          '$--'
                                        )}
                                      </>
                                    )}
                                  </Styled.TableContentLabel>
                                </Fragment>
                              ))}
                            </Styled.TableContent>
                            {sales?.actions.map((order: any, index: number) => (
                              <Fragment key={index}>
                                {numData[type?.name + 'actions' + sales?.salesPersonName + indexP + index] && (
                                  <>
                                    {order
                                      ?.sort(
                                        (a: any, b: any) =>
                                          new Date(a.completedAt)?.getTime() - new Date(b.completedAt)?.getTime()
                                      )
                                      ?.map((action: any, index1: number) => {
                                        // const matchedObject: any = stages.find(
                                        //   (item) => item._id === getStageIdFromId(action?.oppId, sales?.opps[index])
                                        // )
                                        const matchedObject: any = stages.find((item) => item._id === action?.stage)
                                        return (
                                          <Fragment key={index1}>
                                            {/* {opp?.actions.map((action: any) => ( */}
                                            <>
                                              <Styled.TableContent
                                                width="90%"
                                                marginLeft="auto"
                                                color="#8f8484"
                                                as={Link}
                                                to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${
                                                  action?.oppId
                                                }`}
                                                key={action?.oppId}
                                                pointer="pointer"
                                                // onClick={() =>
                                                //   getpageById(
                                                //     getStageIdFromId(action?.oppId, sales?.opps[index]),
                                                //     action?.oppId
                                                //   )
                                                // }
                                                // justifyItems={'center'}
                                                column={'1fr 2fr 1fr 2fr'}
                                                selfgen={action?.selfGen}
                                              >
                                                <Styled.TableContentLabel>{action?.type}</Styled.TableContentLabel>
                                                <Styled.TableContentLabel>
                                                  {action?.body || '--'}
                                                </Styled.TableContentLabel>
                                                <Styled.TableContentLabel>
                                                  {dayjsFormat(action?.completedAt, 'M/D/YY')}
                                                </Styled.TableContentLabel>
                                                <Styled.TableContentLabel>
                                                  {action?.name || '--'}
                                                </Styled.TableContentLabel>
                                              </Styled.TableContent>
                                            </>
                                            {/* ))} */}
                                          </Fragment>
                                        )
                                      })}
                                  </>
                                )}
                              </Fragment>
                            ))}
                          </Fragment>
                        ))}
                      </Styled.TableContainer>
                    ) : null}
                  </div>
                ))}
              </Styled.TableFlexContainer>
            </>
          </Styled.ReportMainContainer>
        </>
      )}

      {Object.keys(sortObjectKeysBySequence(checkpoints, data))
        .filter((category) => !category.toLowerCase().includes('upcoming'))
        .map((category: any, index: number) => {
          return (
            category !== 'Lead Lost' &&
            category !== 'actions' && (
              <Fragment key={index}>
                <Styled.ReportMainContainer>
                  <Styled.ReportHeading>{category}</Styled.ReportHeading>

                  {
                    <>
                      <Styled.TableContainer>
                        <Styled.TableHeading>
                          <Styled.TableTitle>Type</Styled.TableTitle>
                          <Styled.TableTitle className="right-align">{columnHeading[0]}</Styled.TableTitle>
                          <Styled.TableTitle className="right-align">{columnHeading[1]}</Styled.TableTitle>
                          <Styled.TableTitle className="right-align">{columnHeading[2]}</Styled.TableTitle>
                          <Styled.TableTitle className="right-align">{columnHeading[3]}</Styled.TableTitle>
                        </Styled.TableHeading>

                        <Styled.TableContent>
                          <Styled.TableContentLabel>
                            {' '}
                            <b>Total {category}</b>{' '}
                          </Styled.TableContentLabel>
                          {data[category]?.num?.map((val: any, index: number) => (
                            <Styled.TableContentLabel className="right-align" key={index}>
                              <b>{val}</b>{' '}
                              {/* <span style={{ color: 'blue' }}>
                                {data[category]?.converted?.[index] > 0 && ` (${data[category]?.converted?.[index]})`}
                              </span> */}
                              <span style={{ color: '#2fac2f' }}>
                                {data[category]?.selfGen[index] > 0 && `(${data[category]?.selfGen[index]})`}
                              </span>
                              <br />
                              {data[category]?.sold && (
                                <>
                                  {data[category]?.sold?.[index] !== 0 ? (
                                    <b>${formatNumberToCommaS(data[category]?.sold?.[index])}</b>
                                  ) : (
                                    '$--'
                                  )}
                                </>
                              )}
                            </Styled.TableContentLabel>
                          ))}
                          {/* {data[category]?.sold?.map((val: any) => (
                    <Styled.TableContentLabel>${val !== 0 ? val : '--'}</Styled.TableContentLabel>
                  ))} */}
                        </Styled.TableContent>

                        {data[category]?.types?.map((value: any, index: number) => (
                          <Styled.TableContent key={index}>
                            <Styled.TableContentLabel>{value?.name}</Styled.TableContentLabel>
                            {value?.num?.map((val: any, indexC: number) => (
                              <Styled.TableContentLabel className="right-align" key={indexC}>
                                {val}{' '}
                                {/* <span style={{ color: 'blue' }}>
                                  {value?.converted?.[indexC] > 0 && ` (${value?.converted?.[indexC]})`}
                                </span> */}
                                <span style={{ color: '#2fac2f' }}>
                                  {value?.selfGen[indexC] > 0 && `(${value?.selfGen[indexC]})`}
                                </span>
                                <br />
                                {value?.sold && (
                                  <>
                                    {value?.sold?.[indexC] !== 0 ? (
                                      <>${formatNumberToCommaS(value?.sold?.[indexC])}</>
                                    ) : (
                                      '$--'
                                    )}
                                  </>
                                )}
                              </Styled.TableContentLabel>
                            ))}
                          </Styled.TableContent>
                        ))}
                      </Styled.TableContainer>

                      <Styled.ReportMainContainer>
                        <Styled.ReportSubHeading>
                          {' '}
                          <span
                            onClick={() => {
                              toggleCount(`Sales-Person-${category}`)
                            }}
                          >
                            {!toggleHeading[`Sales-Person-${category}`] ? <>&#9654;</> : <>&#9660;</>}
                            &nbsp;{data[category]?.sequence === 0 ? 'CSR' : 'Sales Person'} Totals
                          </span>
                        </Styled.ReportSubHeading>
                      </Styled.ReportMainContainer>

                      {toggleHeading[`Sales-Person-${category}`] ? (
                        <Styled.TableContainer>
                          <Styled.TableHeading>
                            <Styled.TableTitle>
                              {' '}
                              {data[category]?.sequence === 0 ? 'CSR' : 'Sales Person'}
                            </Styled.TableTitle>
                            <Styled.TableTitle className="right-align">{columnHeading[0]}</Styled.TableTitle>
                            <Styled.TableTitle className="right-align">{columnHeading[1]}</Styled.TableTitle>
                            <Styled.TableTitle className="right-align">{columnHeading[2]}</Styled.TableTitle>
                            <Styled.TableTitle className="right-align">{columnHeading[3]}</Styled.TableTitle>
                          </Styled.TableHeading>

                          {data[category]?.salesPeople?.map((sales: any, index: number) => (
                            <Styled.TableContent key={index}>
                              <Styled.TableContentLabel>{sales?.salesPersonName}</Styled.TableContentLabel>

                              {sales.num.map((num: any, index1: number) => (
                                <Styled.TableContentLabel className="right-align" key={index1 * 2}>
                                  {num}{' '}
                                  {/* <span style={{ color: 'blue' }}>
                                    {sales?.converted?.[index1] > 0 && ` (${sales?.converted?.[index1]})`}
                                  </span> */}
                                  <span style={{ color: '#2fac2f' }}>
                                    {sales?.selfGen[index1] > 0 && `(${sales?.selfGen[index1]})`}
                                  </span>
                                  <br />
                                  {sales?.sold && (
                                    <>
                                      {sales?.sold?.[index1] !== 0 ? (
                                        <>${formatNumberToCommaS(sales?.sold?.[index1])}</>
                                      ) : (
                                        '$--'
                                      )}
                                    </>
                                  )}
                                </Styled.TableContentLabel>
                              ))}
                            </Styled.TableContent>
                          ))}
                        </Styled.TableContainer>
                      ) : null}

                      <Styled.TableFlexContainer>
                        {data[category]?.types?.map((type: any, index: number) => (
                          <div style={{ width: '100%' }} key={index}>
                            <Styled.ReportSubHeading>
                              <span
                                onClick={() => {
                                  toggleCount(`${type?.name} ${category}`)
                                }}
                              >
                                {!toggleHeading[`${type?.name} ${category}`] ? <>&#9654;</> : <>&#9660;</>}
                                &nbsp;{`${type?.name} ${category} ${`${type?.count}`}`}{' '}
                                <span style={{ color: '#2fac2f' }}>
                                  {type?.selfGen[0] > 0 && `(${type?.selfGen[0]})`}
                                </span>
                              </span>
                              {/* {roof?.typeReplacement === true
                        ? `Roof Replacement ${category.toUpperCase()}`
                        : `Roof Repair ${category.toUpperCase()}`} */}
                            </Styled.ReportSubHeading>
                            {toggleHeading[`${type?.name} ${category}`] ? (
                              <Styled.TableContainer>
                                <Styled.TableHeading>
                                  <Styled.TableTitle>
                                    {' '}
                                    {data[category]?.sequence === 0 ? 'CSR' : 'Sales Person'}
                                  </Styled.TableTitle>

                                  <Styled.TableTitle className="right-align">{columnHeading[0]}</Styled.TableTitle>
                                  <Styled.TableTitle className="right-align">{columnHeading[1]}</Styled.TableTitle>
                                  <Styled.TableTitle className="right-align">{columnHeading[2]}</Styled.TableTitle>
                                  <Styled.TableTitle className="right-align">{columnHeading[3]}</Styled.TableTitle>
                                </Styled.TableHeading>
                                {type?.salesPeople?.map((sales: any, indexP: number) => (
                                  <Fragment key={indexP}>
                                    <Styled.TableContent>
                                      <Styled.TableContentLabel>{sales?.salesPersonName}</Styled.TableContentLabel>

                                      {sales?.num.map((num: any, index: number) => (
                                        <Fragment key={index}>
                                          <Styled.TableContentLabel
                                            className="right-align"
                                            pointer={num > 0 ? 'pointer' : ''}
                                            onClick={() =>
                                              toggleTableData(
                                                num > 0 &&
                                                  type?.name + category + sales?.salesPersonName + indexP + index
                                              )
                                            }
                                          >
                                            {num}{' '}
                                            {/* <span style={{ color: 'blue' }}>
                                              {sales?.converted?.[index] > 0 && ` (${sales?.converted?.[index]})`}
                                            </span> */}
                                            <span style={{ color: '#2fac2f' }}>
                                              {sales?.selfGen[index] > 0 && `(${sales?.selfGen[index]})`}
                                            </span>
                                            <br />
                                            {sales?.sold && (
                                              <>
                                                {sales?.sold?.[index] !== 0 ? (
                                                  <>${formatNumberToCommaS(sales?.sold?.[index])}</>
                                                ) : (
                                                  '$--'
                                                )}
                                              </>
                                            )}
                                          </Styled.TableContentLabel>
                                        </Fragment>
                                      ))}
                                    </Styled.TableContent>
                                    {sales?.opps?.map((order: any, index: number) => (
                                      <Fragment key={index}>
                                        {numData[type?.name + category + sales?.salesPersonName + indexP + index] && (
                                          <>
                                            {order
                                              ?.sort(
                                                (a: any, b: any) =>
                                                  new Date(a[data[category]?.symbol])?.getTime() -
                                                  new Date(b[data[category]?.symbol])?.getTime()
                                              )
                                              ?.map((opp: any, indexC: number) => {
                                                const matchedObject: any = stages.find(
                                                  (item) => item._id === opp?.stage
                                                )
                                                return (
                                                  <Fragment key={indexC}>
                                                    <Styled.TableContent
                                                      width="90%"
                                                      marginLeft="auto"
                                                      color={'#8f8484'}
                                                      // color={opp?.converted ? 'blue' : '#8f8484'}
                                                      as={Link}
                                                      to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${
                                                        opp?._id
                                                      }`}
                                                      key={opp?._id}
                                                      pointer="pointer"
                                                      // onClick={() => getpageById(opp?.stage, opp?._id)}
                                                      // justifyItems={'center'}
                                                      column={'3fr 1fr repeat(2,2fr) 1fr'}
                                                      selfgen={opp?.selfGen}
                                                    >
                                                      <Styled.TableContentLabel>
                                                        {data[category]?.sequence === 0
                                                          ? `${opp?.lastName ? `${opp?.lastName}, ` : ''}${
                                                              opp?.firstName
                                                            }`
                                                          : `${
                                                              opp?.contactId?.isBusiness
                                                                ? opp?.contactId?.businessName
                                                                : opp?.contactId?.fullName
                                                            }`}

                                                        {/* {opp?.lastName}, {opp?.firstName} */}
                                                      </Styled.TableContentLabel>
                                                      <Styled.TableContentLabel>
                                                        {dayjsFormat(opp[data[category]?.symbol], 'M/D/YY')}
                                                      </Styled.TableContentLabel>
                                                      <Styled.TableContentLabel>
                                                        {opp?.city || '--'}
                                                      </Styled.TableContentLabel>
                                                      <Styled.TableContentLabel>
                                                        {opp?.leadSource || '--'}
                                                      </Styled.TableContentLabel>
                                                      {data[category]?.symbol === 'saleDate' ? (
                                                        <Styled.TableContentLabel>
                                                          {opp?.soldValue || '--'}
                                                        </Styled.TableContentLabel>
                                                      ) : (
                                                        <Styled.TableContentLabel>
                                                          {opp?.leadCost || '--'}
                                                        </Styled.TableContentLabel>
                                                      )}
                                                    </Styled.TableContent>
                                                  </Fragment>
                                                )
                                              })}
                                          </>
                                        )}
                                      </Fragment>
                                    ))}
                                  </Fragment>
                                ))}

                                <SharedStyled.SubTableContainer margin="0 0 0 auto" width="95%">
                                  {type?.leadSources?.map((val: any, index: number) => {
                                    return (
                                      <Styled.TableContent key={index}>
                                        <Styled.TableContentLabel>{val?.id}</Styled.TableContentLabel>
                                        <Styled.TableContentLabel>{val?.num}</Styled.TableContentLabel>
                                        <Styled.TableContentLabel>
                                          ${formatNumberToCommaS(val?.cost)}
                                        </Styled.TableContentLabel>
                                      </Styled.TableContent>
                                    )
                                  })}
                                </SharedStyled.SubTableContainer>
                              </Styled.TableContainer>
                            ) : null}
                          </div>
                        ))}
                      </Styled.TableFlexContainer>
                    </>
                  }
                </Styled.ReportMainContainer>
              </Fragment>
            )
          )
        })}
      {/* {data?.lead_lost.map((category: any, index: number) => { */}
      {/* return ( */}

      {data['Lead Lost'] && (
        <>
          <Styled.ReportMainContainer>
            <Styled.ReportHeading> Lead Lost</Styled.ReportHeading>
            {
              <>
                <Styled.TableContainer>
                  <Styled.TableHeading>
                    <Styled.TableTitle>Type</Styled.TableTitle>
                    <Styled.TableTitle className="right-align">{columnHeading[0]}</Styled.TableTitle>
                    <Styled.TableTitle className="right-align">{columnHeading[1]}</Styled.TableTitle>
                    <Styled.TableTitle className="right-align">{columnHeading[2]}</Styled.TableTitle>
                    <Styled.TableTitle className="right-align">{columnHeading[3]}</Styled.TableTitle>
                  </Styled.TableHeading>

                  <Styled.TableContent>
                    <Styled.TableContentLabel>
                      <b>Total Lead Lost</b>
                    </Styled.TableContentLabel>
                    {data['Lead Lost']?.num?.map((val: any, index: number) => (
                      <Styled.TableContentLabel className="right-align" key={index}>
                        <b>{val}</b>
                        <br />
                      </Styled.TableContentLabel>
                    ))}
                  </Styled.TableContent>

                  {data['Lead Lost']?.types?.map((value: any, index: number) => (
                    <Styled.TableContent key={index}>
                      <Styled.TableContentLabel>{value?.name}</Styled.TableContentLabel>
                      {value?.num?.map((val: any, index: number) => (
                        <Styled.TableContentLabel className="right-align" key={index}>
                          {val}{' '}
                          {/* <span style={{ color: 'blue' }}>
                            {data['Lead Lost']?.converted?.[index] > 0 && ` (${data['Lead Lost']?.converted?.[index]})`}
                          </span> */}
                          <span style={{ color: '#2fac2f' }}>
                            {data['Lead Lost']?.selfGen[index] > 0 && `(${data['Lead Lost']?.selfGen[index]})`}
                          </span>
                          <br />
                          {value?.sold && (
                            <>
                              {value?.sold?.[index] !== 0 ? <>${formatNumberToCommaS(value?.sold?.[index])}</> : '$--'}
                            </>
                          )}
                        </Styled.TableContentLabel>
                      ))}
                    </Styled.TableContent>
                  ))}
                </Styled.TableContainer>

                <Styled.ReportMainContainer>
                  <Styled.ReportSubHeading>
                    {' '}
                    <span
                      onClick={() => {
                        toggleCount(`Sales-Person-Lead Lost`)
                      }}
                    >
                      {!toggleHeading[`Sales-Person-Lead Lost`] ? <>&#9654;</> : <>&#9660;</>}
                      &nbsp;Sales Person Totals
                    </span>
                  </Styled.ReportSubHeading>
                </Styled.ReportMainContainer>

                {toggleHeading[`Sales-Person-Lead Lost`] ? (
                  <Styled.TableContainer>
                    <Styled.TableHeading>
                      <Styled.TableTitle> Sales Person</Styled.TableTitle>
                      <Styled.TableTitle className="right-align">{columnHeading[0]}</Styled.TableTitle>
                      <Styled.TableTitle className="right-align">{columnHeading[1]}</Styled.TableTitle>
                      <Styled.TableTitle className="right-align">{columnHeading[2]}</Styled.TableTitle>
                      <Styled.TableTitle className="right-align">{columnHeading[3]}</Styled.TableTitle>
                    </Styled.TableHeading>

                    {data['Lead Lost']?.salesPeople?.map((sales: any, index1: number) => (
                      <Styled.TableContent key={index1}>
                        <Styled.TableContentLabel>{sales?.salesPersonName}</Styled.TableContentLabel>
                        {sales.num.map((num: any, index: number) => (
                          <Styled.TableContentLabel className="right-align" key={index}>
                            {num}{' '}
                            {/* <span style={{ color: 'blue' }}>
                              {sales?.converted?.[index] > 0 && ` (${sales?.converted?.[index]})`}
                            </span> */}
                            <span style={{ color: '#2fac2f' }}>
                              {sales?.selfGen[index] > 0 && `(${sales?.selfGen[index]})`}
                            </span>
                          </Styled.TableContentLabel>
                        ))}
                      </Styled.TableContent>
                    ))}
                  </Styled.TableContainer>
                ) : null}

                <Styled.TableFlexContainer>
                  {data['Lead Lost']?.types?.map((type: any, index2: number) => (
                    <div style={{ width: '100%' }} key={index2}>
                      <Styled.ReportSubHeading>
                        <span
                          onClick={() => {
                            toggleCount(`${type?.name} ${'Lead Lost'}`)
                          }}
                        >
                          {!toggleHeading[`${type?.name} ${'Lead Lost'}`] ? <>&#9654;</> : <>&#9660;</>}
                          &nbsp;{`${type?.name} ${'Lead Lost'} ${`${type?.count}`}`}{' '}
                          <span style={{ color: '#2fac2f' }}>{type?.selfGen[0] > 0 && `(${type?.selfGen[0]})`}</span>
                        </span>
                      </Styled.ReportSubHeading>
                      {toggleHeading[`${type?.name} ${'Lead Lost'}`] ? (
                        <Styled.TableContainer>
                          <Styled.TableHeading>
                            <Styled.TableTitle> Sales Person</Styled.TableTitle>

                            <Styled.TableTitle className="right-align">{columnHeading[0]}</Styled.TableTitle>
                            <Styled.TableTitle className="right-align">{columnHeading[1]}</Styled.TableTitle>
                            <Styled.TableTitle className="right-align">{columnHeading[2]}</Styled.TableTitle>
                            <Styled.TableTitle className="right-align">{columnHeading[3]}</Styled.TableTitle>
                          </Styled.TableHeading>
                          {type?.salesPeople?.map((sales: any, indexP: number) => (
                            <Fragment key={indexP}>
                              <Styled.TableContent>
                                <Styled.TableContentLabel>{sales?.salesPersonName}</Styled.TableContentLabel>
                                {sales?.num.map((num: any, index: number) => (
                                  <Fragment key={index}>
                                    <Styled.TableContentLabel
                                      className="right-align"
                                      pointer={num > 0 ? 'pointer' : ''}
                                      onClick={() =>
                                        toggleTableData(
                                          num > 0
                                            ? type?.name + 'Lead Lost' + sales?.salesPersonName + indexP + index
                                            : ''
                                        )
                                      }
                                    >
                                      {/* num > 0 &&  */}
                                      {num}
                                      {/* <span style={{ color: 'blue' }}>
                                        {sales?.converted?.[index] > 0 && ` (${sales?.converted?.[index]})`}
                                      </span> */}
                                      <span style={{ color: '#2fac2f' }}>
                                        {sales?.selfGen[index] > 0 && `(${sales?.selfGen[index]})`}
                                      </span>
                                      <br />
                                      {sales?.sold && (
                                        <>
                                          {sales?.sold?.[index] !== 0 ? (
                                            <>${formatNumberToCommaS(sales?.sold?.[index])}</>
                                          ) : (
                                            '$--'
                                          )}
                                        </>
                                      )}
                                    </Styled.TableContentLabel>
                                  </Fragment>
                                ))}
                              </Styled.TableContent>
                              {sales?.opps
                                ?.sort(
                                  (a: any, b: any) => new Date(a?.oppDate)?.getTime() - new Date(b?.oppDate)?.getTime()
                                )
                                ?.map((order: any, index: number) => (
                                  <Fragment key={index}>
                                    {numData[type?.name + 'Lead Lost' + sales?.salesPersonName + indexP + index] && (
                                      <>
                                        {order?.map((opp: any, idx: number) => {
                                          const matchedObject: any = stages.find((item) => item._id === opp?.stage)
                                          return (
                                            <Fragment key={idx}>
                                              {
                                                <Styled.TableContent
                                                  width="90%"
                                                  marginLeft="auto"
                                                  color="#8f8484"
                                                  as={Link}
                                                  to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${
                                                    opp?._id
                                                  }`}
                                                  key={opp?._id}
                                                  pointer="pointer"
                                                  // onClick={() => getpageById(opp?.stage, opp?._id)}
                                                  // justifyItems={'center'}
                                                  column={'1fr repeat(3,2fr)'}
                                                  selfgen={opp?.selfGen}
                                                >
                                                  <Styled.TableContentLabel>
                                                    {renderClientName(
                                                      opp?.isBusiness,
                                                      opp?.fullName,
                                                      opp?.businessName
                                                    )}
                                                  </Styled.TableContentLabel>
                                                  <Styled.TableContentLabel>
                                                    {dayjsFormat(opp?.oppDate, 'M/D/YY')}
                                                  </Styled.TableContentLabel>
                                                  <Styled.TableContentLabel>
                                                    {opp?.leadSource || '--'}
                                                  </Styled.TableContentLabel>
                                                  <Styled.TableContentLabel>
                                                    <SharedStyled.TooltipContainer
                                                      positionLeft="0"
                                                      positionBottom="0"
                                                      positionLeftDecs="40px"
                                                      positionBottomDecs="25px"
                                                    >
                                                      <span className="tooltip-content">{opp?.lostReason}</span>
                                                      {truncateParagraph(opp?.lostReason, 15)}
                                                    </SharedStyled.TooltipContainer>
                                                  </Styled.TableContentLabel>
                                                </Styled.TableContent>
                                              }
                                            </Fragment>
                                          )
                                        })}
                                      </>
                                    )}
                                  </Fragment>
                                ))}
                            </Fragment>
                          ))}
                        </Styled.TableContainer>
                      ) : null}
                    </div>
                  ))}
                </Styled.TableFlexContainer>
              </>
            }
          </Styled.ReportMainContainer>
        </>
      )}

      {/* ) */}
      {/* })} */}
      <Styled.ReportMainContainer>
        <Styled.ReportHeading>Upcoming Jobs: {data?.upcoming?.num || 0}</Styled.ReportHeading>
        <Styled.ReportHeading fontSize="21px" style={{ fontFamily: Nue.regular, margin: '10px 0 20px 0' }}>
          Total Volume: ${formatNumberToCommaS(data?.upcomingVol) || 0}
        </Styled.ReportHeading>

        {data?.upcoming?.types?.map((type: any, index: number) => (
          <Styled.TableFlexContainer key={index} gridColumn="1fr">
            <div style={{ width: '100%' }}>
              <Styled.ReportSubHeading className="upcoming">
                {type?.name} Upcoming: {type?.num}
              </Styled.ReportSubHeading>
              <Styled.ReportSubHeading style={{ fontWeight: 500, margin: '10px 0 0 0' }} className="upcoming">
                Total Volume: ${formatNumberToCommaS(type?.vol) || 0}
              </Styled.ReportSubHeading>
              <Styled.TableContainer>
                <Styled.TableHeading column="repeat(7,1fr)">
                  <Styled.TableTitle>Client name</Styled.TableTitle>
                  <Styled.TableTitle>Signed</Styled.TableTitle>
                  <Styled.TableTitle>City</Styled.TableTitle>
                  <Styled.TableTitle>Source</Styled.TableTitle>
                  <Styled.TableTitle className="right-align">Volume</Styled.TableTitle>
                  <Styled.TableTitle></Styled.TableTitle>
                  <Styled.TableTitle>Note</Styled.TableTitle>
                </Styled.TableHeading>
                {type?.opps
                  ?.sort((a: any, b: any) => {
                    const dateA = a?.saleDate ? new Date(a?.saleDate)?.toISOString()?.split('T')[0] : ''
                    const dateB = b?.saleDate ? new Date(b?.saleDate)?.toISOString()?.split('T')[0] : ''

                    if (dateA < dateB) return -1
                    if (dateA > dateB) return 1

                    // If the dates are the same or missing, sort by last name
                    return (a?.lastName || '').localeCompare(b?.lastName || '')
                  })
                  ?.map((value: any) => {
                    const matchedObject: any = stages.find((item) => item._id === value?.stage)

                    return (
                      <Styled.TableContent
                        as={Link}
                        to={`/${getEnumValue(matchedObject?.stageGroup)}/opportunity/${value?._id}`}
                        key={value?._id}
                        column="repeat(7,1fr)"
                        pointer="pointer"
                        selfgen={value?.selfGen}
                        // onClick={() => getpageById(value?.stage, value?._id)}
                      >
                        <Styled.TableContentLabel>
                          {renderClientName(
                            value?.contactId?.isBusiness,
                            value?.contactId?.fullName,
                            value?.contactId?.businessName
                          )}
                        </Styled.TableContentLabel>
                        <Styled.TableContentLabel>{dayjsFormat(value?.saleDate, 'M/D/YY')}</Styled.TableContentLabel>
                        <Styled.TableContentLabel>{value?.city}</Styled.TableContentLabel>
                        <Styled.TableContentLabel>{value?.leadSource}</Styled.TableContentLabel>
                        <Styled.TableContentLabel className="right-align">
                          ${formatNumberToCommaS(value?.soldValue) || 0}
                        </Styled.TableContentLabel>
                        <Styled.TableContentLabel></Styled.TableContentLabel>
                        <Styled.TableContentLabel>
                          <SharedStyled.TooltipContainer
                            positionLeft="0"
                            positionBottom="0"
                            positionLeftDecs="40px"
                            positionBottomDecs="25px"
                          >
                            <span className="tooltip-content">{value?.jobNote}</span>
                            {truncateParagraph(value?.jobNote, 15)}
                          </SharedStyled.TooltipContainer>
                        </Styled.TableContentLabel>
                      </Styled.TableContent>
                    )
                  })}
              </Styled.TableContainer>
            </div>
          </Styled.TableFlexContainer>
        ))}
      </Styled.ReportMainContainer>
      <div>
        <Button onClick={() => navigate(`/reports`)} type="button">
          Back To Dashboard
        </Button>
      </div>
    </SharedStyled.Content>
  )
}

export default WeeklyReport
