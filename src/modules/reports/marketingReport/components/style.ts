import styled from 'styled-components'
import { Nue } from '../../../../shared/helpers/constants'

export const Group = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`

export const Label = styled.label`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-family: ${Nue.regular};
  font-size: 16px;
`

export const RadioInput = styled.input.attrs({ type: 'radio' })`
  accent-color: black;
  width: 16px;
  height: 16px;
`
