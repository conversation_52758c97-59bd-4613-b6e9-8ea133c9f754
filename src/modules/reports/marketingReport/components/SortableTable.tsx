import React, { useState } from 'react'
import styled from 'styled-components'

// Column definition type
export interface Column {
  label: string
  key: string
  tooltip?: boolean
  link?: boolean
}

// Props interface
interface SortableTableProps {
  data: Record<string, any>[]
  columns: Column[]
  onLinkClick?: (value: any, key: string) => void
  defaultSort?: { key: string; direction: 'asc' | 'desc' }
  className?: string
}

const SortableTable: React.FC<SortableTableProps> = ({
  data = [],
  columns = [],
  onLinkClick = (value) => alert(`Show report for ${value}`),
  defaultSort = { key: null, direction: 'asc' },
  className,
}) => {
  const sortedData = [...data]

  return (
    <Table className={className}>
      <thead>
        <tr>
          {columns.map((col) => (
            <Th key={col.key}>
              <ThContent>{col.label}</ThContent>
            </Th>
          ))}
        </tr>
      </thead>
      <tbody>
        {sortedData.map((row, idx) => (
          <tr key={idx}>
            {columns.map((col) => (
              <Td key={col.key} title={col.tooltip ? row[col.key] : undefined}>
                {col.link ? (
                  <Link onClick={() => onLinkClick(row[col.key], col.key)}>{row[col.key]}</Link>
                ) : (
                  row[col.key]
                )}
              </Td>
            ))}
          </tr>
        ))}
      </tbody>
    </Table>
  )
}

export default SortableTable

export const Table = styled.table`
  border-collapse: collapse;
  width: 100%;
  font-family: Arial, sans-serif;
  font-size: 14px;
`

export const Th = styled.th`
  text-align: left;
  padding: 10px;
  background-color: #f9f9f9;
  cursor: pointer;
  color: teal;
  border-bottom: 2px solid #ddd;
`

export const Td = styled.td`
  padding: 10px;
  border-bottom: 1px solid #eee;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`

export const Link = styled.span`
  color: #4a90e2;
  cursor: pointer;
  text-decoration: underline;
  &:hover {
    opacity: 0.8;
  }
`

const ThContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`

const SortIndicator = styled.span<{ visible: boolean }>`
  visibility: ${(props) => (props.visible ? 'visible' : 'hidden')};
  margin-left: 5px;
  display: inline-block;
  width: 20px;
  text-align: center;
`
