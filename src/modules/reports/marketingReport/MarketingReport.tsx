import { Form, Formik } from 'formik'
import * as SharedStyled from '../../../styles/styled'
import { RadioContainer, MarketingReportContainer, MetricCard, LabelText, Value, Cost, MetricsCont } from './style'
import { SharedDate } from '../../../shared/date/SharedDate'
import * as Styled from '../productionReport/style'
import Button from '../../../shared/components/button/Button'
import { useEffect, useMemo, useState } from 'react'
import { getMarketingReport } from '../../../logic/apis/report'
import { Root, staticData } from './data'
import {
  dayjsFormat,
  formatCurrencyNumber,
  formatDollarAmount,
  getFormattedRatio,
  hasValues,
} from '../../../shared/helpers/util'
import ReactApexChart from 'react-apexcharts'
import { ApexOptions } from 'apexcharts'
import { Nue } from '../../../shared/helpers/constants'
import SortableTable from './components/SortableTable'
import RadioGroup from './components/RadioGroup'

const srcMap = {
  Channel: 'channels',
  'Lead Source': 'leadSource',
  Campaign: 'campaign',
}

const tableData = [
  { channel: 'Facebook', leads: 120, cost: 1500, cpl: 12.5, sales: 15, conversion: '12.5%' },
  { channel: 'Google', leads: 85, cost: 2000, cpl: 23.5, sales: 10, conversion: '11.8%' },
  { channel: 'Referral', leads: 65, cost: 500, cpl: 7.7, sales: 12, conversion: '18.5%' },
]

const tableColumns = [
  { label: 'Channel', key: 'channel', link: true },
  { label: 'Leads', key: 'leads' },
  { label: 'Cost ($)', key: 'cost' },
  { label: 'CPL', key: 'cpl', tooltip: true },
  { label: 'Sales', key: 'sales' },
  { label: 'Conversion', key: 'conversion' },
]

const handleLinkClick = (value: any, key: string) => {
  console.log(`Clicked on ${value} with key ${key}`)
}

const MarketingReport = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [buttonCall, setbuttonCall] = useState(false)
  const [initialValues, setInitialValues] = useState({
    startDate: '',
    endDate: '',
  })
  const [selectedLabel, setSelectedLabel] = useState('')

  const [marketingReportData, setMarketingReportData] = useState<Root>()

  const [selectedSrc, setSelectedSrc] = useState('Channel')
  const [selectedType, setSelectedType] = useState('Volume')

  const srcOptions = ['Channel', 'Lead Source', 'Campaign']
  const typeOptions = ['Volume', 'Sales', 'Leads']
  const [showOppsData, setShowOppsData] = useState(false)

  const getSeriesData = (selectedType: string) => {
    console.log('selectedType===[log]===>', { selectedType, a: marketingReportData?.leadSource })
    switch (selectedType) {
      case 'Volume':
        return marketingReportData?.leadSource?.map((item: any) => item?.totalBudget)
      case 'Sales':
        return marketingReportData?.leadSource?.map((item: any) => item?.checkpointCounts?.Sale)
      case 'Leads':
        return marketingReportData?.leadSource?.map((item: any) => item?.checkpointCounts?.['New Lead'])
    }
  }

  const getChartOptions = (seriesData: number[], labels: string[]) => {
    console.log('chart seriesData===[log]===>', seriesData)
    return {
      series: seriesData,

      options: {
        labels: labels,
        chart: {
          events: {
            dataPointSelection(_e: any, _chart: any, config: any) {
              const label = labels[config.dataPointIndex]
              setSelectedLabel(label)
            },
          },
        },

        legend: {
          position: 'top', // Move legend below the chart
          horizontalAlign: 'left', // Arrange items horizontally
          fontSize: '16px',
          itemMargin: {
            horizontal: 16,
            // vertical: 5,
          },
        },

        // responsive: [
        //   {
        //     breakpoint: 480,
        //     options: {
        //       chart: {
        //         width: 200,
        //       },
        //       legend: {
        //         position: 'top',
        //       },
        //     },
        //   },
        // ],
      },
    } as ApexOptions
  }

  const calculatedLeadSrcValues = useMemo(() => {
    if (selectedSrc === 'Lead Source') {
      return marketingReportData?.leadSource?.reduce(
        (acc, item) => {
          return {
            Volume: acc?.Volume + item?.totalBudget,
            Sales: acc?.Sales + item?.checkpointCounts?.Sale,
            Leads: acc?.Leads + item?.checkpointCounts?.['New Lead'],
            leadSourceNames: [...acc?.leadSourceNames, item?.leadSourceName],
          }
        },
        {
          Volume: 0,
          Sales: 0,
          Leads: 0,
          leadSourceNames: [],
        }
      )
    }
  }, [marketingReportData, selectedSrc])

  const [chartOptions, setChartOptions] = useState({})

  console.log('chartOptions===[log]===>', { chartOptions, marketingReportData })

  useEffect(() => {
    setMarketingReportData(staticData)
  }, [])

  useEffect(() => {
    if (calculatedLeadSrcValues?.leadSourceNames?.length) {
      const chartData = getChartOptions(getSeriesData(selectedType), calculatedLeadSrcValues?.leadSourceNames)
      console.log('chartData===[log]===>', chartData)
      setChartOptions(chartData)
    }
  }, [calculatedLeadSrcValues, selectedType])

  const handleSubmitForm = async (values: any) => {
    const paramsData: any = {
      endDate: values.endDate,
      startDate: values.startDate,
    }
    try {
      setIsLoading(true)
      const response = await getMarketingReport(paramsData)
      setMarketingReportData(response?.data?.report)
    } catch (error) {
      console.log('Error', error)
    } finally {
      setIsLoading(false)
    }
  }

  const selectedLsData = marketingReportData?.leadSource?.find((item: any) => item?.leadSourceName === selectedLabel)

  const convData = selectedLsData?.conversion

  const metrics = useMemo(() => {
    return [
      { label: 'Visits', value: null, cost: null },
      { label: 'Leads', value: selectedLsData?.checkpointCounts?.['New Lead'], cost: '$5.00' },
      { label: 'Opps', value: selectedLsData?.checkpointCounts?.['New Opportunity'], cost: '$6.25' },
      { label: 'NA', value: selectedLsData?.checkpointCounts?.['Needs Assessment'], cost: '$7.14' },
      { label: 'Pres', value: selectedLsData?.checkpointCounts?.Presentation, cost: '$8.33' },
      { label: 'Sales', value: selectedLsData?.checkpointCounts?.Sale, cost: '$25.00' },
      {
        label: 'Volume',
        value: selectedLsData?.totalBudget,
        cost: getFormattedRatio(100, 25),
        isValueCurrency: true,
        ratioLabel: 'Volume : Cost',
      },
      {
        label: 'Gross Profit',
        value: selectedLsData?.totalGrossProfit,
        cost: getFormattedRatio(1000, 10),
        isValueCurrency: true,
        ratioLabel: 'GP : Volume',
      },
    ]
  }, [selectedLabel])

  return (
    <MarketingReportContainer>
      <SharedStyled.SectionTitle margin="0 0 20px 0">Marketing Report</SharedStyled.SectionTitle>
      <Formik enableReinitialize={true} initialValues={initialValues} onSubmit={handleSubmitForm}>
        {({ values, setFieldValue, touched, errors }) => (
          <Form>
            <SharedStyled.FlexBox width="100%" gap="10px" alignItems="flex-end" alignItemsM="start" column="column">
              <div>
                <Styled.DateLabel>Date Start:</Styled.DateLabel>
                <div>
                  <SharedDate
                    value={values.startDate}
                    labelName="From"
                    stateName="startDate"
                    error={touched.startDate && errors.startDate ? true : false}
                    setFieldValue={setFieldValue}
                  />
                </div>
              </div>
              <div>
                <Styled.DateLabel>Date End:</Styled.DateLabel>
                <div>
                  <SharedDate
                    value={values.endDate}
                    labelName="To"
                    stateName="endDate"
                    error={touched.endDate && errors.endDate ? true : false}
                    min={values.startDate}
                    setFieldValue={setFieldValue}
                  />
                </div>
              </div>
              <Styled.KPIButtonContainer>
                <Button
                  type="submit"
                  isLoading={isLoading}
                  onClick={() => setbuttonCall(true)}
                  disabled={values.endDate === '' || values.startDate === ''}
                  width="max-content"
                  height="52px"
                >
                  Run Report
                </Button>
              </Styled.KPIButtonContainer>
            </SharedStyled.FlexBox>
          </Form>
        )}
      </Formik>

      <RadioContainer>
        <RadioGroup name="src" options={srcOptions} selected={selectedSrc} onChange={setSelectedSrc} />
        <RadioGroup name="type" options={typeOptions} selected={selectedType} onChange={setSelectedType} />
      </RadioContainer>

      {selectedSrc === 'Lead Source' ? (
        <div>
          <h4>
            Total {selectedType}:&nbsp;
            {selectedType === 'Volume'
              ? formatDollarAmount(calculatedLeadSrcValues[selectedType as keyof typeof calculatedLeadSrcValues])
              : calculatedLeadSrcValues[selectedType as keyof typeof calculatedLeadSrcValues]}
          </h4>
        </div>
      ) : null}

      <div id="chart">
        {hasValues(chartOptions.series) && (
          <ReactApexChart options={chartOptions.options} series={chartOptions.series} type="pie" height={500} />
        )}
      </div>

      <SharedStyled.FlexCol gap="10px">
        {Object?.entries?.(convData || {})?.length ? (
          <SharedStyled.FlexBox width="100%" justifyContent="space-around">
            <SharedStyled.Text fontWeight="600" fontSize="16px">
              Lead Conversion: {convData?.['New Leads']?.['New Leads > New Opportunity']}%
            </SharedStyled.Text>

            <SharedStyled.Text fontWeight="600" fontSize="16px">
              Opportunity Conversion: {convData?.['New Opportunity']?.['New Opportunity > Presentation']}%
            </SharedStyled.Text>

            <SharedStyled.Text fontWeight="600" fontSize="16px">
              Sales Ratio: {convData?.['Presentation']?.['Presentation > Sale']}%
            </SharedStyled.Text>
          </SharedStyled.FlexBox>
        ) : null}

        <SharedStyled.FlexBox width="100%" justifyContent="center" alignItems="flex-start">
          {Object?.entries?.(convData || {})?.map(([stage, transitions], idx) => (
            <SharedStyled.FlexCol key={idx}>
              {Object?.entries?.(transitions || {})?.map(([transition, value]) => (
                <SharedStyled.Text fontSize="14px" key={transition}>
                  {transition}: {value}%
                </SharedStyled.Text>
              ))}
            </SharedStyled.FlexCol>
          ))}
        </SharedStyled.FlexBox>
      </SharedStyled.FlexCol>

      <MetricsCont>
        {selectedLsData &&
          metrics.map((metric, idx) => (
            <MetricCard key={idx} onClick={() => setShowOppsData(!showOppsData)} style={{ cursor: 'pointer' }}>
              <LabelText>{metric.label}</LabelText>
              {metric.isValueCurrency ? (
                <>
                  <SharedStyled.TooltipContainer
                    positionLeft="0"
                    positionBottom="0"
                    positionLeftDecs="40px"
                    positionBottomDecs="40px"
                  >
                    <span className="tooltip-content">${metric.value?.toLocaleString() || '--'}</span>
                    <Value>${formatCurrencyNumber(metric.value)}</Value>
                  </SharedStyled.TooltipContainer>
                </>
              ) : (
                <Value>{metric.value ?? '--'}</Value>
              )}

              {metric.isValueCurrency ? (
                <div>
                  <SharedStyled.TooltipContainer
                    positionLeft="0"
                    positionBottom="0"
                    positionLeftDecs="40px"
                    positionBottomDecs="40px"
                  >
                    <span className="tooltip-content">{metric.ratioLabel}</span>
                    <Cost>{metric.cost ?? '--'}</Cost>
                  </SharedStyled.TooltipContainer>
                </div>
              ) : (
                <Cost>{metric.cost ?? '--'}</Cost>
              )}
            </MetricCard>
          ))}
      </MetricsCont>

      <SharedStyled.FlexRow margin="30px 0 0 0">
        {showOppsData && (
          <SortableTable
            data={tableData}
            columns={tableColumns}
            onLinkClick={handleLinkClick}
            defaultSort={{ key: 'leads', direction: 'desc' }}
            className="marketing-table"
          />
        )}
      </SharedStyled.FlexRow>
    </MarketingReportContainer>
  )
}

export default MarketingReport
