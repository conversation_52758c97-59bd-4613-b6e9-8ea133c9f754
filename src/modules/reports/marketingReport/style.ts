import styled from 'styled-components'
import { Nue } from '../../../shared/helpers/constants'
import { FlexRow } from '../../../styles/styled'

export const MarketingReportContainer = styled.div`
  #chart {
    .apexcharts-legend-text {
      font-family: ${Nue.regular}!important;
      padding-left: 20px;
    }
  }
`

export const RadioContainer = styled.div`
  display: flex;
  gap: 12rem;
  width: max-content;
  margin: 20px 0;
`

// =================== Metrics ===================

export const MetricsCont = styled(FlexRow)`
  margin-top: 20px;
  justify-content: space-around;
`
export const MetricCard = styled.div`
  text-align: center;
  margin: 10px 20px;
`

export const LabelText = styled.p`
  font-size: 14px;
  font-family: ${Nue.medium};
`

export const Value = styled.p`
  font-size: 22px;
  font-family: ${Nue.bold};
  margin: 10px 0;
`

export const Cost = styled.p`
  font-size: 16px;
  font-family: ${Nue.medium};
`

// =================== Metrics ===================
