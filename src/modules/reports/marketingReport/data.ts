export const staticData = {
    "leadSource": [
        {
            "_id": "01de2b19-086b-4699-8db4-5398a2565f8e",
            "leadSourceName": "Google - Organic",
            "channelId": "3377b3d1-b2df-4f5e-97c3-4edddf9d4094",
            "oppIds": [
                "74e86478-3bc5-4bc7-8476-c696dbe7b2ab",
                "90d33d3f-4f68-4e62-8ef6-0decb06ffe59",
                "03945296-8839-415d-99a0-98b9294719c0",
                "ebffe272-4127-49ed-abbc-d1a8f3ee1ae5",
                "4fe562ab-9718-45c3-b1c2-9293daf4227c",
                "13a56491-e39b-479c-8477-3152bf5d9bf9",
                "3b92fd16-51d3-459e-b7da-09e2b76b99bb",
                "dcb07889-562c-4413-93a4-b9abf7be9e89",
                "0c924e81-888c-4ab6-90ec-49d6fac3dc98",
                "188a74ec-258a-4009-9c08-aa90929c8a85",
                "6b1f5f80-e47d-4ad9-a708-8ee4c7a4f1c3"
            ],
            "totalOpps": 11,
            "totalBudget": 150878.19,
            "totalGrossProfit": 58865.14,
            "checkpointCounts": {
                "New Lead": 11,
                "New Opportunity": 11,
                "Needs Assessment": 11,
                "Presentation": 11,
                "Sale": 11
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "37cef333-6f03-460c-a12e-aae77b3f7813",
            "leadSourceName": "Facebook - Organic",
            "channelId": "3377b3d1-b2df-4f5e-97c3-4edddf9d4094",
            "oppIds": [
                "6e0a8fd2-4974-46de-9383-14539e0505e7"
            ],
            "totalOpps": 1,
            "totalBudget": 11070,
            "totalGrossProfit": 2871.3,
            "checkpointCounts": {
                "New Lead": 1,
                "New Opportunity": 1,
                "Needs Assessment": 1,
                "Presentation": 1,
                "Sale": 1
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "cae80f2e-96c3-477e-8b3f-957a513257c5",
            "leadSourceName": "Spokane Home Show 2025 - Meghann",
            "channelId": "46bd8269-8fee-4f86-b3d6-33868e0a85ea",
            "oppIds": [
                "b5e6a7d3-41e0-4cf6-b3e2-5ad6c1842546",
                "6dcaa6d7-7474-41e4-8fa4-f7f9908c9c80"
            ],
            "totalOpps": 2,
            "totalBudget": 32643,
            "totalGrossProfit": 20845.16,
            "checkpointCounts": {
                "New Lead": 2,
                "New Opportunity": 2,
                "Needs Assessment": 2,
                "Presentation": 2,
                "Sale": 2
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "05d5a823-b75b-43c1-b2b7-ab10581af126",
            "leadSourceName": "Facebook - Paid",
            "channelId": "7cb94035-e3f9-4fad-a778-9c400e160e08",
            "oppIds": [
                "71bd7e6f-979b-457a-b744-72ea119467e7",
                "5ed18481-7aab-4925-accc-61fa0f500dc4"
            ],
            "totalOpps": 2,
            "totalBudget": 123887.13,
            "totalGrossProfit": 48891.71,
            "checkpointCounts": {
                "New Lead": 2,
                "New Opportunity": 2,
                "Needs Assessment": 2,
                "Presentation": 2,
                "Sale": 2
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "dccc01cc-f4f2-4cd9-a5a7-783f08d11e18",
            "leadSourceName": "Bing - Paid",
            "channelId": "7cb94035-e3f9-4fad-a778-9c400e160e08",
            "oppIds": [
                "2a792a2c-36e7-4c23-96da-96a13a7360f7"
            ],
            "totalOpps": 1,
            "totalBudget": 22293,
            "totalGrossProfit": 8583.91,
            "checkpointCounts": {
                "New Lead": 1,
                "New Opportunity": 1,
                "Needs Assessment": 1,
                "Presentation": 1,
                "Sale": 1
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "846fd05d-e873-473e-80d6-1d2bc9420b01",
            "leadSourceName": "Website - Direct Traffic",
            "channelId": "3377b3d1-b2df-4f5e-97c3-4edddf9d4094",
            "oppIds": [
                "5d83daa9-9cb0-424c-9cfd-fd98102c3a91"
            ],
            "totalOpps": 1,
            "totalBudget": 2903,
            "totalGrossProfit": -730.57,
            "checkpointCounts": {
                "New Lead": 1,
                "New Opportunity": 1,
                "Needs Assessment": 1,
                "Presentation": 1,
                "Sale": 1
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "8c115e3c-2f31-4418-b0d6-fe1c0f5bbbc6",
            "leadSourceName": "Referral",
            "channelId": "09c66068-9536-41e0-94f1-810da4a9feea",
            "oppIds": [
                "bc14de96-bb38-44e5-a27b-aa85641e5f97"
            ],
            "totalOpps": 1,
            "totalBudget": 51461,
            "totalGrossProfit": 22623.43,
            "checkpointCounts": {
                "New Lead": 1,
                "New Opportunity": 1,
                "Needs Assessment": 1,
                "Presentation": 1,
                "Sale": 1
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "f2af9385-a84e-42d6-b8e2-70531f5466ae",
            "leadSourceName": "Warranty",
            "channelId": "13c5c48a-814b-40a5-854f-bc9ddbb006cb",
            "oppIds": [
                "11c17d58-10aa-4bff-afa0-eb4587c6759d",
                "a0a60bf7-4938-4579-9b54-d5cdaa288ca4",
                "a28a4f21-4e3a-49f8-bd6c-1e5e514c76c3"
            ],
            "totalOpps": 3,
            "totalBudget": 0,
            "totalGrossProfit": -492.96,
            "checkpointCounts": {
                "New Lead": 3,
                "New Opportunity": 3,
                "Needs Assessment": 3,
                "Presentation": 3,
                "Sale": 3
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "c4f191b7-009b-47af-b8ba-c499caec2127",
            "leadSourceName": "Walk-In / Drive-By",
            "channelId": "09c66068-9536-41e0-94f1-810da4a9feea",
            "oppIds": [
                "d7ad212c-ce63-47f9-8d91-b79c92dc6c75"
            ],
            "totalOpps": 1,
            "totalBudget": 49473,
            "totalGrossProfit": -4947.07,
            "checkpointCounts": {
                "New Lead": 1,
                "New Opportunity": 1,
                "Needs Assessment": 1,
                "Presentation": 1,
                "Sale": 1
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "591b61a0-048e-4b54-8b88-6fd647d00b88",
            "leadSourceName": "Radio",
            "channelId": "dd9f71bd-6fba-4eed-b6ee-b747b7cc5b8d",
            "oppIds": [
                "783b23c9-1a73-4f83-bf42-933244497195"
            ],
            "totalOpps": 1,
            "totalBudget": 1536,
            "totalGrossProfit": 534.92,
            "checkpointCounts": {
                "New Lead": 1,
                "New Opportunity": 1,
                "Needs Assessment": 1,
                "Presentation": 1,
                "Sale": 1
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "cd875433-b21a-42a4-9efb-257cde597d34",
            "leadSourceName": "Repeat Client",
            "channelId": "13c5c48a-814b-40a5-854f-bc9ddbb006cb",
            "oppIds": [
                "e8fed5a3-2a87-41f2-b984-0d85366901a9",
                "52bbe022-1f6a-4db1-b8ee-d5ba3cf5aea0",
                "9d73e785-46ad-4950-bcce-5c8534f12d00",
                "0b89d4ae-a59c-46f5-a11e-0abe9cc17ebd",
                "7c39021f-24d8-45b2-92f3-f8ce265fad4f"
            ],
            "totalOpps": 5,
            "totalBudget": 48564,
            "totalGrossProfit": 2344.49,
            "checkpointCounts": {
                "New Lead": 5,
                "New Opportunity": 5,
                "Needs Assessment": 5,
                "Presentation": 5,
                "Sale": 5
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "ca9b34d3-c211-4947-9a5f-ff93554cad69",
            "leadSourceName": "Truck Wrap",
            "channelId": "9a7b79fa-0776-44a7-9087-8e14424dbccb",
            "oppIds": [
                "be30501a-9e23-464c-a626-6d9a40598a54",
                "212078bd-747e-4e86-a5d5-7611c544377a"
            ],
            "totalOpps": 2,
            "totalBudget": 4161,
            "totalGrossProfit": 1585.47,
            "checkpointCounts": {
                "New Lead": 2,
                "New Opportunity": 2,
                "Needs Assessment": 2,
                "Presentation": 2,
                "Sale": 2
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "69702fb0-3344-49fa-b5ca-0d3dd4cd4fac",
            "leadSourceName": "Canvass - James Peppers",
            "channelId": "dd9f71bd-6fba-4eed-b6ee-b747b7cc5b8d",
            "oppIds": [
                "82160a7e-d603-4df1-a0cf-bad0bb8107d4"
            ],
            "totalOpps": 1,
            "totalBudget": 1879,
            "totalGrossProfit": 584.73,
            "checkpointCounts": {
                "New Lead": 1,
                "New Opportunity": 1,
                "Needs Assessment": 1,
                "Presentation": 1,
                "Sale": 1
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "a4c0743b-4858-4aec-8e7a-b7761bae7749",
            "leadSourceName": "Google GBP - Paid",
            "channelId": "7cb94035-e3f9-4fad-a778-9c400e160e08",
            "oppIds": [
                "705c4614-5675-4c18-b301-06a0ea888ffa"
            ],
            "totalOpps": 1,
            "totalBudget": 18278,
            "totalGrossProfit": 7022.39,
            "checkpointCounts": {
                "New Lead": 1,
                "New Opportunity": 1,
                "Needs Assessment": 1,
                "Presentation": 1,
                "Sale": 1
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "6abe21f1-8366-4e82-b13f-ba2ea4c0ffb8",
            "leadSourceName": "Salesperson Self Gen",
            "channelId": "09c66068-9536-41e0-94f1-810da4a9feea",
            "oppIds": [
                "f542e59c-8700-48ef-a6cb-be75f07395d6",
                "483f445b-5daa-440f-bede-21c6e85302d2"
            ],
            "totalOpps": 2,
            "totalBudget": 29243.95,
            "totalGrossProfit": 9369.37,
            "checkpointCounts": {
                "New Lead": 2,
                "New Opportunity": 2,
                "Needs Assessment": 2,
                "Presentation": 2,
                "Sale": 2
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "a19652b5-dc13-4ebb-aca1-9b13023d7a98",
            "leadSourceName": "Google - Paid",
            "channelId": "7cb94035-e3f9-4fad-a778-9c400e160e08",
            "oppIds": [
                "b296f59a-9fb9-4a54-9a51-10b29cf7cf77",
                "e697bd7d-c635-4970-8dd4-8e2574e427e9",
                "cb18c776-08e0-45b6-8d4a-51034ef7cb74",
                "0cadba65-19c6-4231-92f8-c2bb89636bb5",
                "c7d350b8-24a1-4948-904f-aa410e6a4879"
            ],
            "totalOpps": 5,
            "totalBudget": 70747.91,
            "totalGrossProfit": 34389.77,
            "checkpointCounts": {
                "New Lead": 5,
                "New Opportunity": 5,
                "Needs Assessment": 5,
                "Presentation": 5,
                "Sale": 5
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "8e27c184-f361-464b-89d5-eeb97ec36486",
            "leadSourceName": "Next Door",
            "channelId": "3377b3d1-b2df-4f5e-97c3-4edddf9d4094",
            "oppIds": [
                "e49fc3f7-b6f9-4082-811e-4aeafb77ac38"
            ],
            "totalOpps": 1,
            "totalBudget": 1654,
            "totalGrossProfit": 880.01,
            "checkpointCounts": {
                "New Lead": 1,
                "New Opportunity": 1,
                "Needs Assessment": 1,
                "Presentation": 1,
                "Sale": 1
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        }
    ],
    "opps": [
        {
            "_id": "e49fc3f7-b6f9-4082-811e-4aeafb77ac38",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "dee25998-cd76-4cfa-8f68-d74605d71390",
            "PO": "FORR2048",
            "num": "01",
            "salesPerson": "70c8c150-3d49-4433-9120-73566ff1547f",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2023-02-16T08:00:00.000Z",
            "oppDate": "2023-02-16T08:00:00.000Z",
            "warrantyType": false,
            "changeOrderValue": 0,
            "needsAssessmentDate": "2025-03-20T22:00:00.000Z",
            "budgetScore": 17.1,
            "acceptedProjectId": "98f501b8-edce-4de4-b087-e355730c658d",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "56828cd5-0d1d-4ea0-9d8d-3e1de5ddd26d",
            "salesCommission": {
                "total": 165.4,
                "sale": 82.7,
                "start": 0,
                "completed": 82.7,
                "modificationAmount": 0
            },
            "soldValue": 1654,
            "presentationDate": "2025-03-22T21:57:00.000Z",
            "saleDate": "2025-03-27T15:31:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-04-02T22:00:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 1654,
                    "cashTotal": 1405.38,
                    "financeTotal": 1405.38,
                    "jobCost": 473.19,
                    "lBurden": 49.98,
                    "lSubtotal": 188.59,
                    "lTotal": 238.56,
                    "laborCost": 165.12,
                    "mMarkup": 11.17,
                    "mTotal": 234.63,
                    "matCost": 210.81,
                    "matTax": 12.65,
                    "overhead": 720.81,
                    "profit": 211.98,
                    "commission": 248.01,
                    "travelFee": 23.47,
                    "upsell": 0,
                    "jobTotal": 1654,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 0,
                    "discountComm": 0,
                    "actRev": 1419.37,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 1654,
                    "actualLaborCost": 352.11,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 80.56,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 80.56
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 1654
            },
            "actual": {
                "total": 1654
            },
            "grossProfit": 880.01
        },
        {
            "_id": "6e0a8fd2-4974-46de-9383-14539e0505e7",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "c68ba14a-2316-43f1-9480-ec4a9eb39d6f",
            "PO": "HENR5901",
            "num": "01",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2024-05-29T16:06:12.429Z",
            "needsAssessmentDate": "2024-05-30T15:00:00.000Z",
            "presentationDate": "2025-03-12T18:00:00.000Z",
            "oppDate": "2024-05-29T16:06:12.429Z",
            "warrantyType": false,
            "changeOrderValue": 0,
            "budgetScore": -1.4,
            "acceptedProjectId": "2c08f289-dcc5-4fa3-93fa-280eae80f1ed",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "eeb4609e-480f-4d80-8adc-e90c7cc8119c",
            "salesCommission": {
                "total": 1107,
                "sale": 553.5,
                "start": 0,
                "completed": 553.5,
                "modificationAmount": 0
            },
            "soldValue": 11070,
            "saleDate": "2025-03-12T18:30:00.000Z",
            "workingCrew": {
                "name": "Mejias Construction LLC",
                "id": "be91ce59-a15c-466c-823d-58f14a8c08ed"
            },
            "jobCompletedDate": "2025-04-24T22:33:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 12301,
                    "cashTotal": 10836.68,
                    "financeTotal": 10836.68,
                    "jobCost": 6152.43,
                    "lBurden": 364.13,
                    "lSubtotal": 1374.08,
                    "lTotal": 1738.21,
                    "laborCost": 1273.28,
                    "mMarkup": 210.2,
                    "mTotal": 4648.03,
                    "matCost": 3966.06,
                    "matTax": 237.96,
                    "overhead": 3622.08,
                    "profit": 1062.62,
                    "commission": 1106.96,
                    "travelFee": 100.8,
                    "upsell": 0,
                    "permit": 233.81,
                    "asbTest": 0,
                    "jobTotal": 11070,
                    "salesTax": 0,
                    "discount": 1231,
                    "discountComm": 123.1,
                    "actRev": 6421.97,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 11070,
                    "actualLaborCost": 19,
                    "actualDays": 0,
                    "subcontractorCost": 2430,
                    "inventoryCost": 0,
                    "qbCOGS": 4637.71,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 4637.71
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 11070
            },
            "actual": {
                "total": 11070
            },
            "grossProfit": 2871.3
        },
        {
            "_id": "b296f59a-9fb9-4a54-9a51-10b29cf7cf77",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "61be2a3a-afa0-44b6-8f30-d0acc6944fd0",
            "PO": "LARR2095",
            "num": "01",
            "salesPerson": "c081a3f6-1e74-4532-8794-03665fea2bdf",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2024-09-18T07:00:00.000Z",
            "needsAssessmentDate": "2024-09-30T16:00:00.000Z",
            "presentationDate": "2024-09-30T18:00:00.000Z",
            "oppDate": "2024-09-18T07:00:00.000Z",
            "warrantyType": false,
            "changeOrderValue": 0,
            "budgetScore": 3,
            "acceptedProjectId": "1cd8bac7-3997-4918-b4c6-40eef76aa761",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "519f2b85-58af-4dc7-b26d-7a14fcba869e",
            "salesCommission": {
                "total": 3489.36,
                "sale": 1744.68,
                "start": 0,
                "completed": 1744.68,
                "modificationAmount": 0
            },
            "soldValue": 43617,
            "saleDate": "2025-02-28T18:43:00.000Z",
            "jobCompletedDate": "2025-04-12T13:35:00.000Z",
            "workingCrew": {
                "name": "Mejias Construction LLC",
                "id": "be91ce59-a15c-466c-823d-58f14a8c08ed"
            },
            "order": {
                "priceTotals": {
                    "grandTotal": 48702,
                    "cashTotal": 43239.86,
                    "financeTotal": 43239.86,
                    "jobCost": 20725.16,
                    "lBurden": 1506.07,
                    "lSubtotal": 5683.25,
                    "lTotal": 7189.32,
                    "laborCost": 5246.08,
                    "mMarkup": 644.5600000000001,
                    "mTotal": 14126.6,
                    "matCost": 12161.58,
                    "matTax": 729.6999999999999,
                    "overhead": 15150.21,
                    "profit": 4444.07,
                    "commission": 4361.57,
                    "travelFee": 437.17,
                    "upsell": 2921.74,
                    "permit": 590.76,
                    "asbTest": 0,
                    "jobTotal": 43617,
                    "salesTax": 0,
                    "discount": 5085,
                    "discountComm": 508.5,
                    "actRev": 29490.4,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 43617,
                    "actualLaborCost": 0,
                    "actualDays": 0,
                    "subcontractorCost": 7550,
                    "inventoryCost": 0,
                    "qbCOGS": 11200.09,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 11200.09
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 43617
            },
            "actual": {
                "total": 43617
            },
            "grossProfit": 20505.34
        },
        {
            "_id": "b296f59a-9fb9-4a54-9a51-10b29cf7cf77",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "61be2a3a-afa0-44b6-8f30-d0acc6944fd0",
            "PO": "LARR2095",
            "num": "01",
            "salesPerson": "c081a3f6-1e74-4532-8794-03665fea2bdf",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2024-09-18T07:00:00.000Z",
            "needsAssessmentDate": "2024-09-30T16:00:00.000Z",
            "presentationDate": "2024-09-30T18:00:00.000Z",
            "oppDate": "2024-09-18T07:00:00.000Z",
            "warrantyType": false,
            "changeOrderValue": 0,
            "budgetScore": 3,
            "acceptedProjectId": "1cd8bac7-3997-4918-b4c6-40eef76aa761",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "519f2b85-58af-4dc7-b26d-7a14fcba869e",
            "salesCommission": {
                "total": 3489.36,
                "sale": 1744.68,
                "start": 0,
                "completed": 1744.68,
                "modificationAmount": 0
            },
            "soldValue": 43617,
            "saleDate": "2025-02-28T18:43:00.000Z",
            "jobCompletedDate": "2025-04-12T13:35:00.000Z",
            "workingCrew": {
                "name": "Mejias Construction LLC",
                "id": "be91ce59-a15c-466c-823d-58f14a8c08ed"
            },
            "order": {
                "priceTotals": {
                    "grandTotal": 48702,
                    "cashTotal": 43239.86,
                    "financeTotal": 43239.86,
                    "jobCost": 20725.16,
                    "lBurden": 1506.07,
                    "lSubtotal": 5683.25,
                    "lTotal": 7189.32,
                    "laborCost": 5246.08,
                    "mMarkup": 644.5600000000001,
                    "mTotal": 14126.6,
                    "matCost": 12161.58,
                    "matTax": 729.6999999999999,
                    "overhead": 15150.21,
                    "profit": 4444.07,
                    "commission": 4361.57,
                    "travelFee": 437.17,
                    "upsell": 2921.74,
                    "permit": 590.76,
                    "asbTest": 0,
                    "jobTotal": 43617,
                    "salesTax": 0,
                    "discount": 5085,
                    "discountComm": 508.5,
                    "actRev": 29490.4,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 43617,
                    "actualLaborCost": 0,
                    "actualDays": 0,
                    "subcontractorCost": 7550,
                    "inventoryCost": 0,
                    "qbCOGS": 11200.09,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 11200.09
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 43617
            },
            "actual": {
                "total": 43617
            },
            "grossProfit": 20505.34
        },
        {
            "_id": "f542e59c-8700-48ef-a6cb-be75f07395d6",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "f01388c7-a200-454f-8ef0-515d0426f138",
            "PO": "SYLT8419",
            "num": "01",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "23e39321-92f7-4fd3-8aa0-89873051da4c",
            "newLeadDate": "2024-10-24T15:58:49.733Z",
            "oppDate": "2024-10-24T07:00:00.000Z",
            "needsAssessmentDate": "2024-10-24T23:00:00.000Z",
            "presentationDate": "2024-10-25T00:30:00.000Z",
            "warrantyType": false,
            "changeOrderValue": 0,
            "budgetScore": -5,
            "acceptedProjectId": "48c71a46-7b8f-45ec-a659-06fe3baa14fa",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "a98280eb-0c4e-40ad-8379-69389724f11b",
            "salesCommission": {
                "total": 2751,
                "sale": 1375.5,
                "start": 0,
                "completed": 1375.5,
                "modificationAmount": 0
            },
            "soldValue": 22925,
            "saleDate": "2025-03-27T19:17:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-05-13T00:20:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 26970,
                    "cashTotal": 23848.03,
                    "financeTotal": 23848.03,
                    "jobCost": 14036.73,
                    "lBurden": 762.69,
                    "lSubtotal": 2878.06,
                    "lTotal": 3640.74,
                    "laborCost": 2687.68,
                    "mMarkup": 495.05,
                    "mTotal": 10820.3,
                    "matCost": 9340.51,
                    "matTax": 560.43,
                    "overhead": 7586.56,
                    "profit": 2225.47,
                    "commission": 2292.43,
                    "travelFee": 190.38,
                    "upsell": 0,
                    "permit": 424.31,
                    "asbTest": 0,
                    "jobTotal": 22925,
                    "salesTax": 0,
                    "discount": 4045,
                    "discountComm": 404.5,
                    "actRev": 12104.7,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 24565.95,
                    "actualLaborCost": 5125.45,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 9966.2,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 9966.2
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 24565.95
            },
            "actual": {
                "total": 24565.95
            },
            "grossProfit": 6969.88
        },
        {
            "_id": "e697bd7d-c635-4970-8dd4-8e2574e427e9",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "aec0a940-9aa8-42df-8bdc-d51a364b1714",
            "PO": "RICH6615",
            "num": "01",
            "salesPerson": "72ef6006-a140-4aac-9623-de7e1783d2a3",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-01-17T21:32:00.000Z",
            "oppDate": "2025-01-17T21:32:00.000Z",
            "warrantyType": false,
            "needsAssessmentDate": "2025-01-18T18:00:00.000Z",
            "budgetScore": -13.8,
            "presentationDate": "2025-01-18T18:00:00.000Z",
            "saleDate": "2025-03-06T18:39:00.000Z",
            "acceptedProjectId": "1df9e4ce-17e3-4c02-8fb8-29402c4462c7",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "d3e09b6c-d7ae-4bb3-8e87-fc6c0aef875c",
            "salesCommission": {
                "total": 684.15,
                "sale": 342.08,
                "start": 0,
                "completed": 342.07,
                "modificationAmount": 0
            },
            "soldValue": 13683.06,
            "workingCrew": {
                "name": "Safe Roofing Services LLC",
                "id": "2c23b256-35e8-4ee6-b6b2-4765d0f57801"
            },
            "jobCompletedDate": "2025-03-26T00:26:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 19607,
                    "cashTotal": 17211.13,
                    "financeTotal": 17211.13,
                    "jobCost": 9612.02,
                    "lBurden": 579.14,
                    "lSubtotal": 2185.42,
                    "lTotal": 2764.56,
                    "laborCost": 1914.24,
                    "mMarkup": 326.07,
                    "mTotal": 7282.21,
                    "matCost": 6521.39,
                    "matTax": 0,
                    "overhead": 5760.78,
                    "profit": 1689.81,
                    "commission": 1368.25,
                    "travelFee": 271.18,
                    "upsell": 149,
                    "permit": 334.75,
                    "asbTest": 100,
                    "jobTotal": 13683.06,
                    "salesTax": 1217.79,
                    "discount": 5923.94,
                    "discountComm": 592.39,
                    "actRev": 6400.85,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 13683.06,
                    "actualLaborCost": 0,
                    "actualDays": 0,
                    "subcontractorCost": 2950,
                    "inventoryCost": 0,
                    "qbCOGS": 6362.73,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 6362.73
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 13683.06
            },
            "actual": {
                "total": 13683.06
            },
            "grossProfit": 6564.5
        },
        {
            "_id": "71bd7e6f-979b-457a-b744-72ea119467e7",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "a207334b-421c-49a3-a4a9-2b7314dc9b97",
            "PO": "CARP1661",
            "num": "01",
            "salesPerson": "c081a3f6-1e74-4532-8794-03665fea2bdf",
            "stage": "23e39321-92f7-4fd3-8aa0-89873051da4c",
            "newLeadDate": "2025-01-31T18:16:00.000Z",
            "oppDate": "2025-01-31T18:16:00.000Z",
            "warrantyType": false,
            "needsAssessmentDate": "2025-02-03T18:00:00.000Z",
            "budgetScore": 0,
            "presentationDate": "2025-02-04T03:26:00.000Z",
            "acceptedProjectId": "abef2191-7f21-401a-92a0-cecf7be40a8a",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "00fe88c1-de44-4a10-8a40-13a25d4fd46a",
            "salesCommission": {
                "total": 6380.24,
                "sale": 3190.12,
                "start": 0,
                "completed": 3190.12,
                "modificationAmount": 0
            },
            "soldValue": 79753,
            "saleDate": "2025-03-21T19:13:00.000Z",
            "workingCrew": {
                "name": "Vital MB Construction LLC",
                "id": "e3e61a3b-93c0-47fc-a052-04921d520bf7"
            },
            "changeOrderValue": 274.13,
            "changeOrders": [
                {
                    "name": "Repair Damaged rafters",
                    "description": "labor and materials to repair damaged rafters by replacing or sistering with 2x4 or 2x6 depending on size of original rafter",
                    "materials": 36.12,
                    "rawMatCost": 36.12,
                    "labor": 80.96,
                    "rawLaborCost": 0,
                    "addCost": 157.05,
                    "jobCost": 274.13,
                    "tax": 22.2,
                    "total": 296.33,
                    "signedBySales": false,
                    "date": "2025-04-17T07:00:00.000Z",
                    "mMarkup": 0,
                    "workTask": "Roofing ($32/Hr)",
                    "manHours": 2,
                    "overrideTotalCost": true,
                    "manHourCost": 64,
                    "laborBurden": 16.96,
                    "num": 1,
                    "deleted": false,
                    "markupEnabled": false,
                    "workTaskValue": 32,
                    "isSubContractor": false,
                    "modificationId": null
                }
            ],
            "jobCompletedDate": "2025-04-29T13:33:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 89611,
                    "cashTotal": 79633.21,
                    "financeTotal": 79633.21,
                    "jobCost": 37829.61,
                    "lBurden": 3133.63,
                    "lSubtotal": 11825.01,
                    "lTotal": 14958.64,
                    "laborCost": 10208.64,
                    "mMarkup": 1089.09,
                    "mTotal": 23886.38,
                    "matCost": 21781.88,
                    "matTax": 0,
                    "overhead": 31170.72,
                    "profit": 9142.16,
                    "commission": 7975.15,
                    "travelFee": 1616.37,
                    "upsell": 1492.15,
                    "permit": 915.41,
                    "asbTest": 100,
                    "jobTotal": 79753,
                    "salesTax": 6459.99,
                    "discount": 9858,
                    "discountComm": 985.8,
                    "actRev": 55866.62,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 79613,
                    "actualLaborCost": 47.36,
                    "actualDays": 0,
                    "subcontractorCost": 19822.5,
                    "inventoryCost": 0,
                    "qbCOGS": 21764.96,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 21764.96
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 80027.13
            },
            "actual": {
                "total": 79613
            },
            "grossProfit": 30788.01
        },
        {
            "_id": "11c17d58-10aa-4bff-afa0-eb4587c6759d",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "12c94e05-a3bb-4043-913d-e72256dca1c4",
            "PO": "RABO1181",
            "num": "W02",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-01-31T08:00:00.000Z",
            "oppDate": "2025-01-31T08:00:00.000Z",
            "warrantyType": true,
            "acceptedProjectId": "771bdc07-4d28-454b-b9a9-d2950634c3c8",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "12cc1ad8-f202-4c08-a734-94e3daf2a7ff",
            "salesCommission": {
                "total": 0,
                "sale": 0,
                "start": 0,
                "completed": 0,
                "modificationAmount": 0
            },
            "soldValue": 0,
            "workingCrew": {
                "name": "Repair Crew",
                "id": "96327288-f17c-469b-82b9-63d017f9662c"
            },
            "jobCompletedDate": "2025-03-20T19:43:00.000Z",
            "needsAssessmentDate": "2025-03-21T13:50:00.000Z",
            "presentationDate": "2025-03-21T13:50:00.000Z",
            "saleDate": "2025-03-21T13:51:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 1514,
                    "cashTotal": 1286.62,
                    "financeTotal": 1286.62,
                    "jobCost": 262.17,
                    "lBurden": 54.92,
                    "lSubtotal": 207.25,
                    "lTotal": 262.17,
                    "laborCost": 169.92,
                    "mMarkup": 0,
                    "mTotal": 0,
                    "matCost": 0,
                    "matTax": 0,
                    "overhead": 792.15,
                    "profit": 232.63,
                    "commission": 0,
                    "travelFee": 37.33,
                    "upsell": 0,
                    "jobTotal": 0,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 1514,
                    "discountComm": 0,
                    "actRev": 0,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 0,
                    "actualLaborCost": 70,
                    "actualDays": 0,
                    "subcontractorCost": 0
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 0
            },
            "actual": {
                "total": 0
            },
            "grossProfit": -88.55
        },
        {
            "_id": "d7ad212c-ce63-47f9-8d91-b79c92dc6c75",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "8444607c-ca4b-4107-bd76-128ff888e4ce",
            "PO": "SCHR2704",
            "num": "01",
            "salesPerson": "3d092960-481a-49e6-a030-6525688d51ce",
            "stage": "a01949b3-e299-4d08-b5b4-18b3f17ed63c",
            "newLeadDate": "2025-02-11T18:59:00.000Z",
            "oppDate": "2025-02-11T18:59:00.000Z",
            "warrantyType": false,
            "needsAssessmentDate": "2025-03-10T19:00:00.000Z",
            "budgetScore": 2.5,
            "presentationDate": "2025-03-10T20:31:00.000Z",
            "acceptedProjectId": "af7cb4ba-e76c-4bd0-ae06-43e57912907f",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "e3dbfcf0-6623-4113-8e44-5e9afd865fa9",
            "salesCommission": {
                "total": 3957.84,
                "sale": 1978.92,
                "start": 0,
                "completed": 1978.92,
                "modificationAmount": 0
            },
            "soldValue": 49473,
            "saleDate": "2025-03-26T20:57:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 53973,
                    "cashTotal": 47920.47,
                    "financeTotal": 47920.47,
                    "jobCost": 20746.54,
                    "lBurden": 1976.61,
                    "lSubtotal": 7458.88,
                    "lTotal": 9435.49,
                    "laborCost": 6376,
                    "mMarkup": 538.62,
                    "mTotal": 11964.21,
                    "matCost": 10162.67,
                    "matTax": 609.76,
                    "overhead": 19724.030000000002,
                    "profit": 5786.32,
                    "commission": 4947.07,
                    "travelFee": 1082.88,
                    "upsell": 1665.88,
                    "permit": 653.17,
                    "asbTest": 0,
                    "jobTotal": 49473,
                    "salesTax": 0,
                    "discount": 4500,
                    "discountComm": 450,
                    "actRev": 37508.79,
                    "travelHrlyRate": 32
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 49473
            },
            "actual": {
                "total": 0
            },
            "grossProfit": -4947.07
        },
        {
            "_id": "d7ad212c-ce63-47f9-8d91-b79c92dc6c75",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "8444607c-ca4b-4107-bd76-128ff888e4ce",
            "PO": "SCHR2704",
            "num": "01",
            "salesPerson": "3d092960-481a-49e6-a030-6525688d51ce",
            "stage": "a01949b3-e299-4d08-b5b4-18b3f17ed63c",
            "newLeadDate": "2025-02-11T18:59:00.000Z",
            "oppDate": "2025-02-11T18:59:00.000Z",
            "warrantyType": false,
            "needsAssessmentDate": "2025-03-10T19:00:00.000Z",
            "budgetScore": 2.5,
            "presentationDate": "2025-03-10T20:31:00.000Z",
            "acceptedProjectId": "af7cb4ba-e76c-4bd0-ae06-43e57912907f",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "e3dbfcf0-6623-4113-8e44-5e9afd865fa9",
            "salesCommission": {
                "total": 3957.84,
                "sale": 1978.92,
                "start": 0,
                "completed": 1978.92,
                "modificationAmount": 0
            },
            "soldValue": 49473,
            "saleDate": "2025-03-26T20:57:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 53973,
                    "cashTotal": 47920.47,
                    "financeTotal": 47920.47,
                    "jobCost": 20746.54,
                    "lBurden": 1976.61,
                    "lSubtotal": 7458.88,
                    "lTotal": 9435.49,
                    "laborCost": 6376,
                    "mMarkup": 538.62,
                    "mTotal": 11964.21,
                    "matCost": 10162.67,
                    "matTax": 609.76,
                    "overhead": 19724.030000000002,
                    "profit": 5786.32,
                    "commission": 4947.07,
                    "travelFee": 1082.88,
                    "upsell": 1665.88,
                    "permit": 653.17,
                    "asbTest": 0,
                    "jobTotal": 49473,
                    "salesTax": 0,
                    "discount": 4500,
                    "discountComm": 450,
                    "actRev": 37508.79,
                    "travelHrlyRate": 32
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 49473
            },
            "actual": {
                "total": 0
            },
            "grossProfit": -4947.07
        },
        {
            "_id": "5d83daa9-9cb0-424c-9cfd-fd98102c3a91",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "be721dcd-32f7-43e8-a29e-43a0d3780c98",
            "PO": "CROW1448",
            "num": "01",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-11T19:58:00.000Z",
            "oppDate": "2025-02-11T19:59:00.000Z",
            "warrantyType": false,
            "needsAssessmentDate": "2025-02-24T18:00:00.000Z",
            "budgetScore": 11.9,
            "presentationDate": "2025-02-24T19:30:00.000Z",
            "acceptedProjectId": "652f1e5c-a042-4660-a569-cfb7b0f471f2",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "7a3b5eee-d81e-4a6f-aca1-31ccb9b4ce12",
            "salesCommission": {
                "total": 435.45,
                "sale": 217.73,
                "start": 0,
                "completed": 217.72,
                "modificationAmount": 0
            },
            "soldValue": 2903,
            "saleDate": "2025-03-06T01:30:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-04-07T17:44:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 2903,
                    "cashTotal": 2425.77,
                    "financeTotal": 2425.77,
                    "jobCost": 1464.8799999999999,
                    "lBurden": 59.5,
                    "lSubtotal": 224.54000000000002,
                    "lTotal": 284.03999999999996,
                    "laborCost": 203.2,
                    "mMarkup": 56.23,
                    "mTotal": 1335.34,
                    "matCost": 1060.96,
                    "matTax": 63.66,
                    "overhead": 685.38,
                    "profit": 201.76999999999998,
                    "commission": 321.95,
                    "travelFee": 21.34,
                    "upsell": 74.51,
                    "jobTotal": 2903,
                    "permit": 154.5,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 0,
                    "discountComm": 0,
                    "actRev": 1567.66,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 2903,
                    "actualLaborCost": 1575.2,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 1318.99,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 1318.99
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 2903
            },
            "actual": {
                "total": 2903
            },
            "grossProfit": -730.57
        },
        {
            "_id": "5d83daa9-9cb0-424c-9cfd-fd98102c3a91",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "be721dcd-32f7-43e8-a29e-43a0d3780c98",
            "PO": "CROW1448",
            "num": "01",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-11T19:58:00.000Z",
            "oppDate": "2025-02-11T19:59:00.000Z",
            "warrantyType": false,
            "needsAssessmentDate": "2025-02-24T18:00:00.000Z",
            "budgetScore": 11.9,
            "presentationDate": "2025-02-24T19:30:00.000Z",
            "acceptedProjectId": "652f1e5c-a042-4660-a569-cfb7b0f471f2",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "7a3b5eee-d81e-4a6f-aca1-31ccb9b4ce12",
            "salesCommission": {
                "total": 435.45,
                "sale": 217.73,
                "start": 0,
                "completed": 217.72,
                "modificationAmount": 0
            },
            "soldValue": 2903,
            "saleDate": "2025-03-06T01:30:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-04-07T17:44:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 2903,
                    "cashTotal": 2425.77,
                    "financeTotal": 2425.77,
                    "jobCost": 1464.8799999999999,
                    "lBurden": 59.5,
                    "lSubtotal": 224.54000000000002,
                    "lTotal": 284.03999999999996,
                    "laborCost": 203.2,
                    "mMarkup": 56.23,
                    "mTotal": 1335.34,
                    "matCost": 1060.96,
                    "matTax": 63.66,
                    "overhead": 685.38,
                    "profit": 201.76999999999998,
                    "commission": 321.95,
                    "travelFee": 21.34,
                    "upsell": 74.51,
                    "jobTotal": 2903,
                    "permit": 154.5,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 0,
                    "discountComm": 0,
                    "actRev": 1567.66,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 2903,
                    "actualLaborCost": 1575.2,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 1318.99,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 1318.99
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 2903
            },
            "actual": {
                "total": 2903
            },
            "grossProfit": -730.57
        },
        {
            "_id": "bc14de96-bb38-44e5-a27b-aa85641e5f97",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "773a9c8b-7943-4613-aeb8-0113e3b313b4",
            "PO": "RASA3911",
            "num": "01",
            "salesPerson": "c081a3f6-1e74-4532-8794-03665fea2bdf",
            "stage": "23e39321-92f7-4fd3-8aa0-89873051da4c",
            "newLeadDate": "2025-02-18T17:42:00.000Z",
            "oppDate": "2025-02-18T17:42:00.000Z",
            "warrantyType": false,
            "needsAssessmentDate": "2025-02-21T18:00:00.000Z",
            "presentationDate": "2025-02-24T22:32:00.000Z",
            "budgetScore": 3.1,
            "acceptedProjectId": "701fe6c4-0bab-45d5-bcf4-b59f22e425c4",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "a668c780-d45c-4809-8038-159b48c05138",
            "salesCommission": {
                "total": 4088.88,
                "sale": 2044.44,
                "start": 0,
                "completed": 2044.44,
                "modificationAmount": 0
            },
            "soldValue": 51111,
            "saleDate": "2025-03-10T22:13:00.000Z",
            "workingCrew": {
                "name": "Mejias Construction LLC",
                "id": "be91ce59-a15c-466c-823d-58f14a8c08ed"
            },
            "changeOrderValue": 350,
            "changeOrders": [
                {
                    "name": "Additional Material costs",
                    "description": "Homeowners wanted dark colored trim.  This was not in the contract.  there was a note about brown flashings but otherwise but the next comment said Metal color is sandstone.... Another reason why colors need to be on contract.  Homeowner agreed to split cost of new metal.",
                    "materials": 350,
                    "rawMatCost": 350,
                    "labor": 0,
                    "rawLaborCost": 0,
                    "addCost": 0,
                    "jobCost": 350,
                    "tax": 31.85,
                    "total": 381.85,
                    "signedBySales": false,
                    "date": "2025-05-15T07:00:00.000Z",
                    "mMarkup": 0,
                    "workTask": "Roofing ($32/Hr)",
                    "manHours": 0,
                    "overrideTotalCost": false,
                    "manHourCost": 0,
                    "laborBurden": 0,
                    "num": 1,
                    "deleted": false,
                    "markupEnabled": false,
                    "workTaskValue": 32,
                    "isSubContractor": false,
                    "modificationId": null
                }
            ],
            "jobCompletedDate": "2025-05-16T22:28:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 56790,
                    "cashTotal": 50304.49,
                    "financeTotal": 50304.49,
                    "jobCost": 25234.97,
                    "lBurden": 1665.63,
                    "lSubtotal": 6285.4,
                    "lTotal": 7951.03,
                    "laborCost": 5671.04,
                    "mMarkup": 823.04,
                    "mTotal": 18090.26,
                    "matCost": 16460.89,
                    "matTax": 0,
                    "overhead": 16568.32,
                    "profit": 4858.83,
                    "commission": 5111.08,
                    "travelFee": 614.36,
                    "upsell": 3642.58,
                    "permit": 706.32,
                    "asbTest": 100,
                    "jobTotal": 51111,
                    "salesTax": 4599.99,
                    "discount": 5679,
                    "discountComm": 567.9,
                    "actRev": 33020.74,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 51461,
                    "actualLaborCost": 37.62,
                    "actualDays": 0,
                    "subcontractorCost": 8780,
                    "inventoryCost": 0,
                    "qbCOGS": 14898.9,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 14898.9
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 51461
            },
            "actual": {
                "total": 51461
            },
            "grossProfit": 22623.43
        },
        {
            "_id": "74e86478-3bc5-4bc7-8476-c696dbe7b2ab",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "5ecb3b49-d94f-4436-ae43-f39da6958e41",
            "PO": "HAYN4821",
            "num": "01",
            "salesPerson": "b1bb00eb-11ec-4208-a623-b36cd40e0da1",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-22T20:13:00.000Z",
            "oppDate": "2025-02-22T20:23:00.000Z",
            "warrantyType": false,
            "needsAssessmentDate": "2025-02-27T00:00:00.000Z",
            "budgetScore": 0.9,
            "presentationDate": "2025-02-27T02:59:00.000Z",
            "saleDate": "2025-03-01T03:00:00.000Z",
            "acceptedProjectId": "4880df16-5eb1-4812-95f4-44bd3f7994eb",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "8081247e-b21c-45c2-86e1-faa6c795c181",
            "salesCommission": {
                "total": 1700.55,
                "sale": 850.28,
                "start": 0,
                "completed": 850.27,
                "modificationAmount": 0
            },
            "soldValue": 34011,
            "jobCompletedDate": "2025-04-14T13:45:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "order": {
                "priceTotals": {
                    "grandTotal": 37401,
                    "cashTotal": 33104.42,
                    "financeTotal": 33104.42,
                    "jobCost": 16353.289999999999,
                    "lBurden": 1255.47,
                    "lSubtotal": 4737.63,
                    "lTotal": 5993.1,
                    "laborCost": 4390.08,
                    "mMarkup": 493.34,
                    "mTotal": 10915.7,
                    "matCost": 9866.85,
                    "matTax": 0,
                    "overhead": 12756.66,
                    "profit": 3741.93,
                    "commission": 3400.99,
                    "travelFee": 347.54999999999995,
                    "upsell": 253.62,
                    "permit": 455.52,
                    "asbTest": 100,
                    "jobTotal": 34011,
                    "salesTax": 3026.98,
                    "discount": 3390,
                    "discountComm": 339,
                    "actRev": 23095.3,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 34579.19,
                    "actualLaborCost": 4553.87,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 9861.16,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 9861.16
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 34579.19
            },
            "actual": {
                "total": 34579.19
            },
            "grossProfit": 15556.39
        },
        {
            "_id": "74e86478-3bc5-4bc7-8476-c696dbe7b2ab",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "5ecb3b49-d94f-4436-ae43-f39da6958e41",
            "PO": "HAYN4821",
            "num": "01",
            "salesPerson": "b1bb00eb-11ec-4208-a623-b36cd40e0da1",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-22T20:13:00.000Z",
            "oppDate": "2025-02-22T20:23:00.000Z",
            "warrantyType": false,
            "needsAssessmentDate": "2025-02-27T00:00:00.000Z",
            "budgetScore": 0.9,
            "presentationDate": "2025-02-27T02:59:00.000Z",
            "saleDate": "2025-03-01T03:00:00.000Z",
            "acceptedProjectId": "4880df16-5eb1-4812-95f4-44bd3f7994eb",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "8081247e-b21c-45c2-86e1-faa6c795c181",
            "salesCommission": {
                "total": 1700.55,
                "sale": 850.28,
                "start": 0,
                "completed": 850.27,
                "modificationAmount": 0
            },
            "soldValue": 34011,
            "jobCompletedDate": "2025-04-14T13:45:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "order": {
                "priceTotals": {
                    "grandTotal": 37401,
                    "cashTotal": 33104.42,
                    "financeTotal": 33104.42,
                    "jobCost": 16353.289999999999,
                    "lBurden": 1255.47,
                    "lSubtotal": 4737.63,
                    "lTotal": 5993.1,
                    "laborCost": 4390.08,
                    "mMarkup": 493.34,
                    "mTotal": 10915.7,
                    "matCost": 9866.85,
                    "matTax": 0,
                    "overhead": 12756.66,
                    "profit": 3741.93,
                    "commission": 3400.99,
                    "travelFee": 347.54999999999995,
                    "upsell": 253.62,
                    "permit": 455.52,
                    "asbTest": 100,
                    "jobTotal": 34011,
                    "salesTax": 3026.98,
                    "discount": 3390,
                    "discountComm": 339,
                    "actRev": 23095.3,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 34579.19,
                    "actualLaborCost": 4553.87,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 9861.16,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 9861.16
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 34579.19
            },
            "actual": {
                "total": 34579.19
            },
            "grossProfit": 15556.39
        },
        {
            "_id": "90d33d3f-4f68-4e62-8ef6-0decb06ffe59",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "a255072e-93bb-426c-bb44-2b72d36396d4",
            "PO": "BLAC1688",
            "num": "01",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-24T17:58:00.000Z",
            "oppDate": "2025-02-24T17:58:00.000Z",
            "warrantyType": false,
            "changeOrders": [
                {
                    "name": "Change Order",
                    "description": "Add 12' Gutter and 12' 1 x 6 Cedar Fascia to Job Scope",
                    "materials": 0,
                    "rawMatCost": 0,
                    "labor": 0,
                    "rawLaborCost": 0,
                    "addCost": 500,
                    "jobCost": 500,
                    "tax": 0,
                    "total": 500,
                    "signedBySales": true,
                    "date": "2025-04-07T07:00:00.000Z",
                    "mMarkup": 0,
                    "workTask": "Roofing ($32/Hr)",
                    "manHours": 0,
                    "overrideTotalCost": true,
                    "manHourCost": 0,
                    "laborBurden": 0,
                    "num": 1,
                    "deleted": false,
                    "markupEnabled": false,
                    "workTaskValue": 32,
                    "isSubContractor": false,
                    "modificationId": "7758a25f-3f20-4b85-beb2-c5f23d80826f"
                }
            ],
            "needsAssessmentDate": "2025-02-26T21:00:00.000Z",
            "budgetScore": 4.2,
            "acceptedProjectId": "f96a1c7f-666f-422b-9ba1-7a8788cda96a",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "0b4abd57-f1a7-408b-b891-2f3f5c3756bd",
            "salesCommission": {
                "total": 5115.2,
                "sale": 2557.6,
                "start": 0,
                "completed": 2557.6,
                "modificationAmount": 0
            },
            "soldValue": 51152,
            "presentationDate": "2025-02-27T01:00:00.000Z",
            "saleDate": "2025-03-04T16:40:00.000Z",
            "workingCrew": {
                "name": "Mejias Construction LLC",
                "id": "be91ce59-a15c-466c-823d-58f14a8c08ed"
            },
            "changeOrderValue": 500,
            "jobCompletedDate": "2025-04-08T13:33:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 56836,
                    "cashTotal": 50438.74,
                    "financeTotal": 50438.74,
                    "jobCost": 22773.51,
                    "lBurden": 1838.09,
                    "lSubtotal": 6936.2,
                    "lTotal": 8774.29,
                    "laborCost": 6211.52,
                    "mMarkup": 666.63,
                    "mTotal": 14712.75,
                    "matCost": 12577.91,
                    "matTax": 754.67,
                    "overhead": 18283.82,
                    "profit": 5361.83,
                    "commission": 5115.18,
                    "travelFee": 724.68,
                    "upsell": 4019.73,
                    "permit": 713.53,
                    "asbTest": 0,
                    "jobTotal": 51152,
                    "salesTax": 0,
                    "discount": 5684,
                    "discountComm": 568.4,
                    "actRev": 36439.25,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 51652,
                    "actualLaborCost": 0,
                    "actualDays": 0,
                    "subcontractorCost": 8898,
                    "inventoryCost": 0,
                    "qbCOGS": 12021.49,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 12021.49
                },
                "modifiedBudget": {
                    "subContractorTotal": 8898,
                    "isSubcontractorOnly": true,
                    "rawLaborBudget": 0
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 51652
            },
            "actual": {
                "total": 51652
            },
            "grossProfit": 25617.33
        },
        {
            "_id": "03945296-8839-415d-99a0-98b9294719c0",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "a255072e-93bb-426c-bb44-2b72d36396d4",
            "PO": "BLAC1708",
            "num": "02",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-24T18:07:00.000Z",
            "oppDate": "2025-02-24T18:07:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-02-25T21:00:00.000Z",
            "budgetScore": 8.9,
            "acceptedProjectId": "c2698341-37ea-452e-802d-b72b4cc401b5",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "c9cbdca7-e99b-45ea-8584-f624095500ac",
            "salesCommission": {
                "total": 521.55,
                "sale": 260.77,
                "start": 0,
                "completed": 260.78,
                "modificationAmount": 0
            },
            "soldValue": 3477,
            "presentationDate": "2025-03-01T01:00:00.000Z",
            "saleDate": "2025-03-11T22:20:00.000Z",
            "workingCrew": {
                "name": "Mejias Construction LLC",
                "id": "be91ce59-a15c-466c-823d-58f14a8c08ed"
            },
            "jobCompletedDate": "2025-04-19T00:36:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 4091,
                    "cashTotal": 3476.86,
                    "financeTotal": 3476.86,
                    "jobCost": 989.47,
                    "lBurden": 133.35,
                    "lSubtotal": 503.21,
                    "lTotal": 636.56,
                    "laborCost": 448.96,
                    "mMarkup": 16.81,
                    "mTotal": 352.91,
                    "matCost": 317.08,
                    "matTax": 19.02,
                    "overhead": 1923.37,
                    "profit": 564.6,
                    "commission": 521.46,
                    "travelFee": 54.25,
                    "upsell": 0,
                    "jobTotal": 3477,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 614,
                    "discountComm": 92.1,
                    "actRev": 3124.09,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 3477,
                    "actualLaborCost": 0,
                    "actualDays": 0,
                    "subcontractorCost": 1000,
                    "inventoryCost": 0,
                    "qbCOGS": 620.62,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 620.62
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 3477
            },
            "actual": {
                "total": 3477
            },
            "grossProfit": 1334.92
        },
        {
            "_id": "cb18c776-08e0-45b6-8d4a-51034ef7cb74",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "eb352971-087c-4a1f-938c-2af89e8c1a3a",
            "PO": "VIGI0917",
            "num": "01",
            "salesPerson": "70c8c150-3d49-4433-9120-73566ff1547f",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-25T01:21:00.000Z",
            "oppDate": "2025-02-25T01:21:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-02-27T23:30:00.000Z",
            "budgetScore": 17.1,
            "presentationDate": "2025-02-28T06:25:00.000Z",
            "acceptedProjectId": "20361f6c-a9f3-4331-94df-6194acd20256",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "b77a3e8f-9283-4ccb-a937-b1abf310c9ff",
            "salesCommission": {
                "total": 148.05,
                "sale": 74.03,
                "start": 0,
                "completed": 74.02,
                "modificationAmount": 0
            },
            "soldValue": 2961,
            "saleDate": "2025-03-18T14:21:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-03-19T22:11:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 2961,
                    "cashTotal": 2516.13,
                    "financeTotal": 2516.13,
                    "jobCost": 680.73,
                    "lBurden": 98.4,
                    "lSubtotal": 371.31,
                    "lTotal": 469.7,
                    "laborCost": 333.76,
                    "mMarkup": 10.05,
                    "mTotal": 211.03,
                    "matCost": 200.98,
                    "matTax": 0,
                    "overhead": 1419.21,
                    "profit": 417.03,
                    "commission": 444.02,
                    "travelFee": 37.55,
                    "upsell": 0,
                    "jobTotal": 2961,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 266.49,
                    "discount": 0,
                    "discountComm": 0,
                    "actRev": 2749.97,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 2961,
                    "actualLaborCost": 637.51,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 101.83,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 101.83
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 2961
            },
            "actual": {
                "total": 2961
            },
            "grossProfit": 1608.7
        },
        {
            "_id": "ebffe272-4127-49ed-abbc-d1a8f3ee1ae5",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "a1fd1048-2704-4773-a6a6-98c5909eaf0e",
            "PO": "ROUT1499",
            "num": "01",
            "salesPerson": "70c8c150-3d49-4433-9120-73566ff1547f",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-25T17:40:00.000Z",
            "oppDate": "2025-02-25T17:40:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-02-27T20:00:00.000Z",
            "budgetScore": 23.7,
            "acceptedProjectId": "bd9a4dca-3ba2-4376-97b4-b303799b4ce9",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "f5557fcc-f3b8-4c37-a3bb-d344d24967ff",
            "salesCommission": {
                "total": 17.5,
                "sale": 8.75,
                "start": 0,
                "completed": 8.75,
                "modificationAmount": 0
            },
            "soldValue": 350,
            "presentationDate": "2025-02-27T20:14:00.000Z",
            "saleDate": "2025-02-27T20:30:00.000Z",
            "workingCrew": {
                "name": "Repair Crew",
                "id": "96327288-f17c-469b-82b9-63d017f9662c"
            },
            "jobCompletedDate": "2025-03-03T20:01:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 350,
                    "cashTotal": 268.08,
                    "financeTotal": 268.08,
                    "jobCost": 63.49,
                    "lBurden": 10.97,
                    "lSubtotal": 41.39,
                    "lTotal": 52.36,
                    "laborCost": 33.92,
                    "mMarkup": 0.53,
                    "mTotal": 11.13,
                    "matCost": 10,
                    "matTax": 0.6,
                    "overhead": 158.2,
                    "profit": 47.01,
                    "commission": 47.31,
                    "travelFee": 7.47,
                    "upsell": 0,
                    "jobTotal": 350,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 0,
                    "discountComm": 0,
                    "actRev": 338.87,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 350,
                    "actualLaborCost": 13.44,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 9.2,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 9.2
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 350
            },
            "actual": {
                "total": 350
            },
            "grossProfit": 276.49
        },
        {
            "_id": "e8fed5a3-2a87-41f2-b984-0d85366901a9",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "1b2c28ba-ce28-4041-95f4-25abff70faac",
            "PO": "AMER0550",
            "num": "MV02",
            "salesPerson": "3d092960-481a-49e6-a030-6525688d51ce",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-25T19:45:00.000Z",
            "oppDate": "2025-02-25T19:45:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-02-25T22:00:00.000Z",
            "budgetScore": 12.8,
            "acceptedProjectId": "528374df-9e41-4f5f-9def-a0123080d654",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "3f87d551-6cc1-476d-afbf-5d90fde67b97",
            "salesCommission": {
                "total": 286,
                "sale": 143,
                "start": 0,
                "completed": 143,
                "modificationAmount": 0
            },
            "soldValue": 2200,
            "presentationDate": "2025-02-25T23:25:00.000Z",
            "saleDate": "2025-03-04T18:45:00.000Z",
            "workingCrew": {
                "name": "Repair Crew",
                "id": "96327288-f17c-469b-82b9-63d017f9662c"
            },
            "jobCompletedDate": "2025-03-12T23:02:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 2389,
                    "cashTotal": 2030.01,
                    "financeTotal": 2030.01,
                    "jobCost": 571.08,
                    "lBurden": 78.21,
                    "lSubtotal": 295.14,
                    "lTotal": 373.36,
                    "laborCost": 162.88,
                    "mMarkup": 9.42,
                    "mTotal": 197.71,
                    "matCost": 177.64999999999998,
                    "matTax": 10.66,
                    "overhead": 1128.1100000000001,
                    "profit": 331.57,
                    "commission": 329.89,
                    "travelFee": 132.26,
                    "upsell": 0,
                    "jobTotal": 2200,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 189,
                    "discountComm": 28.35,
                    "actRev": 2002.29,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 2200,
                    "actualLaborCost": 270.55,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 66.69,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 66.69
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 2200
            },
            "actual": {
                "total": 2200
            },
            "grossProfit": 1461.17
        },
        {
            "_id": "e8fed5a3-2a87-41f2-b984-0d85366901a9",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "1b2c28ba-ce28-4041-95f4-25abff70faac",
            "PO": "AMER0550",
            "num": "MV02",
            "salesPerson": "3d092960-481a-49e6-a030-6525688d51ce",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-25T19:45:00.000Z",
            "oppDate": "2025-02-25T19:45:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-02-25T22:00:00.000Z",
            "budgetScore": 12.8,
            "acceptedProjectId": "528374df-9e41-4f5f-9def-a0123080d654",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "3f87d551-6cc1-476d-afbf-5d90fde67b97",
            "salesCommission": {
                "total": 286,
                "sale": 143,
                "start": 0,
                "completed": 143,
                "modificationAmount": 0
            },
            "soldValue": 2200,
            "presentationDate": "2025-02-25T23:25:00.000Z",
            "saleDate": "2025-03-04T18:45:00.000Z",
            "workingCrew": {
                "name": "Repair Crew",
                "id": "96327288-f17c-469b-82b9-63d017f9662c"
            },
            "jobCompletedDate": "2025-03-12T23:02:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 2389,
                    "cashTotal": 2030.01,
                    "financeTotal": 2030.01,
                    "jobCost": 571.08,
                    "lBurden": 78.21,
                    "lSubtotal": 295.14,
                    "lTotal": 373.36,
                    "laborCost": 162.88,
                    "mMarkup": 9.42,
                    "mTotal": 197.71,
                    "matCost": 177.64999999999998,
                    "matTax": 10.66,
                    "overhead": 1128.1100000000001,
                    "profit": 331.57,
                    "commission": 329.89,
                    "travelFee": 132.26,
                    "upsell": 0,
                    "jobTotal": 2200,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 189,
                    "discountComm": 28.35,
                    "actRev": 2002.29,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 2200,
                    "actualLaborCost": 270.55,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 66.69,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 66.69
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 2200
            },
            "actual": {
                "total": 2200
            },
            "grossProfit": 1461.17
        },
        {
            "_id": "705c4614-5675-4c18-b301-06a0ea888ffa",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "05206307-265c-4522-84fe-83fdd5f1d7b4",
            "PO": "AGA2172",
            "num": "01",
            "salesPerson": "b1bb00eb-11ec-4208-a623-b36cd40e0da1",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-25T23:40:00.000Z",
            "oppDate": "2025-02-25T23:40:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-03T18:00:00.000Z",
            "budgetScore": 0.9,
            "acceptedProjectId": "1bf8358d-14d3-4217-b191-7f41958a312c",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "78f52616-75cb-4509-aadd-c518b7d10165",
            "salesCommission": {
                "total": 869.1,
                "sale": 434.55,
                "start": 0,
                "completed": 434.55,
                "modificationAmount": 0
            },
            "soldValue": 17382,
            "presentationDate": "2025-03-03T18:00:00.000Z",
            "saleDate": "2025-03-04T19:00:00.000Z",
            "workingCrew": {
                "name": "Vital MB Construction LLC",
                "id": "e3e61a3b-93c0-47fc-a052-04921d520bf7"
            },
            "jobCompletedDate": "2025-03-28T19:43:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 18882,
                    "cashTotal": 16715.41,
                    "financeTotal": 16715.41,
                    "jobCost": 9288.82,
                    "lBurden": 556.62,
                    "lSubtotal": 2100.46,
                    "lTotal": 2657.09,
                    "laborCost": 1953.92,
                    "mMarkup": 315.78999999999996,
                    "mTotal": 6908.8,
                    "matCost": 5958.43,
                    "matTax": 357.51,
                    "overhead": 5655.62,
                    "profit": 1659.96,
                    "commission": 1738.06,
                    "travelFee": 146.54,
                    "upsell": 112.48,
                    "permit": 277.07,
                    "asbTest": 0,
                    "jobTotal": 17382,
                    "salesTax": 0,
                    "discount": 1500,
                    "discountComm": 150,
                    "actRev": 10473.2,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 18278,
                    "actualLaborCost": 484.64,
                    "actualDays": 0,
                    "subcontractorCost": 2930,
                    "inventoryCost": 0,
                    "qbCOGS": 6848.48,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 6848.48
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 18278
            },
            "actual": {
                "total": 18278
            },
            "grossProfit": 7022.39
        },
        {
            "_id": "705c4614-5675-4c18-b301-06a0ea888ffa",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "05206307-265c-4522-84fe-83fdd5f1d7b4",
            "PO": "AGA2172",
            "num": "01",
            "salesPerson": "b1bb00eb-11ec-4208-a623-b36cd40e0da1",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-25T23:40:00.000Z",
            "oppDate": "2025-02-25T23:40:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-03T18:00:00.000Z",
            "budgetScore": 0.9,
            "acceptedProjectId": "1bf8358d-14d3-4217-b191-7f41958a312c",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "78f52616-75cb-4509-aadd-c518b7d10165",
            "salesCommission": {
                "total": 869.1,
                "sale": 434.55,
                "start": 0,
                "completed": 434.55,
                "modificationAmount": 0
            },
            "soldValue": 17382,
            "presentationDate": "2025-03-03T18:00:00.000Z",
            "saleDate": "2025-03-04T19:00:00.000Z",
            "workingCrew": {
                "name": "Vital MB Construction LLC",
                "id": "e3e61a3b-93c0-47fc-a052-04921d520bf7"
            },
            "jobCompletedDate": "2025-03-28T19:43:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 18882,
                    "cashTotal": 16715.41,
                    "financeTotal": 16715.41,
                    "jobCost": 9288.82,
                    "lBurden": 556.62,
                    "lSubtotal": 2100.46,
                    "lTotal": 2657.09,
                    "laborCost": 1953.92,
                    "mMarkup": 315.78999999999996,
                    "mTotal": 6908.8,
                    "matCost": 5958.43,
                    "matTax": 357.51,
                    "overhead": 5655.62,
                    "profit": 1659.96,
                    "commission": 1738.06,
                    "travelFee": 146.54,
                    "upsell": 112.48,
                    "permit": 277.07,
                    "asbTest": 0,
                    "jobTotal": 17382,
                    "salesTax": 0,
                    "discount": 1500,
                    "discountComm": 150,
                    "actRev": 10473.2,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 18278,
                    "actualLaborCost": 484.64,
                    "actualDays": 0,
                    "subcontractorCost": 2930,
                    "inventoryCost": 0,
                    "qbCOGS": 6848.48,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 6848.48
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 18278
            },
            "actual": {
                "total": 18278
            },
            "grossProfit": 7022.39
        },
        {
            "_id": "4fe562ab-9718-45c3-b1c2-9293daf4227c",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "9e862429-cffe-4834-88f8-157eb764f3d9",
            "PO": "OKHO4596",
            "num": "01",
            "salesPerson": "70c8c150-3d49-4433-9120-73566ff1547f",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-26T19:23:00.000Z",
            "oppDate": "2025-02-26T19:23:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-02-27T21:00:00.000Z",
            "budgetScore": 19.4,
            "presentationDate": "2025-02-28T02:35:00.000Z",
            "saleDate": "2025-02-28T05:32:00.000Z",
            "acceptedProjectId": "fa6c5dc3-3872-4eec-a253-aafd7966c793",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "7f151613-3249-4d03-a5df-edf902252a5e",
            "salesCommission": {
                "total": 37.5,
                "sale": 18.75,
                "start": 0,
                "completed": 18.75,
                "modificationAmount": 0
            },
            "soldValue": 750,
            "jobCompletedDate": "2025-03-01T00:50:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 716,
                    "cashTotal": 608.59,
                    "financeTotal": 608.59,
                    "jobCost": 124.01,
                    "lBurden": 25.98,
                    "lSubtotal": 98.03,
                    "lTotal": 124.01,
                    "laborCost": 68.16,
                    "mMarkup": 0,
                    "mTotal": 0,
                    "matCost": 0,
                    "matTax": 0,
                    "overhead": 374.7,
                    "profit": 109.89,
                    "commission": 112.5,
                    "travelFee": 29.87,
                    "upsell": 0,
                    "jobTotal": 750,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": -34,
                    "discountComm": -5.1,
                    "actRev": 750,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 750,
                    "actualLaborCost": 0,
                    "actualDays": 0,
                    "subcontractorCost": 0
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 750
            },
            "actual": {
                "total": 750
            },
            "grossProfit": 637.5
        },
        {
            "_id": "13a56491-e39b-479c-8477-3152bf5d9bf9",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "22f937e4-9206-4b37-84a2-9acc250d557d",
            "PO": "HOPK0487",
            "num": "01",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-26T19:44:00.000Z",
            "oppDate": "2025-02-26T19:44:00.000Z",
            "warrantyType": false,
            "changeOrders": [
                {
                    "name": "10% Discount",
                    "description": "10% Discount",
                    "materials": 0,
                    "rawMatCost": 0,
                    "labor": 0,
                    "rawLaborCost": 0,
                    "addCost": -741,
                    "jobCost": -741,
                    "tax": -57.06,
                    "total": -798.06,
                    "signedBySales": true,
                    "date": "2025-03-03T08:00:00.000Z",
                    "mMarkup": 0,
                    "workTask": "Roofing ($32/Hr)",
                    "manHours": 0,
                    "overrideTotalCost": true,
                    "manHourCost": 0,
                    "laborBurden": 0,
                    "num": 1,
                    "deleted": false,
                    "markupEnabled": false,
                    "workTaskValue": 32,
                    "isSubContractor": false,
                    "modificationId": "2bbb0eec-066e-47b4-8fb6-4a08ea42bbc0"
                }
            ],
            "needsAssessmentDate": "2025-03-03T21:00:00.000Z",
            "budgetScore": 11.7,
            "acceptedProjectId": "d27a51b6-f503-4c4b-ba66-40403698662f",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "42051a02-bc58-4b47-b4ee-e74aa6654e6c",
            "salesCommission": {
                "total": 1111.35,
                "sale": 555.67,
                "start": 0,
                "completed": 555.68,
                "modificationAmount": 0
            },
            "soldValue": 7409,
            "presentationDate": "2025-03-03T22:30:00.000Z",
            "saleDate": "2025-03-03T22:45:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-03-25T00:13:00.000Z",
            "changeOrderValue": -741,
            "order": {
                "priceTotals": {
                    "grandTotal": 7409,
                    "cashTotal": 6297.45,
                    "financeTotal": 6297.45,
                    "jobCost": 2182.77,
                    "lBurden": 220.59,
                    "lSubtotal": 832.42,
                    "lTotal": 1053.01,
                    "laborCost": 668.16,
                    "mMarkup": 53.8,
                    "mTotal": 1129.77,
                    "matCost": 1075.97,
                    "matTax": 0,
                    "overhead": 3181.66,
                    "profit": 933.25,
                    "commission": 1111.31,
                    "travelFee": 164.26,
                    "upsell": 0,
                    "jobTotal": 7409,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 570.49,
                    "discount": 0,
                    "discountComm": 0,
                    "actRev": 6279.23,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 6668,
                    "actualLaborCost": 916.5,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 840.45,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 840.45
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 6668
            },
            "actual": {
                "total": 6668
            },
            "grossProfit": 3726.95
        },
        {
            "_id": "3b92fd16-51d3-459e-b7da-09e2b76b99bb",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "0277a92d-262a-4e24-8bfe-3c46bfe30cb5",
            "PO": "PIHL0706",
            "num": "02",
            "salesPerson": "3d092960-481a-49e6-a030-6525688d51ce",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-27T16:11:00.000Z",
            "oppDate": "2025-02-27T16:11:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-02-28T18:00:00.000Z",
            "budgetScore": 9.7,
            "presentationDate": "2025-02-28T18:05:00.000Z",
            "acceptedProjectId": "f9b942b5-bc3d-4fca-a51f-e914d505c5d0",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "ddccd8f3-eca7-47ff-ad52-c728e9191e89",
            "salesCommission": {
                "total": 585,
                "sale": 292.5,
                "start": 0,
                "completed": 292.5,
                "modificationAmount": 0
            },
            "soldValue": 4500,
            "saleDate": "2025-03-01T01:40:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-03-27T13:41:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 5128,
                    "cashTotal": 4358.44,
                    "financeTotal": 4358.44,
                    "jobCost": 1514.41,
                    "lBurden": 152.47,
                    "lSubtotal": 575.36,
                    "lTotal": 727.83,
                    "laborCost": 556.8,
                    "mMarkup": 37.46,
                    "mTotal": 786.58,
                    "matCost": 706.72,
                    "matTax": 42.4,
                    "overhead": 2199.14,
                    "profit": 645.31,
                    "commission": 674.94,
                    "travelFee": 18.56,
                    "upsell": 0,
                    "jobTotal": 4500,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 628,
                    "discountComm": 94.2,
                    "actRev": 3713.42,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 4500,
                    "actualLaborCost": 504.24,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 302.92,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 302.92
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 4500
            },
            "actual": {
                "total": 4500
            },
            "grossProfit": 2884.28
        },
        {
            "_id": "0cadba65-19c6-4231-92f8-c2bb89636bb5",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "0320b322-ca15-4d1f-b28a-d0f2bd89a8c9",
            "PO": "DEPE0088",
            "num": "01",
            "salesPerson": "3d092960-481a-49e6-a030-6525688d51ce",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-27T17:14:00.000Z",
            "oppDate": "2025-02-27T17:14:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-02-28T21:00:00.000Z",
            "budgetScore": 6.6,
            "presentationDate": "2025-02-28T21:53:00.000Z",
            "acceptedProjectId": "97e02472-c809-41e6-8f40-a217b4f2d604",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "2b9e2f0a-0c0d-424f-924a-fde58cc8d30a",
            "salesCommission": {
                "total": 800,
                "sale": 400,
                "start": 0,
                "completed": 400,
                "modificationAmount": 0
            },
            "soldValue": 10000,
            "saleDate": "2025-03-13T14:39:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-04-16T01:18:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 10487,
                    "cashTotal": 9232.94,
                    "financeTotal": 9232.94,
                    "jobCost": 4448.48,
                    "lBurden": 317.88,
                    "lSubtotal": 1199.56,
                    "lTotal": 1517.44,
                    "laborCost": 1103.04,
                    "mMarkup": 139.57,
                    "mTotal": 3136.01,
                    "matCost": 2633.46,
                    "matTax": 158.01,
                    "overhead": 3162.03,
                    "profit": 927.69,
                    "commission": 999.95,
                    "travelFee": 96.52,
                    "upsell": 695.18,
                    "permit": 204.97,
                    "asbTest": 0,
                    "jobTotal": 10000,
                    "salesTax": 0,
                    "discount": 487,
                    "discountComm": 48.7,
                    "actRev": 6863.99,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 10000,
                    "actualLaborCost": 1182.1,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 2077.52,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 2077.52
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 10136.85
            },
            "actual": {
                "total": 10000
            },
            "grossProfit": 5427.17
        },
        {
            "_id": "52bbe022-1f6a-4db1-b8ee-d5ba3cf5aea0",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "df36d465-e850-414f-bf06-febb629e79ff",
            "PO": "MEID4210",
            "num": "01",
            "salesPerson": "c081a3f6-1e74-4532-8794-03665fea2bdf",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-28T18:29:00.000Z",
            "oppDate": "2025-02-28T18:29:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "budgetScore": 8.6,
            "acceptedProjectId": "e22cfef2-f649-4ca8-b35f-b043e699bb8b",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "ecc52cc1-4a08-483a-bca6-9368b68d5dda",
            "salesCommission": {
                "total": 352.69,
                "sale": 176.35,
                "start": 0,
                "completed": 176.34,
                "modificationAmount": 0
            },
            "soldValue": 2713,
            "needsAssessmentDate": "2025-02-28T21:02:00.000Z",
            "presentationDate": "2025-02-28T21:02:00.000Z",
            "saleDate": "2025-03-13T18:22:00.000Z",
            "jobCompletedDate": "2025-03-20T22:27:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "order": {
                "priceTotals": {
                    "grandTotal": 3192,
                    "cashTotal": 2713.12,
                    "financeTotal": 2713.12,
                    "jobCost": 836.4,
                    "lBurden": 100.61,
                    "lSubtotal": 379.67,
                    "lTotal": 480.28,
                    "laborCost": 349.12,
                    "mMarkup": 16.96,
                    "mTotal": 356.12,
                    "matCost": 319.96,
                    "matTax": 19.2,
                    "overhead": 1451.17,
                    "profit": 425.65,
                    "commission": 406.94,
                    "travelFee": 30.55,
                    "upsell": 0,
                    "jobTotal": 2713,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 479,
                    "discountComm": 71.85,
                    "actRev": 2356.88,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 2713,
                    "actualLaborCost": 518.06,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 495.61,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 495.61
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 2713
            },
            "actual": {
                "total": 2713
            },
            "grossProfit": 1155.1
        },
        {
            "_id": "5ed18481-7aab-4925-accc-61fa0f500dc4",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "bb7f9fdd-d56c-4f16-8ce4-e5238862be91",
            "PO": "CEIQ1807",
            "num": "01",
            "salesPerson": "c081a3f6-1e74-4532-8794-03665fea2bdf",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-02-28T18:43:00.000Z",
            "oppDate": "2025-02-28T19:02:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-05T17:00:00.000Z",
            "presentationDate": "2025-03-05T19:03:00.000Z",
            "budgetScore": 0.7,
            "acceptedProjectId": "51eef07b-fb72-450e-853c-7fd5350c85aa",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "8afc2f7e-be1c-40a0-8ae2-67ba356c8891",
            "salesCommission": {
                "total": 3508.8,
                "sale": 1754.4,
                "start": 0,
                "completed": 1754.4,
                "modificationAmount": 0
            },
            "soldValue": 43860,
            "saleDate": "2025-03-28T01:06:00.000Z",
            "workingCrew": {
                "name": "Priority Roofing",
                "id": "3154c0ec-47da-42b0-881f-5d434d6cacc6"
            },
            "jobCompletedDate": "2025-05-16T00:03:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 48734,
                    "cashTotal": 42701.73,
                    "financeTotal": 42701.73,
                    "jobCost": 21285.1,
                    "lBurden": 1612.63,
                    "lSubtotal": 6085.41,
                    "lTotal": 7698.04,
                    "laborCost": 4967.68,
                    "mMarkup": 647,
                    "mTotal": 14329.83,
                    "matCost": 12940.05,
                    "matTax": 0,
                    "overhead": 16041.14,
                    "profit": 4704.65,
                    "commission": 4339.77,
                    "travelFee": 1117.73,
                    "upsell": 671.48,
                    "permit": 642.77,
                    "asbTest": 100,
                    "jobTotal": 43860,
                    "salesTax": 3552.66,
                    "discount": 4874,
                    "discountComm": 487.4,
                    "actRev": 29530.17,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 43860,
                    "actualLaborCost": 0,
                    "actualDays": 0,
                    "subcontractorCost": 9018.75,
                    "inventoryCost": 0,
                    "qbCOGS": 12397.78,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 12397.78
                },
                "modifiedBudget": {
                    "subContractorTotal": 9019,
                    "isSubcontractorOnly": true,
                    "rawLaborBudget": 0
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 43860
            },
            "actual": {
                "total": 43860
            },
            "grossProfit": 18103.7
        },
        {
            "_id": "9d73e785-46ad-4950-bcce-5c8534f12d00",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "fddd6b0b-acec-40b4-963b-69a20f658f16",
            "PO": "FENC2430",
            "num": "01",
            "salesPerson": "3d092960-481a-49e6-a030-6525688d51ce",
            "stage": "a01949b3-e299-4d08-b5b4-18b3f17ed63c",
            "newLeadDate": "2025-02-28T21:02:00.000Z",
            "oppDate": "2025-02-28T21:02:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-04T21:00:00.000Z",
            "budgetScore": 7.5,
            "presentationDate": "2025-03-04T21:59:00.000Z",
            "acceptedProjectId": "cb80b33e-ab16-4d82-87f9-9f13b0936a65",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "b4aa9a68-216a-4d9b-a553-3193f916c700",
            "salesCommission": {
                "total": 3000.32,
                "sale": 1500.16,
                "start": 0,
                "completed": 1500.16,
                "modificationAmount": 0
            },
            "soldValue": 37504,
            "saleDate": "2025-03-24T22:28:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 38704,
                    "cashTotal": 34283.83,
                    "financeTotal": 34283.83,
                    "jobCost": 17134.1,
                    "lBurden": 1139.44,
                    "lSubtotal": 4299.76,
                    "lTotal": 5439.2,
                    "laborCost": 4281.92,
                    "mMarkup": 556.9,
                    "mTotal": 12244.05,
                    "matCost": 10507.55,
                    "matTax": 630.45,
                    "overhead": 11334.17,
                    "profit": 3324.41,
                    "commission": 3750.33,
                    "travelFee": 17.84,
                    "upsell": 2491.84,
                    "permit": 549.14,
                    "asbTest": 0,
                    "jobTotal": 37504,
                    "salesTax": 0,
                    "discount": 1200,
                    "discountComm": 120,
                    "actRev": 25259.95,
                    "travelHrlyRate": 32
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 37504
            },
            "actual": {
                "total": 0
            },
            "grossProfit": -3750.33
        },
        {
            "_id": "0b89d4ae-a59c-46f5-a11e-0abe9cc17ebd",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "650159e9-d343-4d63-91c1-f5746734d628",
            "PO": "WILL1405",
            "num": "03",
            "salesPerson": "05c60407-7f57-4330-b3d3-bd24b96e0fee",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-03T16:20:00.000Z",
            "oppDate": "2025-03-03T16:20:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-05T17:00:00.000Z",
            "budgetScore": 17.1,
            "presentationDate": "2025-03-05T18:52:00.000Z",
            "acceptedProjectId": "c66ba116-6b69-4e45-af11-b3771d79ee7a",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "04bf16d0-3396-4fca-b73c-493bcfaf53e8",
            "salesCommission": {
                "total": 147.35,
                "sale": 73.68,
                "start": 0,
                "completed": 73.67,
                "modificationAmount": 0
            },
            "soldValue": 2947,
            "saleDate": "2025-03-05T19:35:00.000Z",
            "jobCompletedDate": "2025-03-11T19:01:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 2947,
                    "cashTotal": 2504.37,
                    "financeTotal": 2504.37,
                    "jobCost": 731.86,
                    "lBurden": 95.03,
                    "lSubtotal": 358.59,
                    "lTotal": 453.61,
                    "laborCost": 340.16,
                    "mMarkup": 13.25,
                    "mTotal": 278.25,
                    "matCost": 250,
                    "matTax": 15,
                    "overhead": 1370.58,
                    "profit": 402.61,
                    "commission": 441.95,
                    "travelFee": 18.43,
                    "upsell": 0,
                    "jobTotal": 2947,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 0,
                    "discountComm": 0,
                    "actRev": 2668.75,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 2947,
                    "actualLaborCost": 61.04,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 177.27,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 177.27
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 2947
            },
            "actual": {
                "total": 2947
            },
            "grossProfit": 2250.56
        },
        {
            "_id": "b5e6a7d3-41e0-4cf6-b3e2-5ad6c1842546",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "604f5fe0-f355-4df6-a86e-1c7c328dd381",
            "PO": "COWA4668",
            "num": "01",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-01T16:39:00.000Z",
            "oppDate": "2025-03-01T16:41:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-07T18:00:00.000Z",
            "budgetScore": 3.4,
            "presentationDate": "2025-03-07T19:30:00.000Z",
            "acceptedProjectId": "e5757df4-2fc0-46a9-bded-d1c57f6a37fe",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "7c6e92e7-c1d2-45f7-bce8-670949ce0258",
            "salesCommission": {
                "total": 3024.2,
                "sale": 1512.1,
                "start": 0,
                "completed": 1512.1,
                "modificationAmount": 0
            },
            "soldValue": 30242,
            "saleDate": "2025-03-25T00:30:00.000Z",
            "workingCrew": {
                "name": "Mejias Construction LLC",
                "id": "be91ce59-a15c-466c-823d-58f14a8c08ed"
            },
            "jobCompletedDate": "2025-05-03T00:12:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 33603,
                    "cashTotal": 29745.54,
                    "financeTotal": 29745.54,
                    "jobCost": 14166.34,
                    "lBurden": 1035.09,
                    "lSubtotal": 3906,
                    "lTotal": 4941.09,
                    "laborCost": 3720,
                    "mMarkup": 439.3,
                    "mTotal": 9722.38,
                    "matCost": 8288.64,
                    "matTax": 497.32,
                    "overhead": 10296.22,
                    "profit": 3019.37,
                    "commission": 3024.2,
                    "travelFee": 186,
                    "upsell": 2263.64,
                    "permit": 497.13,
                    "asbTest": 0,
                    "jobTotal": 30242,
                    "salesTax": 0,
                    "discount": 3361,
                    "discountComm": 336.1,
                    "actRev": 20519.62,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 30914,
                    "actualLaborCost": 45.22,
                    "actualDays": 0,
                    "subcontractorCost": 5065,
                    "inventoryCost": 0,
                    "qbCOGS": 2281.82,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 2281.82
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 30914
            },
            "actual": {
                "total": 30914
            },
            "grossProfit": 20485.78
        },
        {
            "_id": "6dcaa6d7-7474-41e4-8fa4-f7f9908c9c80",
            "oppType": "798feebb-2e45-4fd5-9995-abe0a7396b7c",
            "clientId": "eaac39af-872a-479e-bc93-fd2732c1dddb",
            "PO": "BETC2415",
            "num": "01",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-01T18:09:00.000Z",
            "oppDate": "2025-03-01T18:09:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-10T21:00:00.000Z",
            "budgetScore": 4.3,
            "presentationDate": "2025-03-10T22:00:00.000Z",
            "acceptedProjectId": "3c9606c6-77da-489d-9af7-1c629227d656",
            "acceptedType": "798feebb-2e45-4fd5-9995-abe0a7396b7c",
            "orderId": "d81203eb-b721-4d7c-acc9-822c2bb57269",
            "salesCommission": {
                "total": 172.9,
                "sale": 86.45,
                "start": 0,
                "completed": 86.45,
                "modificationAmount": 0
            },
            "soldValue": 1729,
            "saleDate": "2025-03-27T17:01:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-04-30T14:25:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 1928,
                    "cashTotal": 1710.68,
                    "financeTotal": 1710.68,
                    "jobCost": 635.22,
                    "lBurden": 72.05,
                    "lSubtotal": 271.9,
                    "lTotal": 343.95,
                    "laborCost": 205.76,
                    "mMarkup": 13.87,
                    "mTotal": 291.26,
                    "matCost": 277.39,
                    "matTax": 0,
                    "overhead": 831.59,
                    "profit": 245.11999999999998,
                    "commission": 189.88,
                    "travelFee": 66.14,
                    "upsell": 0,
                    "jobTotal": 1729,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 155.61,
                    "discount": 192,
                    "discountComm": 19.2,
                    "actRev": 1437.74,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 1729,
                    "actualLaborCost": 681.42,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 317.74,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 317.74
                }
            },
            "acceptedProjectDetails": {
                "name": "Gutters"
            },
            "budget": {
                "total": 1729
            },
            "actual": {
                "total": 1729
            },
            "grossProfit": 359.38
        },
        {
            "_id": "6dcaa6d7-7474-41e4-8fa4-f7f9908c9c80",
            "oppType": "798feebb-2e45-4fd5-9995-abe0a7396b7c",
            "clientId": "eaac39af-872a-479e-bc93-fd2732c1dddb",
            "PO": "BETC2415",
            "num": "01",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-01T18:09:00.000Z",
            "oppDate": "2025-03-01T18:09:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-10T21:00:00.000Z",
            "budgetScore": 4.3,
            "presentationDate": "2025-03-10T22:00:00.000Z",
            "acceptedProjectId": "3c9606c6-77da-489d-9af7-1c629227d656",
            "acceptedType": "798feebb-2e45-4fd5-9995-abe0a7396b7c",
            "orderId": "d81203eb-b721-4d7c-acc9-822c2bb57269",
            "salesCommission": {
                "total": 172.9,
                "sale": 86.45,
                "start": 0,
                "completed": 86.45,
                "modificationAmount": 0
            },
            "soldValue": 1729,
            "saleDate": "2025-03-27T17:01:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-04-30T14:25:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 1928,
                    "cashTotal": 1710.68,
                    "financeTotal": 1710.68,
                    "jobCost": 635.22,
                    "lBurden": 72.05,
                    "lSubtotal": 271.9,
                    "lTotal": 343.95,
                    "laborCost": 205.76,
                    "mMarkup": 13.87,
                    "mTotal": 291.26,
                    "matCost": 277.39,
                    "matTax": 0,
                    "overhead": 831.59,
                    "profit": 245.11999999999998,
                    "commission": 189.88,
                    "travelFee": 66.14,
                    "upsell": 0,
                    "jobTotal": 1729,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 155.61,
                    "discount": 192,
                    "discountComm": 19.2,
                    "actRev": 1437.74,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 1729,
                    "actualLaborCost": 681.42,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 317.74,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 317.74
                }
            },
            "acceptedProjectDetails": {
                "name": "Gutters"
            },
            "budget": {
                "total": 1729
            },
            "actual": {
                "total": 1729
            },
            "grossProfit": 359.38
        },
        {
            "_id": "dcb07889-562c-4413-93a4-b9abf7be9e89",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "eb44e622-9ff0-45f4-a1d7-f916f2beca75",
            "PO": "CART1691",
            "num": "01",
            "salesPerson": "72ef6006-a140-4aac-9623-de7e1783d2a3",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-04T23:34:00.000Z",
            "oppDate": "2025-03-04T23:34:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-06T21:00:00.000Z",
            "budgetScore": -0.7,
            "presentationDate": "2025-03-06T21:00:00.000Z",
            "acceptedProjectId": "6279f388-a9f7-4304-96e1-5f5a90ccbe35",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "ea3a0081-c459-42a3-a10d-145aa662b40b",
            "salesCommission": {
                "total": 1373.45,
                "sale": 686.73,
                "start": 0,
                "completed": 686.72,
                "modificationAmount": 0
            },
            "soldValue": 27469,
            "saleDate": "2025-03-12T17:30:00.000Z",
            "workingCrew": {
                "name": "Safe Roofing Services LLC",
                "id": "2c23b256-35e8-4ee6-b6b2-4765d0f57801"
            },
            "jobCompletedDate": "2025-04-03T13:38:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 30594,
                    "cashTotal": 26978.22,
                    "financeTotal": 26978.22,
                    "jobCost": 14477.26,
                    "lBurden": 952.71,
                    "lSubtotal": 3595.14,
                    "lTotal": 4547.86,
                    "laborCost": 3344.32,
                    "mMarkup": 472.83,
                    "mTotal": 10484.92,
                    "matCost": 9456.57,
                    "matTax": 0,
                    "overhead": 9476.8,
                    "profit": 2780,
                    "commission": 2746.8,
                    "travelFee": 250.82,
                    "upsell": 245.12,
                    "permit": 455.52,
                    "asbTest": 100,
                    "jobTotal": 27469,
                    "salesTax": 2444.74,
                    "discount": 3125,
                    "discountComm": 312.5,
                    "actRev": 16984.08,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 27469,
                    "actualLaborCost": 0,
                    "actualDays": 0,
                    "subcontractorCost": 5030,
                    "inventoryCost": 0,
                    "qbCOGS": 10551.35,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 10551.35
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 27469
            },
            "actual": {
                "total": 27469
            },
            "grossProfit": 9140.85
        },
        {
            "_id": "be30501a-9e23-464c-a626-6d9a40598a54",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "a592b328-662f-4028-a288-3ffa5e8d6ff3",
            "PO": "JOHN0600",
            "num": "01",
            "salesPerson": "05c60407-7f57-4330-b3d3-bd24b96e0fee",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-06T16:43:00.000Z",
            "oppDate": "2025-03-06T16:43:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-07T21:00:00.000Z",
            "budgetScore": 9.9,
            "presentationDate": "2025-03-07T21:16:00.000Z",
            "saleDate": "2025-03-07T21:36:00.000Z",
            "workingCrew": {
                "name": "Repair Crew",
                "id": "96327288-f17c-469b-82b9-63d017f9662c"
            },
            "jobCompletedDate": "2025-03-07T18:01:00.000Z",
            "acceptedProjectId": "d713a7c2-640a-4b0a-afa1-840c24550a86",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "9debff24-4c56-4db4-a595-cdd0715f6da2",
            "salesCommission": {
                "total": 29.2,
                "sale": 14.6,
                "start": 0,
                "completed": 14.6,
                "modificationAmount": 0
            },
            "soldValue": 584,
            "order": {
                "priceTotals": {
                    "grandTotal": 684,
                    "cashTotal": 580.8,
                    "financeTotal": 580.8,
                    "jobCost": 124.2,
                    "lBurden": 24.48,
                    "lSubtotal": 92.37,
                    "lTotal": 116.85,
                    "laborCost": 90.24,
                    "mMarkup": 0.35,
                    "mTotal": 7.35,
                    "matCost": 6.6,
                    "matTax": 0.4,
                    "overhead": 353.06,
                    "profit": 104.24,
                    "commission": 87.49,
                    "travelFee": 2.13,
                    "upsell": 0,
                    "jobTotal": 584,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 100,
                    "discountComm": 15,
                    "actRev": 576.65,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 584,
                    "actualLaborCost": 27.16,
                    "actualDays": 0,
                    "subcontractorCost": 0
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 584
            },
            "actual": {
                "total": 584
            },
            "grossProfit": 462.15
        },
        {
            "_id": "0c924e81-888c-4ab6-90ec-49d6fac3dc98",
            "oppType": "798feebb-2e45-4fd5-9995-abe0a7396b7c",
            "clientId": "4ca787ac-c1b7-48ed-908d-a4943c3a371d",
            "PO": "DOOL1208",
            "num": "01",
            "salesPerson": "72ef6006-a140-4aac-9623-de7e1783d2a3",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-07T17:52:00.000Z",
            "oppDate": "2025-03-07T17:52:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-12T22:30:00.000Z",
            "budgetScore": 5,
            "acceptedProjectId": "039fe88c-9407-43cb-babd-e6651d609102",
            "acceptedType": "798feebb-2e45-4fd5-9995-abe0a7396b7c",
            "orderId": "a6819117-2e21-4ac0-a6b3-94973df697f3",
            "salesCommission": {
                "total": 44.3,
                "sale": 22.15,
                "start": 0,
                "completed": 22.15,
                "modificationAmount": 0
            },
            "soldValue": 886,
            "presentationDate": "2025-03-12T22:30:00.000Z",
            "saleDate": "2025-03-12T23:45:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-03-28T13:33:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 936,
                    "cashTotal": 842.27,
                    "financeTotal": 842.27,
                    "jobCost": 333.89,
                    "lBurden": 35.93,
                    "lSubtotal": 135.57,
                    "lTotal": 171.5,
                    "laborCost": 107.84,
                    "mMarkup": 7.73,
                    "mTotal": 162.39,
                    "matCost": 154.65,
                    "matTax": 0,
                    "overhead": 393.11,
                    "profit": 115.42,
                    "commission": 88.59,
                    "travelFee": 27.73,
                    "upsell": 0,
                    "jobTotal": 886,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 79.74,
                    "discount": 50,
                    "discountComm": 5,
                    "actRev": 723.61,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 886,
                    "actualLaborCost": 431.11,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 149.22,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 149.22
                }
            },
            "acceptedProjectDetails": {
                "name": "Gutters"
            },
            "budget": {
                "total": 886
            },
            "actual": {
                "total": 886
            },
            "grossProfit": 102.84
        },
        {
            "_id": "188a74ec-258a-4009-9c08-aa90929c8a85",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "3f1ccabc-9dc0-4166-9844-5c0a9c027b1a",
            "PO": "ROGE3057",
            "num": "01",
            "salesPerson": "c081a3f6-1e74-4532-8794-03665fea2bdf",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-08T00:41:00.000Z",
            "oppDate": "2025-03-08T00:41:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-10T15:00:00.000Z",
            "budgetScore": 7.6,
            "acceptedProjectId": "cc57b4ab-40ce-4bc4-af2f-7624ac245b03",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "eae8d558-b533-4144-963d-b37ec8cafe25",
            "salesCommission": {
                "total": 328.25,
                "sale": 164.13,
                "start": 0,
                "completed": 164.12,
                "modificationAmount": 0
            },
            "soldValue": 2525,
            "presentationDate": "2025-03-10T16:23:00.000Z",
            "saleDate": "2025-03-18T19:30:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-03-27T19:32:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 2971,
                    "cashTotal": 2525.03,
                    "financeTotal": 2525.03,
                    "jobCost": 964.82,
                    "lBurden": 83.64,
                    "lSubtotal": 315.64,
                    "lTotal": 399.28,
                    "laborCost": 298.24,
                    "mMarkup": 26.93,
                    "mTotal": 565.54,
                    "matCost": 508.12,
                    "matTax": 30.49,
                    "overhead": 1206.43,
                    "profit": 354.16,
                    "commission": 378.69,
                    "travelFee": 17.4,
                    "upsell": 0,
                    "jobTotal": 2525,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 446,
                    "discountComm": 66.9,
                    "actRev": 1959.46,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 2525,
                    "actualLaborCost": 328,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 312.33,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 312.33
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 2525
            },
            "actual": {
                "total": 2525
            },
            "grossProfit": 1419.06
        },
        {
            "_id": "212078bd-747e-4e86-a5d5-7611c544377a",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "54ad554f-7cd8-40f3-b5e3-b9d2560b32f7",
            "PO": "JOHN1053",
            "num": "01",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-10T17:11:00.000Z",
            "oppDate": "2025-03-10T17:11:00.000Z",
            "warrantyType": false,
            "changeOrders": [
                {
                    "name": "Change Order",
                    "description": "Remove Installation of 7/16 OSB from job scope\nAdditional Man hours 2 @ 119.00",
                    "materials": 0,
                    "rawMatCost": 0,
                    "labor": 0,
                    "rawLaborCost": 0,
                    "addCost": -978,
                    "jobCost": -978,
                    "tax": 0,
                    "total": -978,
                    "signedBySales": true,
                    "date": "2025-03-20T07:00:00.000Z",
                    "mMarkup": 0,
                    "workTask": "Roofing ($32/Hr)",
                    "manHours": 0,
                    "overrideTotalCost": true,
                    "manHourCost": 0,
                    "laborBurden": 0,
                    "num": 1,
                    "deleted": false,
                    "markupEnabled": false,
                    "workTaskValue": 32,
                    "isSubContractor": false,
                    "modificationId": null,
                    "oldJobCost": 238,
                    "oldMaterials": 0
                }
            ],
            "needsAssessmentDate": "2025-03-17T17:00:00.000Z",
            "budgetScore": -15.2,
            "presentationDate": "2025-03-17T18:30:00.000Z",
            "acceptedProjectId": "71a79469-47e7-4ee3-b197-8761d7ad0c75",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "5c6fce0e-36c4-4e69-9421-cb70a999e8b8",
            "salesCommission": {
                "total": 455.5,
                "sale": 227.75,
                "start": 0,
                "completed": 227.75,
                "modificationAmount": 0
            },
            "soldValue": 4555,
            "saleDate": "2025-03-20T19:30:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-04-01T22:39:00.000Z",
            "changeOrderValue": -978,
            "order": {
                "priceTotals": {
                    "grandTotal": 5358,
                    "cashTotal": 4667.54,
                    "financeTotal": 4667.54,
                    "jobCost": 2524.77,
                    "lBurden": 166.57,
                    "lSubtotal": 628.57,
                    "lTotal": 795.14,
                    "laborCost": 593.92,
                    "mMarkup": 82.36,
                    "mTotal": 1884.13,
                    "matCost": 1554.03,
                    "matTax": 93.24,
                    "overhead": 1656.9,
                    "profit": 486.05,
                    "commission": 455.49,
                    "travelFee": 34.65,
                    "upsell": 0,
                    "permit": 154.5,
                    "asbTest": 0,
                    "jobTotal": 4555,
                    "salesTax": 0,
                    "discount": 803,
                    "discountComm": 80.3,
                    "actRev": 2670.87,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 3577,
                    "actualLaborCost": 607.89,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 1327.01,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 1327.01
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 3577
            },
            "actual": {
                "total": 3577
            },
            "grossProfit": 1123.32
        },
        {
            "_id": "82160a7e-d603-4df1-a0cf-bad0bb8107d4",
            "oppType": "798feebb-2e45-4fd5-9995-abe0a7396b7c",
            "clientId": "9d18dc40-bf7e-4e36-a7d3-46869029ddc8",
            "PO": "QUIN2103",
            "num": "01",
            "salesPerson": "b1bb00eb-11ec-4208-a623-b36cd40e0da1",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-11T21:13:00.000Z",
            "oppDate": "2025-03-11T21:13:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-12T21:00:00.000Z",
            "presentationDate": "2025-03-13T19:51:00.000Z",
            "budgetScore": 3.8,
            "acceptedProjectId": "213702e2-e472-4671-b804-1004ab7ff567",
            "acceptedType": "798feebb-2e45-4fd5-9995-abe0a7396b7c",
            "orderId": "07cdc09b-277f-4fef-b98b-42a2fa5c7248",
            "salesCommission": {
                "total": 93.95,
                "sale": 46.98,
                "start": 0,
                "completed": 46.97,
                "modificationAmount": 0
            },
            "soldValue": 1879,
            "saleDate": "2025-03-15T20:22:00.000Z",
            "jobCompletedDate": "2025-04-15T13:50:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "order": {
                "priceTotals": {
                    "grandTotal": 2029,
                    "cashTotal": 1825.82,
                    "financeTotal": 1825.82,
                    "jobCost": 783.47,
                    "lBurden": 73.66,
                    "lSubtotal": 277.97,
                    "lTotal": 351.63,
                    "laborCost": 253.44,
                    "mMarkup": 20.56,
                    "mTotal": 431.84,
                    "matCost": 411.28,
                    "matTax": 0,
                    "overhead": 806,
                    "profit": 236.67,
                    "commission": 187.87,
                    "travelFee": 24.53,
                    "upsell": 0,
                    "jobTotal": 1879,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 169.11,
                    "discount": 150,
                    "discountComm": 15,
                    "actRev": 1447.16,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 1879,
                    "actualLaborCost": 559.51,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 398.62,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 398.62
                }
            },
            "acceptedProjectDetails": {
                "name": "Gutters"
            },
            "budget": {
                "total": 1879
            },
            "actual": {
                "total": 1879
            },
            "grossProfit": 584.73
        },
        {
            "_id": "c7d350b8-24a1-4948-904f-aa410e6a4879",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "bd233823-b610-4211-a091-5ba2e9c26a0d",
            "PO": "BEAC3286",
            "num": "01",
            "salesPerson": "05c60407-7f57-4330-b3d3-bd24b96e0fee",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-13T20:09:00.000Z",
            "oppDate": "2025-03-13T20:09:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-13T21:30:00.000Z",
            "budgetScore": 61,
            "acceptedProjectId": "2464424c-4531-4bca-87be-2d641bcdb646",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "4ab8a391-f0c3-4d70-9803-f368e6fedc55",
            "salesCommission": {
                "total": 17.5,
                "sale": 8.75,
                "start": 0,
                "completed": 8.75,
                "modificationAmount": 0
            },
            "soldValue": 350,
            "presentationDate": "2025-03-13T21:31:00.000Z",
            "saleDate": "2025-03-13T21:32:00.000Z",
            "workingCrew": {
                "name": "Repair Crew",
                "id": "96327288-f17c-469b-82b9-63d017f9662c"
            },
            "jobCompletedDate": "2025-03-13T17:01:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 350,
                    "cashTotal": 172.93,
                    "financeTotal": 172.93,
                    "jobCost": 42.7,
                    "lBurden": 6.98,
                    "lSubtotal": 26.35,
                    "lTotal": 33.33,
                    "laborCost": 12.48,
                    "mMarkup": 0.45,
                    "mTotal": 9.38,
                    "matCost": 8.43,
                    "matTax": 0.51,
                    "overhead": 100.7,
                    "profit": 30.08,
                    "commission": 30.52,
                    "travelFee": 13.87,
                    "upsell": 0,
                    "jobTotal": 350,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 0,
                    "discountComm": 0,
                    "actRev": 340.62,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 350,
                    "actualLaborCost": 28,
                    "actualDays": 0,
                    "subcontractorCost": 0
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 350
            },
            "actual": {
                "total": 350
            },
            "grossProfit": 284.06
        },
        {
            "_id": "a0a60bf7-4938-4579-9b54-d5cdaa288ca4",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "061397da-23ec-43e6-b84f-b47d4f3ae44b",
            "PO": "PAIN3833",
            "num": "W01",
            "salesPerson": "05c60407-7f57-4330-b3d3-bd24b96e0fee",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-13T23:32:00.000Z",
            "oppDate": "2025-03-13T23:35:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "acceptedProjectId": "660e7a72-5563-4dc9-b343-3778a63a7fb1",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "3858e2bd-d884-4b09-9884-96ce479e386c",
            "salesCommission": {
                "total": 0,
                "sale": 0,
                "start": 0,
                "completed": 0,
                "modificationAmount": 0
            },
            "soldValue": 0,
            "needsAssessmentDate": "2025-03-25T14:42:00.000Z",
            "presentationDate": "2025-03-25T14:42:00.000Z",
            "saleDate": "2025-03-25T14:43:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-03-26T14:44:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 902,
                    "cashTotal": 766.11,
                    "financeTotal": 766.11,
                    "jobCost": 156.11,
                    "lBurden": 32.7,
                    "lSubtotal": 123.41,
                    "lTotal": 156.11,
                    "laborCost": 102.08,
                    "mMarkup": 0,
                    "mTotal": 0,
                    "matCost": 0,
                    "matTax": 0,
                    "overhead": 471.69,
                    "profit": 139.01,
                    "commission": 0,
                    "travelFee": 21.33,
                    "upsell": 0,
                    "jobTotal": 0,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 902,
                    "discountComm": 0,
                    "actRev": 0,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 0,
                    "actualLaborCost": 61.04,
                    "actualDays": 0,
                    "subcontractorCost": 0
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 0
            },
            "actual": {
                "total": 0
            },
            "grossProfit": -77.22
        },
        {
            "_id": "2a792a2c-36e7-4c23-96da-96a13a7360f7",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "c368367a-f0fe-4978-a773-8bd59f468130",
            "PO": "GALL6538",
            "num": "01",
            "salesPerson": "c081a3f6-1e74-4532-8794-03665fea2bdf",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-14T23:16:00.000Z",
            "oppDate": "2025-03-14T23:16:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-19T17:00:00.000Z",
            "budgetScore": 2.9,
            "acceptedProjectId": "de5fc705-05f6-441d-9325-0e9fe25085b2",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "d4a99423-4bd9-4559-82f5-7ab1cf415d83",
            "salesCommission": {
                "total": 1783.44,
                "sale": 891.72,
                "start": 0,
                "completed": 891.72,
                "modificationAmount": 0
            },
            "soldValue": 22293,
            "presentationDate": "2025-03-19T21:35:00.000Z",
            "saleDate": "2025-03-20T18:05:00.000Z",
            "workingCrew": {
                "name": "Mejias Construction LLC",
                "id": "be91ce59-a15c-466c-823d-58f14a8c08ed"
            },
            "jobCompletedDate": "2025-04-17T13:28:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 24770,
                    "cashTotal": 21885.34,
                    "financeTotal": 21885.34,
                    "jobCost": 11244.47,
                    "lBurden": 706.98,
                    "lSubtotal": 2667.87,
                    "lTotal": 3374.85,
                    "laborCost": 2328.32,
                    "mMarkup": 374.74,
                    "mTotal": 8276.47,
                    "matCost": 7070.64,
                    "matTax": 424.24,
                    "overhead": 7032.5,
                    "profit": 2063.16,
                    "commission": 2229.21,
                    "travelFee": 339.55,
                    "upsell": 1546.11,
                    "permit": 406.85,
                    "asbTest": 0,
                    "jobTotal": 22293,
                    "salesTax": 0,
                    "discount": 2477,
                    "discountComm": 247.7,
                    "actRev": 14016.53,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 22293,
                    "actualLaborCost": 26.83,
                    "actualDays": 0,
                    "subcontractorCost": 3500,
                    "inventoryCost": 0,
                    "qbCOGS": 7945.94,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 7945.94
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 22293
            },
            "actual": {
                "total": 22293
            },
            "grossProfit": 8583.91
        },
        {
            "_id": "a28a4f21-4e3a-49f8-bd6c-1e5e514c76c3",
            "oppType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "clientId": "434961be-dff8-4332-9625-8ee02df61f56",
            "PO": "BLAC0327",
            "num": "W02",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-21T07:00:00.000Z",
            "oppDate": "2025-03-21T07:00:00.000Z",
            "warrantyType": true,
            "changeOrders": [],
            "acceptedProjectId": "e4904b4c-0713-493b-bbed-dc86234bfd45",
            "acceptedType": "c4948c95-fc97-4780-8029-27573ea9bd96",
            "orderId": "dc28e835-4117-4b8a-aa81-967a2f0cf5f1",
            "salesCommission": {
                "total": 0,
                "sale": 0,
                "start": 0,
                "completed": 0,
                "modificationAmount": 0
            },
            "soldValue": 0,
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-03-20T21:45:00.000Z",
            "needsAssessmentDate": "2025-03-21T13:48:00.000Z",
            "presentationDate": "2025-03-21T13:48:00.000Z",
            "saleDate": "2025-03-21T13:48:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 2153,
                    "cashTotal": 1829.31,
                    "financeTotal": 1829.31,
                    "jobCost": 372.75,
                    "lBurden": 78.09,
                    "lSubtotal": 294.67,
                    "lTotal": 372.75,
                    "laborCost": 272,
                    "mMarkup": 0,
                    "mTotal": 0,
                    "matCost": 0,
                    "matTax": 0,
                    "overhead": 1126.27,
                    "profit": 331.15,
                    "commission": 0,
                    "travelFee": 22.67,
                    "upsell": 0,
                    "jobTotal": 0,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 0,
                    "discount": 2153,
                    "discountComm": 0,
                    "actRev": 0,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 0,
                    "actualLaborCost": 258.65,
                    "actualDays": 0,
                    "subcontractorCost": 0
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Repair"
            },
            "budget": {
                "total": 0
            },
            "actual": {
                "total": 0
            },
            "grossProfit": -327.19
        },
        {
            "_id": "483f445b-5daa-440f-bede-21c6e85302d2",
            "oppType": "798feebb-2e45-4fd5-9995-abe0a7396b7c",
            "clientId": "d270b80b-1189-4e55-92ab-8ad315c9c6ca",
            "PO": "GILL5625",
            "num": "01",
            "salesPerson": "c081a3f6-1e74-4532-8794-03665fea2bdf",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-21T16:27:00.000Z",
            "oppDate": "2025-03-21T16:27:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-27T17:30:00.000Z",
            "budgetScore": 2.3,
            "acceptedProjectId": "010e75fe-b141-4a19-8521-3895dff951cd",
            "acceptedType": "798feebb-2e45-4fd5-9995-abe0a7396b7c",
            "orderId": "e21ef55b-8203-4853-a305-89afe9e4f630",
            "salesCommission": {
                "total": 467.8,
                "sale": 233.9,
                "start": 0,
                "completed": 233.9,
                "modificationAmount": 0
            },
            "soldValue": 4678,
            "presentationDate": "2025-03-27T18:38:00.000Z",
            "saleDate": "2025-03-27T18:40:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-04-01T01:26:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 5198,
                    "cashTotal": 4677.94,
                    "financeTotal": 4677.94,
                    "jobCost": 2083.4,
                    "lBurden": 183.35,
                    "lSubtotal": 691.9,
                    "lTotal": 875.25,
                    "laborCost": 653.76,
                    "mMarkup": 57.53,
                    "mTotal": 1208.15,
                    "matCost": 1150.62,
                    "matTax": 0,
                    "overhead": 2006.22,
                    "profit": 588.61,
                    "commission": 467.77,
                    "travelFee": 38.14,
                    "upsell": 0,
                    "jobTotal": 4678,
                    "permit": 0,
                    "asbTest": 0,
                    "salesTax": 378.92,
                    "discount": 520,
                    "discountComm": 52,
                    "actRev": 3469.85,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 4678,
                    "actualLaborCost": 779.15,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 825.12,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 825.12
                }
            },
            "acceptedProjectDetails": {
                "name": "Gutters"
            },
            "budget": {
                "total": 4678
            },
            "actual": {
                "total": 4678
            },
            "grossProfit": 2399.49
        },
        {
            "_id": "6b1f5f80-e47d-4ad9-a708-8ee4c7a4f1c3",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "1b29da1f-0477-45e6-bc07-03972ff1f5dd",
            "PO": "CHRI0213",
            "num": "01",
            "salesPerson": "646c702c-4f9d-4cf5-ac69-228c3e2bf167",
            "stage": "990fb7fe-9256-4ffb-bdbb-4ece2963160b",
            "newLeadDate": "2025-03-22T02:18:41.335Z",
            "oppDate": "2025-03-22T02:48:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-25T17:00:00.000Z",
            "budgetScore": -1,
            "acceptedProjectId": "39d370bc-0f79-447f-a2f1-409db590fe80",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "6b81e54e-c978-4689-9670-0dce6e9812ca",
            "salesCommission": {
                "total": 1441.76,
                "sale": 720.88,
                "start": 0,
                "completed": 720.88,
                "modificationAmount": 0
            },
            "soldValue": 18022,
            "presentationDate": "2025-03-25T17:00:00.000Z",
            "saleDate": "2025-03-25T19:00:00.000Z",
            "workingCrew": {
                "name": "Priority Roofing",
                "id": "3154c0ec-47da-42b0-881f-5d434d6cacc6"
            },
            "order": {
                "priceTotals": {
                    "grandTotal": 20025,
                    "cashTotal": 17687.49,
                    "financeTotal": 17687.49,
                    "jobCost": 9503.72,
                    "lBurden": 636.17,
                    "lSubtotal": 2400.63,
                    "lTotal": 3036.8,
                    "laborCost": 2361.28,
                    "mMarkup": 307.95,
                    "mTotal": 6801.67,
                    "matCost": 5810.35,
                    "matTax": 348.62,
                    "overhead": 6328.07,
                    "profit": 1855.99,
                    "commission": 1802.17,
                    "travelFee": 39.35,
                    "upsell": 0,
                    "permit": 334.75,
                    "asbTest": 0,
                    "jobTotal": 18022,
                    "salesTax": 0,
                    "discount": 2003,
                    "discountComm": 200.3,
                    "actRev": 11220.33,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 0,
                    "actualLaborCost": 22.11,
                    "actualDays": 0,
                    "subcontractorCost": 1.33
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 18022
            },
            "actual": {
                "total": 0
            },
            "grossProfit": -1831.47
        },
        {
            "_id": "7c39021f-24d8-45b2-92f3-f8ce265fad4f",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "fab0c8ab-7d45-4e62-8350-217ad478c86b",
            "PO": "OLSO0770",
            "num": "03",
            "salesPerson": "3d092960-481a-49e6-a030-6525688d51ce",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-24T15:09:00.000Z",
            "oppDate": "2025-03-24T15:09:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-25T20:00:00.000Z",
            "budgetScore": 0.6,
            "acceptedProjectId": "2616a447-12ac-411b-8e6e-f1ebc4b8645b",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "c1820c37-bd08-4433-97a2-9539096379bc",
            "salesCommission": {
                "total": 256,
                "sale": 128,
                "start": 0,
                "completed": 128,
                "modificationAmount": 0
            },
            "soldValue": 3200,
            "presentationDate": "2025-03-25T21:48:00.000Z",
            "saleDate": "2025-03-25T21:49:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "jobCompletedDate": "2025-04-28T22:23:00.000Z",
            "order": {
                "priceTotals": {
                    "grandTotal": 3482,
                    "cashTotal": 2979.28,
                    "financeTotal": 2979.28,
                    "jobCost": 1412.6,
                    "lBurden": 121.79,
                    "lSubtotal": 459.57,
                    "lTotal": 581.36,
                    "laborCost": 434.24,
                    "mMarkup": 39.58,
                    "mTotal": 985.74,
                    "matCost": 746.85,
                    "matTax": 44.81,
                    "overhead": 1211.43,
                    "profit": 355.27,
                    "commission": 320,
                    "travelFee": 25.33,
                    "upsell": 0,
                    "permit": 154.5,
                    "asbTest": 0,
                    "jobTotal": 3200,
                    "salesTax": 0,
                    "discount": 282,
                    "discountComm": 28.2,
                    "actRev": 2214.26,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 3200,
                    "actualLaborCost": 625.72,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 860.47,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 860.47
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 3200
            },
            "actual": {
                "total": 3200
            },
            "grossProfit": 1227.99
        },
        {
            "_id": "783b23c9-1a73-4f83-bf42-933244497195",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "3d386722-3a3e-4181-8290-8f675d267cf6",
            "PO": "THUL5276",
            "num": "02",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-24T16:23:00.000Z",
            "oppDate": "2025-03-24T16:24:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-26T17:00:00.000Z",
            "budgetScore": 3.3,
            "acceptedProjectId": "0fae93b4-532f-47df-9a31-68f43cd275e3",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "7f7f166e-b143-4f4d-ba56-136bd3b38d3b",
            "salesCommission": {
                "total": 153.6,
                "sale": 76.8,
                "start": 0,
                "completed": 76.8,
                "modificationAmount": 0
            },
            "soldValue": 1536,
            "presentationDate": "2025-03-26T18:00:00.000Z",
            "saleDate": "2025-03-26T18:15:00.000Z",
            "jobCompletedDate": "2025-04-02T22:19:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "order": {
                "priceTotals": {
                    "grandTotal": 1736,
                    "cashTotal": 1372.2,
                    "financeTotal": 1372.2,
                    "jobCost": 516.93,
                    "lBurden": 55.94,
                    "lSubtotal": 211.10000000000002,
                    "lTotal": 267.03,
                    "laborCost": 170.56,
                    "mMarkup": 11.89,
                    "mTotal": 404.39,
                    "matCost": 224.53,
                    "matTax": 13.469999999999999,
                    "overhead": 661.33,
                    "profit": 194.45999999999998,
                    "commission": 188.77,
                    "travelFee": 40.54,
                    "upsell": 0,
                    "permit": 154.5,
                    "asbTest": 0,
                    "jobTotal": 1536,
                    "salesTax": 0,
                    "discount": 200,
                    "discountComm": 20,
                    "actRev": 1131.61,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 1536,
                    "actualLaborCost": 427.56,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 271.45,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 271.45
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 1536
            },
            "actual": {
                "total": 1536
            },
            "grossProfit": 534.92
        },
        {
            "_id": "783b23c9-1a73-4f83-bf42-933244497195",
            "oppType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "clientId": "3d386722-3a3e-4181-8290-8f675d267cf6",
            "PO": "THUL5276",
            "num": "02",
            "salesPerson": "b5f4b1e6-d6ec-4cb2-b4b6-35b0b18ccb26",
            "stage": "d2792291-a581-472d-b2a4-22eee2809650",
            "newLeadDate": "2025-03-24T16:23:00.000Z",
            "oppDate": "2025-03-24T16:24:00.000Z",
            "warrantyType": false,
            "changeOrders": [],
            "needsAssessmentDate": "2025-03-26T17:00:00.000Z",
            "budgetScore": 3.3,
            "acceptedProjectId": "0fae93b4-532f-47df-9a31-68f43cd275e3",
            "acceptedType": "aa5ef392-b5b7-4d5c-9def-89c9829e81e0",
            "orderId": "7f7f166e-b143-4f4d-ba56-136bd3b38d3b",
            "salesCommission": {
                "total": 153.6,
                "sale": 76.8,
                "start": 0,
                "completed": 76.8,
                "modificationAmount": 0
            },
            "soldValue": 1536,
            "presentationDate": "2025-03-26T18:00:00.000Z",
            "saleDate": "2025-03-26T18:15:00.000Z",
            "jobCompletedDate": "2025-04-02T22:19:00.000Z",
            "workingCrew": {
                "name": "Devon's Crew",
                "id": "d2c35efe-434a-4f0c-9138-91451740b8bf"
            },
            "order": {
                "priceTotals": {
                    "grandTotal": 1736,
                    "cashTotal": 1372.2,
                    "financeTotal": 1372.2,
                    "jobCost": 516.93,
                    "lBurden": 55.94,
                    "lSubtotal": 211.10000000000002,
                    "lTotal": 267.03,
                    "laborCost": 170.56,
                    "mMarkup": 11.89,
                    "mTotal": 404.39,
                    "matCost": 224.53,
                    "matTax": 13.469999999999999,
                    "overhead": 661.33,
                    "profit": 194.45999999999998,
                    "commission": 188.77,
                    "travelFee": 40.54,
                    "upsell": 0,
                    "permit": 154.5,
                    "asbTest": 0,
                    "jobTotal": 1536,
                    "salesTax": 0,
                    "discount": 200,
                    "discountComm": 20,
                    "actRev": 1131.61,
                    "travelHrlyRate": 32
                },
                "actualTotals": {
                    "actualPrice": 1536,
                    "actualLaborCost": 427.56,
                    "actualDays": 0,
                    "subcontractorCost": 0,
                    "inventoryCost": 0,
                    "qbCOGS": 271.45,
                    "other": 0,
                    "receipts": 0,
                    "actualMatCost": 271.45
                }
            },
            "acceptedProjectDetails": {
                "name": "Roof Replacement"
            },
            "budget": {
                "total": 1536
            },
            "actual": {
                "total": 1536
            },
            "grossProfit": 534.92
        }
    ],
    "campaign": [
        {
            "_id": null,
            "campaignId": null,
            "campaignName": null,
            "oppIds": [
                "e49fc3f7-b6f9-4082-811e-4aeafb77ac38",
                "6e0a8fd2-4974-46de-9383-14539e0505e7",
                "b296f59a-9fb9-4a54-9a51-10b29cf7cf77",
                "f542e59c-8700-48ef-a6cb-be75f07395d6",
                "e697bd7d-c635-4970-8dd4-8e2574e427e9",
                "71bd7e6f-979b-457a-b744-72ea119467e7",
                "11c17d58-10aa-4bff-afa0-eb4587c6759d",
                "d7ad212c-ce63-47f9-8d91-b79c92dc6c75",
                "5d83daa9-9cb0-424c-9cfd-fd98102c3a91",
                "bc14de96-bb38-44e5-a27b-aa85641e5f97",
                "74e86478-3bc5-4bc7-8476-c696dbe7b2ab",
                "90d33d3f-4f68-4e62-8ef6-0decb06ffe59",
                "03945296-8839-415d-99a0-98b9294719c0",
                "cb18c776-08e0-45b6-8d4a-51034ef7cb74",
                "ebffe272-4127-49ed-abbc-d1a8f3ee1ae5",
                "e8fed5a3-2a87-41f2-b984-0d85366901a9",
                "705c4614-5675-4c18-b301-06a0ea888ffa",
                "4fe562ab-9718-45c3-b1c2-9293daf4227c",
                "13a56491-e39b-479c-8477-3152bf5d9bf9",
                "3b92fd16-51d3-459e-b7da-09e2b76b99bb",
                "0cadba65-19c6-4231-92f8-c2bb89636bb5",
                "52bbe022-1f6a-4db1-b8ee-d5ba3cf5aea0",
                "5ed18481-7aab-4925-accc-61fa0f500dc4",
                "9d73e785-46ad-4950-bcce-5c8534f12d00",
                "0b89d4ae-a59c-46f5-a11e-0abe9cc17ebd",
                "b5e6a7d3-41e0-4cf6-b3e2-5ad6c1842546",
                "6dcaa6d7-7474-41e4-8fa4-f7f9908c9c80",
                "dcb07889-562c-4413-93a4-b9abf7be9e89",
                "be30501a-9e23-464c-a626-6d9a40598a54",
                "0c924e81-888c-4ab6-90ec-49d6fac3dc98",
                "188a74ec-258a-4009-9c08-aa90929c8a85",
                "212078bd-747e-4e86-a5d5-7611c544377a",
                "82160a7e-d603-4df1-a0cf-bad0bb8107d4",
                "a0a60bf7-4938-4579-9b54-d5cdaa288ca4",
                "a28a4f21-4e3a-49f8-bd6c-1e5e514c76c3",
                "483f445b-5daa-440f-bede-21c6e85302d2",
                "6b1f5f80-e47d-4ad9-a708-8ee4c7a4f1c3",
                "7c39021f-24d8-45b2-92f3-f8ce265fad4f",
                "783b23c9-1a73-4f83-bf42-933244497195"
            ],
            "totalOpps": 39,
            "totalBudget": 598029.18,
            "totalGrossProfit": 204353.23,
            "checkpointCounts": {
                "New Lead": 39,
                "New Opportunity": 39,
                "Needs Assessment": 39,
                "Presentation": 39,
                "Sale": 39
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "2f1acf40-d55b-4b45-84e7-4a2c5441c564",
            "campaignId": "2f1acf40-d55b-4b45-84e7-4a2c5441c564",
            "campaignName": "Google: WR-Roofing",
            "oppIds": [
                "c7d350b8-24a1-4948-904f-aa410e6a4879"
            ],
            "totalOpps": 1,
            "totalBudget": 350,
            "totalGrossProfit": 284.06,
            "checkpointCounts": {
                "New Lead": 1,
                "New Opportunity": 1,
                "Needs Assessment": 1,
                "Presentation": 1,
                "Sale": 1
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "c0aff990-c10f-4574-bc64-cdde6f5e5920",
            "campaignId": "c0aff990-c10f-4574-bc64-cdde6f5e5920",
            "campaignName": "Bing: WR-Roofing",
            "oppIds": [
                "2a792a2c-36e7-4c23-96da-96a13a7360f7"
            ],
            "totalOpps": 1,
            "totalBudget": 22293,
            "totalGrossProfit": 8583.91,
            "checkpointCounts": {
                "New Lead": 1,
                "New Opportunity": 1,
                "Needs Assessment": 1,
                "Presentation": 1,
                "Sale": 1
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        }
    ],
    "channels": [
        {
            "_id": "7cb94035-e3f9-4fad-a778-9c400e160e08",
            "channelId": "7cb94035-e3f9-4fad-a778-9c400e160e08",
            "oppIds": [
                "b296f59a-9fb9-4a54-9a51-10b29cf7cf77",
                "e697bd7d-c635-4970-8dd4-8e2574e427e9",
                "71bd7e6f-979b-457a-b744-72ea119467e7",
                "cb18c776-08e0-45b6-8d4a-51034ef7cb74",
                "705c4614-5675-4c18-b301-06a0ea888ffa",
                "0cadba65-19c6-4231-92f8-c2bb89636bb5",
                "5ed18481-7aab-4925-accc-61fa0f500dc4",
                "c7d350b8-24a1-4948-904f-aa410e6a4879",
                "2a792a2c-36e7-4c23-96da-96a13a7360f7"
            ],
            "totalOpps": 9,
            "name": "Online ads",
            "createdBy": "a223f050-17fa-4cb5-b6ad-a82d04cf3639",
            "deleted": false,
            "createdAt": "2024-01-07T07:18:55.714Z",
            "description": "All paid sources of online leads",
            "order": 1,
            "totalBudget": 235206.04,
            "totalGrossProfit": 98887.78,
            "checkpointCounts": {
                "New Lead": 9,
                "New Opportunity": 9,
                "Needs Assessment": 9,
                "Presentation": 9,
                "Sale": 9
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "09c66068-9536-41e0-94f1-810da4a9feea",
            "channelId": "09c66068-9536-41e0-94f1-810da4a9feea",
            "oppIds": [
                "f542e59c-8700-48ef-a6cb-be75f07395d6",
                "d7ad212c-ce63-47f9-8d91-b79c92dc6c75",
                "bc14de96-bb38-44e5-a27b-aa85641e5f97",
                "483f445b-5daa-440f-bede-21c6e85302d2"
            ],
            "totalOpps": 4,
            "name": "Word of mouth",
            "createdBy": "a223f050-17fa-4cb5-b6ad-a82d04cf3639",
            "deleted": false,
            "createdAt": "2024-01-07T07:18:55.714Z",
            "description": "All word of mouth sources",
            "order": 3,
            "totalBudget": 130177.95,
            "totalGrossProfit": 27045.73,
            "checkpointCounts": {
                "New Lead": 4,
                "New Opportunity": 4,
                "Needs Assessment": 4,
                "Presentation": 4,
                "Sale": 4
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "9a7b79fa-0776-44a7-9087-8e14424dbccb",
            "channelId": "9a7b79fa-0776-44a7-9087-8e14424dbccb",
            "oppIds": [
                "be30501a-9e23-464c-a626-6d9a40598a54",
                "212078bd-747e-4e86-a5d5-7611c544377a"
            ],
            "totalOpps": 2,
            "name": "Print ads",
            "createdBy": "a223f050-17fa-4cb5-b6ad-a82d04cf3639",
            "deleted": false,
            "createdAt": "2024-01-07T07:18:55.714Z",
            "description": "Any print ads and campaigns",
            "order": 5,
            "totalBudget": 4161,
            "totalGrossProfit": 1585.47,
            "checkpointCounts": {
                "New Lead": 2,
                "New Opportunity": 2,
                "Needs Assessment": 2,
                "Presentation": 2,
                "Sale": 2
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "13c5c48a-814b-40a5-854f-bc9ddbb006cb",
            "channelId": "13c5c48a-814b-40a5-854f-bc9ddbb006cb",
            "oppIds": [
                "11c17d58-10aa-4bff-afa0-eb4587c6759d",
                "e8fed5a3-2a87-41f2-b984-0d85366901a9",
                "52bbe022-1f6a-4db1-b8ee-d5ba3cf5aea0",
                "9d73e785-46ad-4950-bcce-5c8534f12d00",
                "0b89d4ae-a59c-46f5-a11e-0abe9cc17ebd",
                "a0a60bf7-4938-4579-9b54-d5cdaa288ca4",
                "a28a4f21-4e3a-49f8-bd6c-1e5e514c76c3",
                "7c39021f-24d8-45b2-92f3-f8ce265fad4f"
            ],
            "totalOpps": 8,
            "name": "Repeat business",
            "createdBy": "a223f050-17fa-4cb5-b6ad-a82d04cf3639",
            "deleted": false,
            "createdAt": "2024-01-07T07:18:55.715Z",
            "description": "Any repeats",
            "order": 4,
            "totalBudget": 48564,
            "totalGrossProfit": 1851.53,
            "checkpointCounts": {
                "New Lead": 8,
                "New Opportunity": 8,
                "Needs Assessment": 8,
                "Presentation": 8,
                "Sale": 8
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "3377b3d1-b2df-4f5e-97c3-4edddf9d4094",
            "channelId": "3377b3d1-b2df-4f5e-97c3-4edddf9d4094",
            "oppIds": [
                "e49fc3f7-b6f9-4082-811e-4aeafb77ac38",
                "6e0a8fd2-4974-46de-9383-14539e0505e7",
                "5d83daa9-9cb0-424c-9cfd-fd98102c3a91",
                "74e86478-3bc5-4bc7-8476-c696dbe7b2ab",
                "90d33d3f-4f68-4e62-8ef6-0decb06ffe59",
                "03945296-8839-415d-99a0-98b9294719c0",
                "ebffe272-4127-49ed-abbc-d1a8f3ee1ae5",
                "4fe562ab-9718-45c3-b1c2-9293daf4227c",
                "13a56491-e39b-479c-8477-3152bf5d9bf9",
                "3b92fd16-51d3-459e-b7da-09e2b76b99bb",
                "dcb07889-562c-4413-93a4-b9abf7be9e89",
                "0c924e81-888c-4ab6-90ec-49d6fac3dc98",
                "188a74ec-258a-4009-9c08-aa90929c8a85",
                "6b1f5f80-e47d-4ad9-a708-8ee4c7a4f1c3"
            ],
            "totalOpps": 14,
            "name": "Online organic",
            "createdBy": "a223f050-17fa-4cb5-b6ad-a82d04cf3639",
            "deleted": false,
            "createdAt": "2024-01-07T07:18:55.715Z",
            "description": "All organic forms of online leads",
            "order": 2,
            "totalBudget": 166505.19,
            "totalGrossProfit": 61885.88,
            "checkpointCounts": {
                "New Lead": 14,
                "New Opportunity": 14,
                "Needs Assessment": 14,
                "Presentation": 14,
                "Sale": 14
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "46bd8269-8fee-4f86-b3d6-33868e0a85ea",
            "channelId": "46bd8269-8fee-4f86-b3d6-33868e0a85ea",
            "oppIds": [
                "b5e6a7d3-41e0-4cf6-b3e2-5ad6c1842546",
                "6dcaa6d7-7474-41e4-8fa4-f7f9908c9c80"
            ],
            "totalOpps": 2,
            "name": "Events",
            "description": "Homeshows, Fairs, etc",
            "createdBy": "a223f050-17fa-4cb5-b6ad-a82d04cf3639",
            "deleted": false,
            "createdAt": "2025-03-17T21:15:40.482Z",
            "order": 4,
            "totalBudget": 32643,
            "totalGrossProfit": 20845.16,
            "checkpointCounts": {
                "New Lead": 2,
                "New Opportunity": 2,
                "Needs Assessment": 2,
                "Presentation": 2,
                "Sale": 2
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        },
        {
            "_id": "dd9f71bd-6fba-4eed-b6ee-b747b7cc5b8d",
            "channelId": "dd9f71bd-6fba-4eed-b6ee-b747b7cc5b8d",
            "oppIds": [
                "82160a7e-d603-4df1-a0cf-bad0bb8107d4",
                "783b23c9-1a73-4f83-bf42-933244497195"
            ],
            "totalOpps": 2,
            "name": "Other",
            "description": "For all others",
            "createdBy": "a223f050-17fa-4cb5-b6ad-a82d04cf3639",
            "deleted": false,
            "createdAt": "2024-01-11T11:54:32.480Z",
            "order": 10,
            "totalBudget": 3415,
            "totalGrossProfit": 1119.65,
            "checkpointCounts": {
                "New Lead": 2,
                "New Opportunity": 2,
                "Needs Assessment": 2,
                "Presentation": 2,
                "Sale": 2
            },
            "conversion": {
                "New Lead": [
                    "New Lead > New Opportunity: 100x",
                    "New Lead > Needs Assessment: 100x",
                    "New Lead > Presentation: 100x",
                    "New Lead > Sale: 100x"
                ],
                "New Opportunity": [
                    "New Opportunity > Needs Assessment: 100x",
                    "New Opportunity > Presentation: 100x",
                    "New Opportunity > Sale: 100x"
                ],
                "Needs Assessment": [
                    "Needs Assessment > Presentation: 100x",
                    "Needs Assessment > Sale: 100x"
                ],
                "Presentation": [
                    "Presentation > Sale: 100x"
                ]
            }
        }
    ]
}
export interface Root {
  leadSource: LeadSource[]
  opps: Opp[]
  campaign: Campaign[]
  channels: Channel[]
}

export interface LeadSource {
  _id: string
  leadSourceName: string
  channelId: string
  oppIds: string[]
  totalOpps: number
  totalBudget: number
  totalGrossProfit: number
  checkpointCounts: CheckpointCounts
  conversion: Conversion
}

export interface CheckpointCounts {
  "New Lead": number
  "New Opportunity": number
  "Needs Assessment": number
  Presentation: number
  Sale: number
}

export interface Conversion {
  "New Lead": string[]
  "New Opportunity": string[]
  "Needs Assessment": string[]
  Presentation: string[]
}

export interface Opp {
  _id: string
  oppType: string
  clientId: string
  PO: string
  num: string
  salesPerson?: string
  stage: string
  newLeadDate: string
  oppDate: string
  warrantyType: boolean
  changeOrderValue?: number
  needsAssessmentDate: string
  budgetScore?: number
  acceptedProjectId: string
  acceptedType: string
  orderId: string
  salesCommission: SalesCommission
  soldValue: number
  presentationDate: string
  saleDate: string
  workingCrew?: WorkingCrew
  jobCompletedDate?: string
  order: Order
  acceptedProjectDetails: AcceptedProjectDetails
  budget: Budget
  actual: Actual
  grossProfit: number
  changeOrders?: ChangeOrder[]
}

export interface SalesCommission {
  total: number
  sale: number
  start: number
  completed: number
  modificationAmount: number
}

export interface WorkingCrew {
  name: string
  id: string
}

export interface Order {
  priceTotals: PriceTotals
  actualTotals?: ActualTotals
  modifiedBudget?: ModifiedBudget
}

export interface PriceTotals {
  grandTotal: number
  cashTotal: number
  financeTotal: number
  jobCost: number
  lBurden: number
  lSubtotal: number
  lTotal: number
  laborCost: number
  mMarkup: number
  mTotal: number
  matCost: number
  matTax: number
  overhead: number
  profit: number
  commission: number
  travelFee: number
  upsell: number
  jobTotal: number
  permit: number
  asbTest: number
  salesTax: number
  discount: number
  discountComm: number
  actRev: number
  travelHrlyRate: number
}

export interface ActualTotals {
  actualPrice: number
  actualLaborCost: number
  actualDays: number
  subcontractorCost: number
  inventoryCost?: number
  qbCOGS?: number
  other?: number
  receipts?: number
  actualMatCost?: number
}

export interface ModifiedBudget {
  subContractorTotal: number
  isSubcontractorOnly: boolean
  rawLaborBudget: number
}

export interface AcceptedProjectDetails {
  name: string
}

export interface Budget {
  total: number
}

export interface Actual {
  total: number
}

export interface ChangeOrder {
  name: string
  description: string
  materials: number
  rawMatCost: number
  labor: number
  rawLaborCost: number
  addCost: number
  jobCost: number
  tax: number
  total: number
  signedBySales: boolean
  date: string
  mMarkup: number
  workTask: string
  manHours: number
  overrideTotalCost: boolean
  manHourCost: number
  laborBurden: number
  num: number
  deleted: boolean
  markupEnabled: boolean
  workTaskValue: number
  isSubContractor: boolean
  modificationId?: string
  oldJobCost?: number
  oldMaterials?: number
}

export interface Campaign {
  _id?: string
  campaignId?: string
  campaignName?: string
  oppIds: string[]
  totalOpps: number
  totalBudget: number
  totalGrossProfit: number
  checkpointCounts: CheckpointCounts2
  conversion: Conversion2
}

export interface CheckpointCounts2 {
  "New Lead": number
  "New Opportunity": number
  "Needs Assessment": number
  Presentation: number
  Sale: number
}

export interface Conversion2 {
  "New Lead": string[]
  "New Opportunity": string[]
  "Needs Assessment": string[]
  Presentation: string[]
}

export interface Channel {
  _id: string
  channelId: string
  oppIds: string[]
  totalOpps: number
  name: string
  createdBy: string
  deleted: boolean
  createdAt: string
  description: string
  order: number
  totalBudget: number
  totalGrossProfit: number
  checkpointCounts: CheckpointCounts3
  conversion: Conversion3
}

export interface CheckpointCounts3 {
  "New Lead": number
  "New Opportunity": number
  "Needs Assessment": number
  Presentation: number
  Sale: number
}

export interface Conversion3 {
  "New Lead": string[]
  "New Opportunity": string[]
  "Needs Assessment": string[]
  Presentation: string[]
}
