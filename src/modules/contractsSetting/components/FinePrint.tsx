import React, { useEffect, useState, useRef, useMemo } from 'react'
import * as SharedStyled from '../../../styles/styled'
import * as Styled from './styles'
import { Field, Form, Formik } from 'formik'
import UnitSvg from '../../../assets/newIcons/unitModal.svg'
import { CrossIcon } from '../../../assets/icons/CrossIcon'
import CustomSelect from '../../../shared/customSelect/CustomSelect'
import { useAppSelector } from '../../../logic/redux/reduxHook'
import Button from '../../../shared/components/button/Button'
import JoditEditor from 'jodit-react'
import { InputWithValidation } from '../../../shared/inputWithValidation/InputWithValidation'
import { createFinePrintContent, updateFinePrintContent } from '../../../logic/apis/company'
import { ContentBlockTypeEnum } from '../../../shared/helpers/constants'
import { isSuccess, notify } from '../../../shared/helpers/util'
import { ContentData } from '../ContractsSetting'
import * as Yup from 'yup'
import './jodit-editor.css'
import { editorConfig } from './constant'
import { position } from 'polished'
interface I_NewFinePrintModal {
  onClose: () => void
  onComplete: () => void
  projectTypes: any
  isEdit: boolean
  company: any
  data: ContentData
  selectedProjectType: string
}
function moveFontSizeFromSpanToLi(html: string): string {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')

  doc.querySelectorAll('ol > li, ul > li').forEach((li) => {
    const span = li.querySelector('span')

    if (span) {
      let fontSize: string | null = null
      let fontWeight: string | null = null

      // Check span's style
      const spanStyle = span.getAttribute('style') || ''
      const fontSizeMatch = spanStyle.match(/font-size:\s*([^;]+);?/)
      const fontWeightMatch = spanStyle.match(/font-weight:\s*([^;]+);?/)

      if (fontSizeMatch) fontSize = fontSizeMatch[1]
      if (fontWeightMatch) fontWeight = fontWeightMatch[1]

      // Check parent strong if needed
      const parent = span.parentElement
      if (parent && parent.tagName.toLowerCase() === 'strong') {
        const strongStyle = parent.getAttribute('style') || ''
        const strongFontSizeMatch = strongStyle.match(/font-size:\s*([^;]+);?/)
        const strongFontWeightMatch = strongStyle.match(/font-weight:\s*([^;]+);?/)

        if (strongFontSizeMatch) fontSize = strongFontSizeMatch[1]
        if (strongFontWeightMatch) fontWeight = strongFontWeightMatch[1]
      }

      // Apply to li
      if (fontSize) {
        li.style.fontSize = fontSize
      }
      if (fontWeight) {
        li.style.fontWeight = fontWeight
      }
    }
  })

  return doc.body.innerHTML
}

const FinePrint: React.FC<I_NewFinePrintModal> = (props) => {
  const { onClose, onComplete, projectTypes, company, isEdit, data, selectedProjectType } = props
  const [loading, setLoading] = useState(false)
  // const [selectedStates, setSelectedStates] = useState<string[]>([])
  // const [selectedProjectTypes, setSelectedProjectTypes] = useState<string[]>([])
  const [useSimpleEditor, setUseSimpleEditor] = useState(false) // State to toggle between editors

  const initialValues = {
    name: isEdit ? data.name || '' : '',
    type: ContentBlockTypeEnum.contract,
    // projectType: [],
    // addProjectType: '',
    // state: [],
    // addState: '',
    content: isEdit ? data.content || '' : '',
    // isDefault: isEdit ? data.isDefault : false,
    _id: isEdit ? data?._id || '' : undefined,
  }

  const editor = useRef(null)

  // Add resize observer in your component

  // Effect to handle editor initialization and cleanup
  useEffect(() => {
    // Add a class to the body when the editor is open
    document.body.classList.add('jodit-modal-open')

    // Cleanup function
    return () => {
      document.body.classList.remove('jodit-modal-open')

      // Close any open Jodit popups when component unmounts
      const popups = document.querySelectorAll('.jodit-popup, .jodit-dialog')
      popups.forEach((popup) => {
        if (popup.parentNode) {
          popup.parentNode.removeChild(popup)
        }
      })
    }
  }, [])

  const ContentSchema = Yup.object().shape({
    name: Yup.string().min(2, 'Too Short!').max(100, 'Too Long!').required('Required'),
    // type: Yup.string().required('Required'),
    // projectType: Yup.string().required('Required'),
    // state: Yup.string().required('Required'),
  })

  // useEffect(() => {
  //   if (isEdit && Object.entries(data).length) {
  //     // Ensure data.state is an array
  //     if (Array.isArray(data.state)) {
  //       setSelectedStates(data.state)
  //     } else if (typeof data.state === 'string') {
  //       setSelectedStates([data.state])
  //     } else {
  //       setSelectedStates([])
  //     }

  //     // Ensure data.projectType is an array
  //     if (Array.isArray(data.projectType)) {
  //       setSelectedProjectTypes(
  //         data.projectType
  //           .map((id: string) => {
  //             const project = projectTypes?.find((v: any) => v?.id === id)
  //             return project?.name || ''
  //           })
  //           .filter(Boolean) || []
  //       )
  //     } else {
  //       setSelectedProjectTypes([])
  //     }
  //   }
  // }, [isEdit, data, projectTypes])

  const { companySettingForAll } = company

  const handleSubmit = async (values: typeof initialValues) => {
    setLoading(true)
    try {
      const { ...restValues } = values

      // const projectTypeId =
      //   selectedProjectTypes
      //     ?.map(
      //       (name: string) =>
      //         projectTypes?.find((v: any) => v?.name?.trim().toLowerCase() === name?.trim().toLowerCase())?.id
      //     )
      //     .filter(Boolean) || []

      // Validate required fields
      if (values.name.trim() === '') {
        notify('Fine Print Name is required', 'error')
        setLoading(false)
        return
      }

      // Check if content is empty
      if (!values.content || values.content.trim() === '' || values.content === '<p><br></p>') {
        notify('Content cannot be empty', 'error')
        setLoading(false)
        return
      }

      // Create the data object with all required fields
      // Convert arrays to strings for API compatibility
      const submitData = {
        ...restValues,
        // Ensure we have at least one state, or use a default if empty
        // state: selectedStates.length > 0 ? selectedStates : [],
        // Ensure we have at least one project type, or use a default if empty
        // projectType: projectTypeId.length > 0 ? projectTypeId : [],
        isActive: true, // Add the missing required field
        type: Number(ContentBlockTypeEnum.contract), // Ensure type is a number
      }

      // Type assertion to match API requirements
      const res = !isEdit
        ? await createFinePrintContent(submitData as any)
        : await updateFinePrintContent(submitData as any)

      if (isSuccess(res)) {
        notify('Fine Print saved successfully', 'success')
        onComplete()
      } else {
        console.error('Failed to submit data', res)
        notify(res?.data?.message || 'Failed to save Fine Print', 'error')
      }
    } catch (error) {
      console.error('Submit error:', error)
      notify('An error occurred while saving the Fine Print', 'error')
    } finally {
      setLoading(false)
    }
  }
  const removeField = (cd: any, idx: number, fields: Array<any>) => {
    let newField = [...fields.slice(0, idx), ...fields.slice(idx + 1)]
    cd([...newField])
  }

  return (
    <div>
      <Styled.StepModalContainer width="902px">
        <Formik
          initialValues={initialValues}
          enableReinitialize={true}
          onSubmit={handleSubmit}
          validationSchema={ContentSchema}
          validateOnChange={true}
          validateOnBlur={false}
        >
          {({ values, errors, touched, resetForm, setFieldValue }) => {
            // Remove debug log in production
            return (
              <>
                <Styled.ModalHeaderContainer>
                  <SharedStyled.FlexRow>
                    <img src={UnitSvg} alt="modal icon" />
                    <SharedStyled.FlexCol>
                      <Styled.ModalHeader>Fine Print</Styled.ModalHeader>
                    </SharedStyled.FlexCol>
                  </SharedStyled.FlexRow>
                  <Styled.CrossContainer
                    onClick={() => {
                      resetForm()
                      onClose()
                    }}
                  >
                    <CrossIcon />
                  </Styled.CrossContainer>
                </Styled.ModalHeaderContainer>

                <div style={{ padding: '24px' }}>
                  <Form className="form">
                    <InputWithValidation
                      labelName="Fine Print Name"
                      stateName="name"
                      value={values.name}
                      error={touched.name && errors.name ? true : false}
                    />
                    {/* <div>
                      <SharedStyled.FlexCol gap="6px">
                        <SharedStyled.FlexBox width="100%" gap="10px" alignItems="center">
                          <CustomSelect
                            dropDownData={projectTypes?.map((v: any) => v?.name) || []}
                            setValue={() => {}}
                            stateName="addProjectType"
                            value={values.addProjectType}
                            // error={touched.addProjectType && errors.addProjectType ? true : false}
                            setFieldValue={setFieldValue}
                            labelName="Project Type"
                            innerHeight="52px"
                            margin="10px 0 0 0"
                          />
                          <SharedStyled.Button
                            maxWidth="50px"
                            marginTop="7px"
                            mediaHeight="52px"
                            type="button"
                            disabled={selectedProjectTypes.includes(values.addProjectType)}
                            onClick={() => {
                              let val = values.addProjectType
                              // Only add if value is not empty and not already in the list
                              if (val && !selectedProjectTypes.includes(val)) {
                                setSelectedProjectTypes((prev) => [...prev, val])
                                // Clear the input after adding
                                setFieldValue('addProjectType', '')
                              }
                            }}
                          >
                            <SharedStyled.IconCode>&#x2B;</SharedStyled.IconCode>
                          </SharedStyled.Button>
                        </SharedStyled.FlexBox>
                      </SharedStyled.FlexCol>

                      {selectedProjectTypes.length > 0 ? (
                        <SharedStyled.FlexCol>
                          <SharedStyled.Text
                            margin="5px 0 0 0"
                            fontWeight="500"
                            fontSize="14px"
                            width="100%"
                            textAlign="flex-start"
                          >
                            {'Selected Project Types: '}
                          </SharedStyled.Text>
                          <Styled.OptionsWrapper>
                            {selectedProjectTypes.map((field, idx) => (
                              <div
                                key={idx}
                                className="option"
                                onClick={() =>
                                  removeField(
                                    (values: string[]) => setSelectedProjectTypes(values),
                                    idx,
                                    selectedProjectTypes
                                  )
                                }
                              >
                                {field} <CrossIcon />
                              </div>
                            ))}
                          </Styled.OptionsWrapper>
                        </SharedStyled.FlexCol>
                      ) : (
                        <></>
                      )}
                    </div> */}

                    {/* <div>
                      <SharedStyled.FlexCol gap="6px">
                        <SharedStyled.FlexBox width="100%" gap="10px" alignItems="center">
                          <CustomSelect
                            dropDownData={companySettingForAll?.workingStates || []}
                            setValue={() => {}}
                            stateName="addState"
                            value={values.addState}
                            error={touched.addState && errors.addState ? true : false}
                            setFieldValue={setFieldValue}
                            labelName="Select State"
                            innerHeight="52px"
                            margin="10px 0 0 0"
                          />
                          <SharedStyled.Button
                            maxWidth="50px"
                            marginTop="7px"
                            mediaHeight="52px"
                            type="button"
                            disabled={selectedStates.includes(values.addState)}
                            onClick={() => {
                              let val = values.addState
                              // Only add if value is not empty and not already in the list
                              if (val && !selectedStates.includes(val)) {
                                setSelectedStates((prev) => [...prev, val])
                                // Clear the input after adding
                                setFieldValue('addState', '')
                              }
                            }}
                          >
                            <SharedStyled.IconCode>&#x2B;</SharedStyled.IconCode>
                          </SharedStyled.Button>
                        </SharedStyled.FlexBox>
                      </SharedStyled.FlexCol>

                      {selectedStates.length > 0 ? (
                        <SharedStyled.FlexCol>
                          <SharedStyled.Text
                            margin="5px 0 0 0"
                            fontWeight="500"
                            fontSize="14px"
                            width="100%"
                            textAlign="flex-start"
                          >
                            {'Selected states: '}
                          </SharedStyled.Text>
                          <Styled.OptionsWrapper>
                            {selectedStates.map((field, idx) => (
                              <div
                                key={idx}
                                className="option"
                                onClick={() =>
                                  removeField((values: string[]) => setSelectedStates(values), idx, selectedStates)
                                }
                              >
                                {field} <CrossIcon />
                              </div>
                            ))}
                          </Styled.OptionsWrapper>
                        </SharedStyled.FlexCol>
                      ) : (
                        <></>
                      )}
                    </div> */}

                    <SharedStyled.Text fontSize="14px" color="blue">
                      Note: Add dynamic variables using curly braces {`{  }`}
                      <br />
                      Example:
                      <br /> ✅ Use {`{manHourRate}`} for hourly rate
                      <br /> ✅ Use {`{plywoodRate}`} for plywood cost
                      <br /> ✅ Use {`{firstName}`} for First Name
                      <br /> ✅ Use {`{lastName}`} for Last Name
                      <br />
                      These placeholders will be replaced with actual values in the contract.
                    </SharedStyled.Text>
                    <br />

                    <div>
                      <JoditEditor
                        ref={editor}
                        value={values.content}
                        config={editorConfig}
                        onChange={(newContent) => {
                          const cleaned = moveFontSizeFromSpanToLi(newContent)
                          setFieldValue('content', cleaned)
                        }}
                      />
                    </div>
                    <br />

                    <div>
                      <Button isLoading={loading} type="submit">
                        Save
                      </Button>
                    </div>
                  </Form>
                </div>
              </>
            )
          }}
        </Formik>
      </Styled.StepModalContainer>
    </div>
  )
}

export default FinePrint
