import React, { ReactNode, useEffect } from 'react'
import styled from 'styled-components'

interface FinePrintModalProps {
  isOpen: boolean
  onClose: () => void
  children: ReactNode
}

// Styled components for the modal
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`

const ModalContent = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 100%;

  max-height: 90vh;
  max-width: 950px;

  display: flex;
  flex-direction: column;

  padding: 20px;
  overflow: hidden;
`

const ScrollableContent = styled.div`
  flex: 1 1 auto;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
`

const FinePrintModal: React.FC<FinePrintModalProps> = ({ isOpen, onClose, children }) => {
  // Handle ESC key press to close the modal
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey)
      // Prevent body scrolling when modal is open
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey)
      // Restore body scrolling when modal is closed
      document.body.style.overflow = 'auto'
    }
  }, [isOpen, onClose])

  // If modal is not open, don't render anything
  if (!isOpen) return null

  // Handle click on the overlay to close the modal
  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <ModalOverlay onClick={handleOverlayClick}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <ScrollableContent>{children}</ScrollableContent>
      </ModalContent>
    </ModalOverlay>
  )
}

export default FinePrintModal
