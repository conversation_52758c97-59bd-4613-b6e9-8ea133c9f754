import React, { useEffect, useState } from 'react'
import * as Styled from './style'
import * as SharedStyled from '../../styles/styled'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { NormalDropdown } from '../../shared/normalDropdown/NormalDropdown'
import Button from '../../shared/components/button/Button'
import { getProjectTypes } from '../../logic/apis/projects'
import { isSuccess, notify } from '../../shared/helpers/util'
import { Formik } from 'formik'
import { CustomModal } from '../../shared/customModal/CustomModal'
import AddSectionModal from './components/AddSectionModal'
import AddContract from './components/AddContract'
import {
  deleteFinePrintContent,
  deleteSection,
  getAllContent,
  getAllContracts,
  getContractById,
  getSections,
  updateSectionOrder,
} from '../../logic/apis/company'
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd'
import { EditIcon } from '../../assets/icons/EditIcon'
import { DeleteIcon } from '../../assets/icons/DeleteIcon'
import PreviewContract from './components/PreviewContract'
import FinePrint from './components/FinePrint'
import FinePrintModal from './components/FinePrintModal'
import { useAppSelector } from '../../logic/redux/reduxHook'
import { ContentBlockTypeEnum } from '../../shared/helpers/constants'
import TabBar from '../../shared/components/tabBar/TabBar'
import { SLoader } from '../../shared/components/loader/Loader'

interface Group {
  _id: string
  name: string
}

// Interface for the 'type' object
interface Type {
  groups: Group[]
}

// Main interface representing the entire data structure
export interface RoofType {
  id: string
  label: string
  name: string
  typeReplacement: boolean
  usesPitch: boolean
  value: string
  type: Type
}
export interface SelectedContract {
  contractName: string
  createdAt: string // ISO timestamp format
  deleted: boolean
  isDefault: boolean
  projectType: string
  sections: any[] // Assuming sections is an array, but currently empty. Can be further defined if structure is known.
  updatedAt: string // ISO timestamp format
  _id: string
  contentBlockId?: string
}
export interface ContentData {
  name: string
  type: string
  state: string
  content: string
  isDefault: boolean
  projectType: string
  _id: string
}
const ContractsSetting = () => {
  const [type, setType] = useState<RoofType | null>(null)
  const [selectedContract, setSelectedContract] = useState<SelectedContract | null>(null)
  const [projectTypes, setProjectTypes] = useState([])
  const [contracts, setContracts] = useState([])
  const [contents, setContents] = useState([])
  const [contentData, setContentData] = useState<ContentData>()

  const [selectedProjectType, setSelectedProjectType] = useState('')
  const [iscontract, setIscontract] = useState(true)
  const [contractById, setContractsById] = useState<SelectedContract | null>(null)
  const [sections, setSections] = useState([])
  const [section, setSection] = useState<any>()
  const [openModal, setOpenModal] = useState(false)
  const [finePrintLoading, setFinePrintLoading] = useState(true)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [isEdit, setIsEdit] = useState(false)
  const [openContractModal, setOpenContractModal] = useState(false)
  const [openContractPreview, setOpenContractPreviewModal] = useState(false)
  const [openFinePrintPreview, setFinePrintPreviewModal] = useState(false)
  const [tabIndex, setTabIndex] = useState(0)
  const { company } = useAppSelector((state) => state)

  console.log({ company })

  useEffect(() => {
    initFetch()
    fetchContractForProjectType()
    fetchAllContent()
  }, [])

  // useEffect(() => {
  //   if (type?.id) {
  //     fetchAllContent()
  //   }
  // }, [type])

  const fetchAllContent = async () => {
    try {
      // projectType: type?.id,
      const res = await getAllContent({ type: ContentBlockTypeEnum.contract }, false)
      if (isSuccess(res)) {
        const { contents } = res?.data?.data
        setContents(contents)
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setFinePrintLoading(false)
    }
  }
  // useEffect(() => {
  //   if (selectedContract?._id) {
  //     fetchContractById()
  //   }
  // }, [selectedContract])

  const fetchContractForProjectType = async () => {
    try {
      const res = await getAllContracts()
      if (isSuccess(res)) {
        const { contracts } = res?.data?.data
        console.log(
          { res },
          contracts?.map((v) => v?.contractName)
        )
        setContracts(contracts)
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setIscontract(false)
    }
  }

  // const fetchContractById = async () => {
  //   try {
  //     const res = await getContractById(selectedContract?._id!)
  //     if (isSuccess(res)) {
  //       const { contract } = res?.data?.data
  //       setContractsById(contract)
  //       setSections(contract?.sections || [])
  //     }
  //   } catch (error) {}
  // }

  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(
          ({
            _id,
            name,
            typeReplacement,
            groups,
            usesPitch,
          }: {
            _id: string
            name: string
            typeReplacement: boolean
            groups: string[]
            usesPitch: boolean
          }) => ({
            name: name,
            id: _id,
            value: _id,
            label: name,
            groups,
            usesPitch,
            typeReplacement,
          })
        )
        setProjectTypes(object)
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }
  const reorder = (list: any[], startIndex: number, endIndex: number) => {
    const result = Array.from(list)
    const [removed] = result.splice(startIndex, 1)
    result.splice(endIndex, 0, removed)

    // Update the order field based on the new index
    return result.map((item, index) => ({
      ...item,
      order: index + 1, // Set the new order
    }))
  }

  const updateSectionSequence = async (reorderedSections: any) => {
    try {
      const data = { sections: reorderedSections?.map((v) => ({ order: v.order, sectionId: v._id })) }

      const res = await updateSectionOrder(data, selectedContract?._id!)
      if (isSuccess(res)) {
        notify('Sequence Updated', 'success')
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const handleDeleteSection = async (sectionId: string) => {
    try {
      const res = await deleteSection(selectedContract?._id, sectionId)
      if (isSuccess(res)) {
        fetchSections()
        notify('Section Deleted Successfully', 'success')
      }
    } catch (error) {
      console.log({ error })
    }
  }
  {
    console.log({ deleteLoading })
  }

  useEffect(() => {
    if (Object.entries(selectedContract || {})?.length) {
      fetchSections()
    }
  }, [selectedContract])

  const fetchSections = async () => {
    try {
      const res = await getSections(selectedContract?._id)
      if (isSuccess(res)) {
        setSections(res?.data?.data?.sections || [])
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const handleDeleteFinePrint = async (id: string) => {
    try {
      const res = await deleteFinePrintContent(id)
      if (isSuccess(res)) {
        fetchAllContent()
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setDeleteLoading((pre) => !pre)
    }
  }

  return (
    <>
      <Formik initialValues={{ typeName: '', contract: '' }} onSubmit={() => {}}>
        {({ values, setFieldValue, resetForm }) => {
          useEffect(() => {
            if (values.typeName) {
              const filterType = projectTypes?.find((v) => v.name === values.typeName)
              setType(filterType)
            }
          }, [values.typeName])

          useEffect(() => {
            if (values.contract) {
              const filterContract = contracts
                ?.filter((v: any) => v.projectType === type?.id)
                ?.find(
                  (v) =>
                    v.contractName?.trim() ===
                    (values.contract.endsWith(' - Default')
                      ? values.contract.replace(' - Default', '')?.trim()
                      : values.contract?.trim())
                )
              setSelectedContract(filterContract)
            }
          }, [values.contract])

          useEffect(() => {
            if (type?.id) {
              // fetchContractForProjectType()
              setSelectedContract(null)
              setSections([])
              setFieldValue(
                'contract',
                `${
                  contracts?.filter((v: any) => v.projectType === type?.id)?.find((v: any) => v.isDefault === true)
                    ?.contractName
                    ? `${
                        contracts
                          ?.filter((v: any) => v.projectType === type?.id)
                          ?.find((v: any) => v.isDefault === true)?.contractName
                      } - Default`
                    : ''
                }`
              )
            }
          }, [type])

          useEffect(() => {
            setSections([])
            setFieldValue('contract', '')
          }, [contracts])

          const onDragEnd = (result) => {
            // Dropped outside the list
            if (!result.destination) {
              return
            }

            const reorderedSections = reorder(sections, result.source.index, result.destination.index)
            setSections(reorderedSections) // Update the sections state with new order
            updateSectionSequence(reorderedSections)
          }
          return (
            <Styled.ContractBox>
              <SharedStyled.SectionTitle>Contracts Settings</SharedStyled.SectionTitle>
              <br />
              <TabBar
                onTabChange={setTabIndex}
                tabs={[
                  {
                    title: 'Work Description',
                    render: () => (
                      <>
                        <SharedStyled.FlexCol gap="14px">
                          <CustomSelect
                            labelName="Project Type"
                            value={values.typeName}
                            dropDownData={projectTypes?.map((v) => v?.name)}
                            setValue={setSelectedProjectType}
                            setFieldValue={setFieldValue}
                            innerHeight="45px"
                            margin="10px 0 0 0"
                            stateName="typeName"
                            maxWidth="300px"
                          />

                          <SharedStyled.FlexBox gap="20px" width="100%">
                            <CustomSelect
                              labelName="Contract Name"
                              value={values.contract}
                              disabled={!values.typeName}
                              dropDownData={contracts
                                ?.filter((v) => v?.projectType === type?.id)
                                ?.map((v) =>
                                  v?.isDefault ? v?.contractName?.trim() + ' - Default' : v?.contractName?.trim()
                                )}
                              setValue={() => {}}
                              setFieldValue={setFieldValue}
                              innerHeight="45px"
                              margin="10px 0 0 0"
                              stateName="contract"
                              maxWidth="300px"
                            />
                            <SharedStyled.FlexBox gap="20px">
                              <Button
                                disabled={!values.contract}
                                onClick={() => {
                                  setIsEdit(true)
                                  setOpenContractModal(true)
                                }}
                              >
                                Edit
                              </Button>
                              <Button
                                disabled={!projectTypes?.length || iscontract}
                                onClick={() => {
                                  setIsEdit(false)
                                  setOpenContractModal(true)
                                }}
                              >
                                Add New
                              </Button>
                            </SharedStyled.FlexBox>
                          </SharedStyled.FlexBox>
                          {selectedContract ? (
                            selectedContract?.isDefault ? (
                              <h4>This is the Default Contract</h4>
                            ) : (
                              <h4>This is NOT the Default Contract</h4>
                            )
                          ) : null}

                          <p>
                            <span onClick={() => setOpenContractPreviewModal(true)} className="link">
                              Preview
                            </span>
                          </p>

                          <SharedStyled.FlexBox width="100%" justifyContent="space-between">
                            <div>
                              <SharedStyled.FlexBox gap="40px" alignItems="center">
                                <SharedStyled.Text fontWeight="700" fontSize="24px">
                                  Sections
                                </SharedStyled.Text>
                                <Button
                                  disabled={!values.contract}
                                  onClick={() => {
                                    setIsEdit(false)
                                    setOpenModal(true)
                                  }}
                                  width="fit-content"
                                >
                                  Add
                                </Button>
                              </SharedStyled.FlexBox>
                              <br />
                              <SharedStyled.FlexCol gap="20px">
                                <DragDropContext onDragEnd={onDragEnd}>
                                  <Droppable droppableId="sectionsList">
                                    {(provided) => (
                                      <div {...provided.droppableProps} ref={provided.innerRef}>
                                        {sections
                                          ?.sort((a, b) => a?.order - b?.order)
                                          ?.map((section: any, index: number) => (
                                            <Draggable key={section._id} draggableId={section._id} index={index}>
                                              {(provided) => (
                                                <div
                                                  ref={provided.innerRef}
                                                  {...provided.draggableProps}
                                                  {...provided.dragHandleProps}
                                                  style={{
                                                    ...provided.draggableProps.style,
                                                    padding: '10px',
                                                    marginBottom: '10px',
                                                    border: '1px solid #ccc',
                                                    borderRadius: '4px',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                  }}
                                                >
                                                  <div style={{ width: '350px' }}>
                                                    <SharedStyled.Text key={index} fontWeight="600" fontSize="18px">
                                                      {index + 1}. {section?.title}
                                                      &emsp;{' '}
                                                    </SharedStyled.Text>
                                                  </div>

                                                  <SharedStyled.FlexBox gap="10px" margin={'0 0 0 auto'}>
                                                    <Styled.IconContainer
                                                      className="edit"
                                                      onClick={(e) => {
                                                        e.stopPropagation()
                                                        setIsEdit(true)
                                                        setOpenModal(true)
                                                        setSection(section)
                                                      }}
                                                    >
                                                      <EditIcon />
                                                    </Styled.IconContainer>

                                                    <Styled.IconContainer
                                                      className="delete"
                                                      onClick={(e) => {
                                                        e.stopPropagation()
                                                        handleDeleteSection(section._id)
                                                      }}
                                                    >
                                                      <DeleteIcon />
                                                    </Styled.IconContainer>
                                                  </SharedStyled.FlexBox>
                                                </div>
                                              )}
                                            </Draggable>
                                          ))}
                                        {provided.placeholder}
                                      </div>
                                    )}
                                  </Droppable>
                                </DragDropContext>
                              </SharedStyled.FlexCol>
                            </div>
                          </SharedStyled.FlexBox>
                        </SharedStyled.FlexCol>
                      </>
                    ),
                  },
                  {
                    title: 'Fine Print',
                    render: () => (
                      <>
                        {finePrintLoading ? (
                          <>
                            <SharedStyled.FlexCol width="50%" gap="25px" margin="20px 0 0 0">
                              <SLoader height={45} width={100} isPercent />
                              <br />
                              <SLoader height={15} width={100} isPercent />
                              <SLoader height={15} width={100} isPercent />
                              <SLoader height={15} width={100} isPercent />
                            </SharedStyled.FlexCol>
                          </>
                        ) : (
                          <>
                            <div>
                              <SharedStyled.FlexBox gap="40px" marginTop="10px" alignItems="center">
                                {/* <SharedStyled.Text fontWeight="700" fontSize="24px">
                                  Fine Print
                                </SharedStyled.Text> */}
                                <Button
                                  // disabled={!values.contract}
                                  onClick={() => {
                                    setIsEdit(false)
                                    setFinePrintPreviewModal(true)
                                  }}
                                  width="fit-content"
                                >
                                  Add
                                </Button>
                              </SharedStyled.FlexBox>
                              <br />

                              <SharedStyled.FlexCol>
                                {contents?.length
                                  ? contents?.map((v: any, index: number) => {
                                      return (
                                        <>
                                          <div
                                            style={{
                                              padding: '10px',
                                              marginBottom: '10px',
                                              border: '1px solid #ccc',
                                              borderRadius: '4px',
                                              display: 'flex',
                                              alignItems: 'center',
                                            }}
                                          >
                                            <div style={{ width: '350px' }}>
                                              <SharedStyled.Text key={index} fontWeight="600" fontSize="18px">
                                                {index + 1}. {v?.name}
                                                {/* {v?.isDefault ? ` - Default` : ''} */}
                                                &emsp;{' '}
                                              </SharedStyled.Text>
                                            </div>

                                            <SharedStyled.FlexBox gap="10px" margin={'0 0 0 auto'}>
                                              <Styled.IconContainer
                                                className="edit"
                                                onClick={(e) => {
                                                  e.stopPropagation()
                                                  setIsEdit(true)
                                                  setFinePrintPreviewModal(true)
                                                  setContentData(v)
                                                }}
                                              >
                                                <EditIcon />
                                              </Styled.IconContainer>
                                              <Styled.IconContainer
                                                className="delete"
                                                disabled={deleteLoading}
                                                onClick={(e) => {
                                                  e.stopPropagation()
                                                  setDeleteLoading(true)
                                                  deleteLoading ? null : handleDeleteFinePrint(v?._id)
                                                }}
                                              >
                                                <DeleteIcon />
                                              </Styled.IconContainer>
                                            </SharedStyled.FlexBox>
                                          </div>
                                        </>
                                      )
                                    })
                                  : null}
                              </SharedStyled.FlexCol>
                            </div>
                          </>
                        )}
                      </>
                    ),
                  },
                ]}
              />
            </Styled.ContractBox>
          )
        }}
      </Formik>
      <CustomModal show={openModal}>
        <AddSectionModal
          onClose={() => {
            setOpenModal(false)
            // setEditReferrerVals(null)
          }}
          onComplete={() => {
            setOpenModal(false)
            fetchSections()
          }}
          type={type}
          selectedContract={selectedContract}
          isEdit={isEdit}
          section={section}
          sections={sections}
        />
      </CustomModal>
      <CustomModal show={openContractModal}>
        <AddContract
          onClose={() => {
            setOpenContractModal(false)
            // setEditReferrerVals(null)
          }}
          onComplete={() => {
            setOpenContractModal(false)
            fetchContractForProjectType()
          }}
          selectedProjectType={selectedProjectType}
          projectTypes={projectTypes}
          selectedContract={selectedContract}
          isEdit={isEdit}
          company={company}
          contracts={contracts}
          sections={sections}
          contents={contents}
        />
      </CustomModal>
      <CustomModal show={openContractPreview}>
        <PreviewContract
          onClose={() => {
            setOpenContractPreviewModal(false)
          }}
        />
      </CustomModal>
      <FinePrintModal
        isOpen={openFinePrintPreview}
        onClose={() => {
          setFinePrintPreviewModal(false)
          setContentData({} as ContentData)
        }}
      >
        <FinePrint
          onClose={() => {
            setFinePrintPreviewModal(false)
            setContentData({} as ContentData)
          }}
          onComplete={() => {
            setFinePrintPreviewModal(false)
            fetchAllContent()
            setContentData({} as ContentData)
          }}
          isEdit={isEdit}
          company={company}
          projectTypes={projectTypes}
          data={contentData || ({} as ContentData)}
          selectedProjectType={selectedProjectType}
        />
      </FinePrintModal>
    </>
  )
}

export default ContractsSetting
