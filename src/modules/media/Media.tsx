import { Link, useLocation, useNavigate, useParams } from 'react-router-dom'
// @ts-ignore
import convertHeic from 'heic-convert/browser'
import {
  createMediaOpportunity,
  deleteMediaOpportunity,
  FilePayload,
  generateTokenUrl,
  getMediaSettings,
  getOpportunityMedia,
  getPresignedUrlMedia,
  updateMediaOpportunity,
} from '../../logic/apis/media'
import useFetch from '../../logic/apis/useFetch'
import { FlexCol, FlexRow, Text, TooltipContainer } from '../../styles/styled'
import { colors } from '../../styles/theme'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import * as Styled from './style'

import { dayjsFormat, generateUUID, getDataFromLocalStorage, isSuccess, notify } from '../../shared/helpers/util'
import MediaPreview from './components/MediaPreview'
import PdfSvg from '../../assets/newIcons/pdf.svg'
import AudioSvg from '../../assets/newIcons/audio.svg'
import FilterSvg from '../../assets/newIcons/filter.svg'
import VideoSvg from '../../assets/newIcons/video.svg'
import DownloadSvg from '../../assets/newIcons/download.svg'

import UploadIcon from '../../assets/newIcons/uploadIcon.svg'
import SelectIcon from '../../assets/newIcons/select.svg'

import SelectAllIcon from '../../assets/newIcons/selectAll.svg'
import GpsIcon from '../../assets/newIcons/gps.svg'
import TagIcon from '../../assets/newIcons/tag.svg'
import ShareIcon from '../../assets/newIcons/share.svg'
import { DropdownButton, DropdownContainer, DropdownContent } from '../../shared/dropdownWithCheckboxes/style'
import { FilterContainer } from '../reports/productionReport/style'
import CheckboxList from '../track/components/CheckboxList'
import {
  checkIfFilterIsApplied,
  getSelectedFilters,
  handleFilterChange,
} from '../reports/productionReport/ProductionReport'
import Tag from '../../shared/components/tag'
import { FilePathTypeEnum, FormAccess, mimeTypesMap } from '../../shared/helpers/constants'
import { useAppSelector } from '../../logic/redux/reduxHook'
import { extractImageData, getThumbnailUrl, validateFiles } from './mediaUtils'
import { FullpageLoader, SLoader } from '../../shared/components/loader/Loader'
import Delete from '../../assets/newIcons/delete.svg'
import { CustomModal } from '../../shared/customModal/CustomModal'
import Modal from '../../shared/customModal/Modal'
import Button from '../../shared/components/button/Button'
import ShareLink from '../../shared/shareLink/ShareLink'
import { getCompanyAllForms } from '../../logic/apis/form'
import { mediaGpsPath } from '../../logic/paths'
import CreateTagModal from '../mediaSettings/components/CreateTagModal'
import AutoComplete from '../../shared/autoComplete/AutoComplete'
import FormSelect from '../../shared/formSelect/FormSelect'
import EditSvg from '../../assets/newIcons/edit.svg'
import { useSelector } from 'react-redux'
import JSZip from 'jszip'

const getCommonTags = (data: any[]) => {
  if (!data.length) return []

  const tagLists = data.map(({ tags = [] }) => tags).filter((tags) => tags.length)

  return tagLists.length ? tagLists.reduce((common, tags) => common.filter((tag: any) => tags.includes(tag))) : []
}

const filteredMediaGpsData = (mediaData: any[]) =>
  mediaData.filter(
    (item) =>
      item.mimetype?.includes('image') && item.location?.type === 'Point' && item.location?.coordinates?.length === 2
  )

export const processFile = async (file: any) => {
  if (file.type === 'image/heic' || file.name.toLowerCase().endsWith('.heic')) {
    const buffer = await file.arrayBuffer()
    const convertedBuffer = await convertHeic({
      buffer: Buffer.from(buffer),
      format: 'JPEG',
      quality: 1,
    })

    return new File([convertedBuffer], file.name.replace(/\.heic$/i, '.jpg'), {
      type: 'image/jpeg',
    })
  }
  return file
}

export interface ISelectedMedia {
  _id: string
  tags: string[]
  createdBy: any
  mimetype: string
  url: string
  createdAt: string
  name?: string
}

export const MediaType: Record<string, string> = {
  image: 'Photos',
  video: 'Videos',
  application: 'Documents',
}
const TypeMedia: Record<string, string> = {
  Photos: 'image',
  Videos: 'video',
  Documents: 'application',
}

export const ImageWithFallback = ({
  thumbnail,
  media,
  alt = '',
  className,
  style,
}: {
  thumbnail: string
  media: string
  alt?: string
  className?: string
  style?: React.CSSProperties
}) => {
  const [src, setSrc] = useState(thumbnail)
  const [isValidVideoThumbnail, setIsValidVideoThumbnail] = useState(true)
  const [loaded, setLoaded] = useState(false)

  let isVideoThumbnail = className === 'video'
  const isPdf = className === 'pdf' && isValidVideoThumbnail

  return (
    <>
      {isVideoThumbnail && isValidVideoThumbnail ? (
        <Styled.VideoThumbnailCont>
          <Styled.StyledImage
            src={src}
            alt={alt}
            onError={() => {
              setSrc(media)
              setIsValidVideoThumbnail(false)
            }}
            onLoad={() => {
              setIsValidVideoThumbnail(true)
            }}
            className={isVideoThumbnail && isValidVideoThumbnail ? 'video-thumbnail' : className}
            style={style}
          />

          <img src={VideoSvg} alt="icon" className="play-overlay" />
        </Styled.VideoThumbnailCont>
      ) : (
        <>
          <Styled.StyledImage
            src={src}
            alt={alt}
            onLoad={() => {
              setLoaded(true)
            }}
            onError={() => {
              setSrc(media)
              setIsValidVideoThumbnail(false)
            }}
            className={isVideoThumbnail && isValidVideoThumbnail ? 'video-thumbnail' : isPdf ? 'isPdf' : className}
            style={style}
          />
          {isPdf && loaded && <img src={PdfSvg} alt="icon" className="pdf-overlay" />}
        </>
      )}
    </>
  )
}

export const renderMedia = (
  url: string,
  type: any,
  isPreview = false,
  fullScreen = false,
  memberId?: any,
  oppId?: string,
  media?: any,
  thumbnail?: string
) => {
  switch (type?.split('/')?.[0]) {
    case 'image':
      return (
        <Styled.MediaContainer className={isPreview ? 'preview' : ''}>
          <ImageWithFallback alt="media image" media={url} thumbnail={thumbnail || getThumbnailUrl(url)} />
        </Styled.MediaContainer>
      )
    case 'video':
      return (
        <>
          {fullScreen ? (
            <FlexRow justifyContent="center">
              <Styled.VideoCont>
                <Styled.StyledVideo controls>
                  <source src={url} type="video/mp4" />
                  Your browser does not support the video tag.
                </Styled.StyledVideo>
              </Styled.VideoCont>
            </FlexRow>
          ) : (
            <Styled.MediaContainer className={isPreview ? 'preview' : ''}>
              <ImageWithFallback
                alt="video"
                media={VideoSvg}
                thumbnail={thumbnail || getThumbnailUrl(url)}
                className="video"
              />
            </Styled.MediaContainer>
          )}
        </>
      )

    case 'audio':
      return (
        <>
          {fullScreen ? (
            <FlexRow justifyContent="center">
              <Styled.VideoCont>
                <Styled.StyledVideo controls>
                  <source src={url} type="audio/mpeg" />
                  Your browser does not support the audio element.
                </Styled.StyledVideo>
              </Styled.VideoCont>
            </FlexRow>
          ) : (
            <Styled.MediaContainer className={isPreview ? 'preview' : ''}>
              <ImageWithFallback
                alt="audio"
                media={AudioSvg}
                thumbnail={thumbnail || getThumbnailUrl(url)}
                className="audio"
              />
            </Styled.MediaContainer>
          )}
        </>
      )

    case 'application':
      return (
        <>
          {fullScreen ? (
            <Styled.IframeCont justifyContent="center">
              <iframe src={url} />
            </Styled.IframeCont>
          ) : (
            <Styled.MediaContainer className={isPreview ? 'preview' : 'pdf-cont'}>
              <>
                {media?.builderFormId ? (
                  <>
                    {media?.createdBy?._id === memberId && (
                      <button
                        className="edit-btn"
                        onClick={(e) => {
                          e.stopPropagation()
                          window.location.href = `/sales/opportunity/${oppId!}/form/${media?.builderFormId}/${
                            media?.formId
                          }/${media?._id}`
                        }}
                      >
                        <img src={EditSvg} alt="edit icon" />
                      </button>
                    )}
                  </>
                ) : null}
                <ImageWithFallback
                  alt="pdf"
                  media={PdfSvg}
                  thumbnail={thumbnail || getThumbnailUrl(url)}
                  className="pdf"
                />
              </>
            </Styled.MediaContainer>
          )}
        </>
      )

    case 'audio':
      return (
        <>
          {fullScreen ? (
            <audio controls>
              <source src={url} type={'audio/mp3'} />
            </audio>
          ) : (
            <Styled.MediaContainer className={isPreview ? 'preview' : 'pdf-cont'}>
              <>
                <ImageWithFallback
                  alt="pdf"
                  media={AudioSvg}
                  thumbnail={thumbnail || getThumbnailUrl(url)}
                  className="pdf"
                />
              </>
            </Styled.MediaContainer>
          )}
        </>
      )
    default:
      return <span>Unsupported media type</span>
  }
}

export interface IMedia {
  _id: string
  url: any
  name: any
  mimetype: string
  createdAt: string
  date?: string
  builderFormId?: string
}

const Media = () => {
  const { oppId } = useParams()
  const { company } = useAppSelector((state) => state)
  const memberId = company?.currentMember?._id
  const positionPermissions = company?.positionPermissions

  const { state } = useLocation()
  const [selectedMedia, setSelectedMedia] = useState<any>([])
  const [selectedTags, setSelectedTags] = useState<string[]>([])

  const [selectedMediaData, setSelectedMediaData] = useState<any>([])

  const [mediaData, setMediaData] = useState<any[]>([])
  const [showPreview, setShowPreview] = useState(false)
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [bool, setBool] = useState(false)
  const [showTagModal, setShowTagModal] = useState(false)
  const [updateLoading, setUpdateLoading] = useState(false)
  const [showFilter, setShowFilter] = useState(false)
  const [fetchBool, setFetchBool] = useState(false)
  const [typeSelected, setTypeSelected] = useState<any>({})
  const [tagSelected, setTagSelected] = useState<any>({})
  const [userSelected, setUserSelected] = useState<any>({})
  const mediaStats = useRef<any>({})
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [uploadLoading, setUploadLoading] = useState(false)
  const [selectionView, setSelectionView] = useState(false)
  const [tagBool, setTagBool] = useState(false)
  const [shareMediaLoader, setShareMediaLoader] = useState(true)
  const [urlHash, setUrlHash] = useState('')
  const navigate = useNavigate()
  const [gpsMediaData, setGpsMediaData] = useState<any[]>([])
  const [downloadLoading, setDownloadLoading] = useState(false)

  const { data: tagsData } = useFetch({
    fetchFn: getMediaSettings,
    refetchTrigger: tagBool,
  })

  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowFilter(false)
      }
    }

    if (showFilter) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showFilter])

  const { data, loading } = useFetch({
    fetchFn: () => (oppId ? getOpportunityMedia(oppId!) : () => {}),
    refetchTrigger: bool,
  })
  const generateUrl = async () => {
    try {
      const res = await generateTokenUrl(oppId!, {
        imageIds: selectedMedia?.length ? selectedMedia : undefined,
        tags: selectedMedia?.length ? undefined : tagTypeFilters,
        types: selectedMedia?.length ? undefined : mediaTypeFilters,
        createdBy: selectedMedia?.length
          ? undefined
          : userTypeFilters.map((name) => {
              const found = data?.images.find((item: any) => item.createdBy?.name === name)
              return found?.createdBy?._id || null
            }),
      })
      if (isSuccess(res)) {
        setUrlHash(res?.data?.data?.hash)
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setShareMediaLoader(false)
    }
  }

  useEffect(() => {
    if (showShareModal) {
      generateUrl()
    }
  }, [showShareModal])

  const allMedia = useMemo(() => {
    return mediaData?.[selectedDate as unknown as number]?.map((itm: ISelectedMedia) => ({
      url: itm?.url,
      mimetype: itm?.mimetype,
      user: itm?.createdBy?.name,
      userImage: itm?.createdBy?.userImageUrl?.imageUrl,
      createdAt: itm?.createdAt,
      id: itm?.createdBy?.userImageUrl?._id,
      tags: itm?.tags,
      imageId: itm?._id,
      name: itm?.name,
      originalData: itm,
    }))
  }, [selectedDate, bool, selectedIndex, fetchBool, mediaData])

  const mediaTypeFilters = getSelectedFilters(typeSelected)
  const tagTypeFilters = getSelectedFilters(tagSelected)
  const userTypeFilters = getSelectedFilters(userSelected)

  const filters = {
    mediaTypeFilters,
    tagTypeFilters,
    userTypeFilters,
  }

  const isFilterApplied = checkIfFilterIsApplied(filters)

  const hasMedia = !!data?.images?.length

  useEffect(() => {
    if (data?.images?.length) {
      setFetchBool((p) => !p)

      setGpsMediaData(filteredMediaGpsData(data?.images))
    }
  }, [data?.images])

  const filteredMedia = useMemo(() => {
    return data?.images?.filter((itm: { createdBy: any; mimetype: string; tags: string[] | any[] }) => {
      const mediaTypeMatch = mediaTypeFilters.length === 0 || mediaTypeFilters.includes(itm?.mimetype?.split('/')?.[0])
      const tagMatch = tagTypeFilters.length === 0 || tagTypeFilters.some((tag) => itm?.tags?.includes(tag))
      const userMatch = userTypeFilters.length === 0 || userTypeFilters.includes(itm?.createdBy?.name)

      return mediaTypeMatch && tagMatch && userMatch
    })
  }, [mediaTypeFilters, tagTypeFilters, userTypeFilters])

  const uploadRef = useRef(null)

  const uniqueValues = useMemo(() => {
    const user = new Set()

    const mediaTotal: Record<string, number> = {
      Photos: 0,
      Videos: 0,
      Documents: 0,
    }

    data?.images?.forEach((type: any) => {
      type?.createdBy?.name && user.add(type?.createdBy?.name)
      mediaTotal[MediaType[type?.mimetype?.split('/')?.[0]]] += 1
    })

    mediaStats.current = mediaTotal

    let media = []
    if (mediaStats?.current.Photos) {
      media.push('Photos')
    }
    if (mediaStats?.current.Videos) {
      media.push('Videos')
    }
    if (mediaStats?.current.Documents) {
      media.push('Documents')
    }

    return {
      mediaTypes: media?.map((itm) => ({ name: itm, _id: TypeMedia[itm] })),
      users: [...user]?.map((itm) => ({ name: itm, _id: itm })),
      tags: [...(tagsData?.mediaSetting?.tags || [])]?.map((itm) => ({ name: itm, _id: itm })),
    }
  }, [allMedia, tagsData?.mediaSetting?.tags, data?.images?.length, mediaStats.current])

  useEffect(() => {
    const groupedByDate = (filteredMedia || [])?.reduce((acc: any, item: any) => {
      const date = dayjsFormat(item?.createdAt, 'YYYY-MM-DD')

      if (!acc[date]) {
        acc[date] = []
      }

      acc[date].push({
        ...item,
        date: dayjsFormat(item?.createdAt, 'YYYY-MM-DD'),
      })

      return acc
    }, {})

    setMediaData(groupedByDate)
  }, [filteredMedia?.length, bool, fetchBool])

  const handleCloseClick = (filter: any, fn: Function, arr: any) => {
    fn({
      ...arr,
      [filter]: false,
    })
  }

  const handleMediaSelect = (media: any) => {
    let mediaId = media?._id
    setSelectedMedia((prev: any) => {
      const newSelection = new Set(prev)
      if (newSelection.has(mediaId)) {
        newSelection.delete(mediaId)
        setSelectedMediaData((p: any) => p?.filter((itm: any) => itm?._id !== mediaId))
      } else {
        newSelection.add(mediaId)
        setSelectedMediaData((p: any) => [...p, media?.url])
      }
      return [...newSelection]
    })
  }
  const handleSelectAll = () => {
    const allIds = filteredMedia?.map((media: any) => media?._id)

    if (selectedMedia?.length) {
      setSelectionView(false)
      setSelectedMedia([])
      setSelectedMediaData([])
    } else {
      setSelectionView(true)
      setSelectedMedia(allIds)
      setSelectedMediaData(filteredMedia?.map((media: any) => media?.url))
    }
  }

  const handleUpdateMedia = async () => {
    try {
      console.log(
        selectedMedia?.map((itm: any) => ({
          _id: itm,
          tags: selectedTags,
        }))
      )

      const payload = selectedMedia?.map((itm: any) => ({
        _id: itm,
        tags: selectedTags,
      }))
      setUpdateLoading(true)
      const res = await updateMediaOpportunity(oppId!, payload)
      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        setBool((p) => !p)
        setShowTagModal(false)
        setSelectedTags([])
        setSelectionView(false)
        setSelectedMedia([])
      }
    } catch (error) {
    } finally {
      setUpdateLoading(false)
    }
  }

  const handleFileChange = async (e: any) => {
    try {
      setUploadLoading(true)
      const selectedFiles = Array.from(e.target.files)

      const processedFiles = await Promise.all(selectedFiles.map(processFile))

      const newFiles = processedFiles.map((file: any) => ({
        url: URL.createObjectURL(file),
        mimetype: file.type,
        name: file.name,
        file,
      }))

      const validFiles = validateFiles(newFiles, {
        maxImageSizeMB: company?.companySettingForAll?.maxImageSizeMB,
        maxVideoSizeMB: company?.companySettingForAll?.maxVideoSizeMB,
      })

      if (validFiles?.length) {
        const res = await getPresignedUrlMedia(
          FilePathTypeEnum.Project,
          validFiles.map((itm: any) => ({
            fileName: itm.name,
            mimetype: itm.mimetype.includes('video') ? 'video/mp4' : itm.mimetype,
          })),
          memberId!,
          oppId!
        )
        if (!isSuccess(res)) {
          return
        }

        const urlData: Record<string, any> = res?.data?.data?.signedUrls?.reduce((acc: any, itm: any) => {
          acc[itm.fileName] = itm
          return acc
        }, {})

        const uploadPromises = validFiles.map(async (itm: any) => {
          const myHeaders = new Headers()
          myHeaders.append('Content-Type', itm.mimetype.includes('video') ? 'video/mp4' : itm.mimetype)

          const requestOptions = {
            method: 'PUT',
            headers: myHeaders,
            body: itm.file,
          }

          const response = await fetch(urlData[itm.name]?.url, requestOptions)
          if (!response.ok) {
            notify('Failed to upload', 'error')
            throw new Error(`Failed to upload ${itm.name}: ${response.statusText}`)
          }
          return response
        })

        await Promise.all(uploadPromises)

        const payload: FilePayload[] = []

        const onlyImages = validFiles.filter((itm: any) => itm.mimetype.includes('image'))

        const metaData = await extractImageData(onlyImages)

        validFiles?.forEach((itm: any) => {
          if (itm?.mimetype?.includes('image')) {
            payload.push({
              _id: generateUUID()!,
              createdAt: metaData?.[itm?.name]?.createdAt,
              createdBy: memberId,
              mimetype: itm?.mimetype,
              name: itm?.name,
              url: urlData?.[itm?.name]?.url?.split('?')[0],
              thumbnail: getThumbnailUrl(urlData?.[itm?.name]?.url?.split('?')[0]),
              location: metaData?.[itm?.name]?.location || undefined,
              tags: [],
            })
          } else {
            payload.push({
              _id: generateUUID()!,
              createdAt: new Date().toISOString(),
              createdBy: memberId,
              mimetype: itm.mimetype.includes('video') ? 'video/mp4' : itm.mimetype,
              name: itm?.name,
              url: urlData?.[itm?.name]?.url?.split('?')[0],
              thumbnail: getThumbnailUrl(urlData?.[itm?.name]?.url?.split('?')[0]),
              tags: [],
            })
          }
        })

        const uploadRes = await createMediaOpportunity(oppId!, payload)

        if (isSuccess(uploadRes)) {
          notify(uploadRes?.data?.data?.message, 'success')
          setUploadLoading(false)
          setBool((prev) => !prev)
        }
      }
    } catch (error) {
      console.log('error======>', error)
    } finally {
      setUploadLoading(false)
    }
  }

  const handleDeleteSelection = async () => {
    try {
      setDeleteLoading(true)
      const res = await deleteMediaOpportunity(selectedMedia?.join(','))

      if (isSuccess(res)) {
        notify(res?.data?.data?.message, 'success')
        setShowDeleteModal(false)
        setBool((p) => !p)
        setSelectionView(false)
        setSelectedMedia([])
      }
    } catch (error) {
    } finally {
      setDeleteLoading(false)
    }
  }

  const handleDownloadClick = async () => {
    setDownloadLoading(true)
    const poName = state?.poName || `${data?.PO}-${data?.num}`
    const zip = new JSZip()
    const folder = zip.folder(poName)

    await Promise.all(
      selectedMediaData?.map(async (url: string, index: number) => {
        try {
          const res = await fetch(url)
          const blob = await res.blob()
          const name = url.split('/').pop()?.split('-')?.[1] || `file-${index}`
          folder?.file(name, blob)
        } catch (err) {
          console.error('Error fetching:', url, err)
        }
      })
    )

    const zipBlob = await zip.generateAsync({ type: 'blob' })

    const url = URL.createObjectURL(zipBlob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${poName}-media-files.zip`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    setDownloadLoading(false)
  }

  const allowedMediaTypes = company?.companySettingForAll?.allowedMediaTypes ?? []

  const allowedMIMETypes = allowedMediaTypes.map((ext: string) => mimeTypesMap[ext] || '').filter(Boolean)

  return (
    <Styled.SectionCont>
      <FlexCol>
        <FlexRow margin="0 auto" justifyContent="center">
          <h1>{state?.poName || `${data?.PO || ''}${data?.num ? `-${data?.num}` : ''}`}</h1>
          <Text fontSize="16px" fontWeight="500" color={colors.subRow}>
            {state?.clientName || `${data?.firstName || ''} ${data?.lastName || ''}`}
          </Text>
        </FlexRow>

        {hasMedia ? (
          <FlexRow margin="10px 0 0 0" width="max-content" className="stats">
            <div>
              {mediaStats?.current?.Photos ? (
                <span
                  onClick={() =>
                    setTypeSelected({
                      ...typeSelected,
                      image: true,
                    })
                  }
                >
                  Photos ({mediaStats?.current?.Photos})
                </span>
              ) : null}
              {mediaStats?.current?.Videos ? (
                <span
                  onClick={() =>
                    setTypeSelected({
                      ...typeSelected,
                      video: true,
                    })
                  }
                >
                  &nbsp; Videos ({mediaStats?.current?.Videos})
                </span>
              ) : null}
              {mediaStats?.current?.Documents ? (
                <span
                  onClick={() =>
                    setTypeSelected({
                      ...typeSelected,
                      application: true,
                    })
                  }
                >
                  &nbsp; Documents ({mediaStats?.current?.Documents})
                </span>
              ) : null}
            </div>
          </FlexRow>
        ) : null}

        <FlexRow margin="10px 0 0 0" justifyContent="space-between" className="filter-cont">
          {uploadLoading ? (
            <div>
              <FullpageLoader />
            </div>
          ) : (
            <FlexRow gap="16px" width="max-content">
              <div style={{ width: '20px' }}>
                <TooltipContainer
                  width="70px"
                  positionLeft="0px"
                  positionBottom="0px"
                  positionLeftDecs="14px"
                  positionBottomDecs="unset"
                  positionTopDecs="-40px"
                  fontSize="14px"
                >
                  <span className="tooltip-content">Upload</span>
                  <input
                    type="file"
                    ref={uploadRef}
                    style={{ visibility: 'hidden', width: '0px' }}
                    onChange={handleFileChange}
                    accept={`${allowedMIMETypes.join(',')}`}
                    multiple
                  />
                  <img
                    src={UploadIcon}
                    alt="upload icon"
                    style={{ width: '20px', cursor: 'pointer' }}
                    onClick={() => {
                      // @ts-ignore
                      uploadRef.current.click()
                    }}
                  />
                </TooltipContainer>
              </div>

              {hasMedia ? (
                <>
                  {gpsMediaData?.length ? (
                    <TooltipContainer
                      width="120px"
                      positionLeft="0px"
                      positionBottom="0px"
                      positionLeftDecs="10px"
                      positionBottomDecs="unset"
                      positionTopDecs="-40px"
                      fontSize="14px"
                    >
                      <span className="tooltip-content">GPS Media</span>
                      <Link
                        to={mediaGpsPath}
                        state={{
                          imageData: gpsMediaData,
                        }}
                      >
                        <img src={GpsIcon} alt="gps icon" style={{ width: '20px', cursor: 'pointer' }} />
                      </Link>
                    </TooltipContainer>
                  ) : null}
                  <TooltipContainer
                    width="120px"
                    positionLeft="0px"
                    positionBottom="0px"
                    positionLeftDecs="10px"
                    positionBottomDecs="unset"
                    positionTopDecs="-40px"
                    fontSize="14px"
                  >
                    <span className="tooltip-content">Select all Media</span>
                    <img
                      src={SelectAllIcon}
                      alt="select icon"
                      style={{ width: '20px', cursor: 'pointer' }}
                      onClick={handleSelectAll}
                    />
                  </TooltipContainer>

                  {selectedMedia?.length !== filteredMedia?.length ? (
                    <TooltipContainer
                      width="120px"
                      positionLeft="0px"
                      positionBottom="0px"
                      positionLeftDecs="10px"
                      positionBottomDecs="unset"
                      positionTopDecs="-40px"
                      fontSize="14px"
                    >
                      <span className="tooltip-content">Select Media</span>

                      <img
                        src={SelectIcon}
                        alt="select All icon"
                        style={{ width: '20px', cursor: 'pointer' }}
                        onClick={() => {
                          setSelectedMedia([])
                          setSelectionView((p) => !p)
                        }}
                      />
                    </TooltipContainer>
                  ) : null}
                </>
              ) : null}

              {positionPermissions?.forms ? (
                <FormSelect oppId={oppId!} data={{ whereIsUse: FormAccess.Opportunity }} />
              ) : null}

              <TooltipContainer
                width="120px"
                positionLeft="0px"
                positionBottom="0px"
                positionLeftDecs="10px"
                positionBottomDecs="unset"
                positionTopDecs="-40px"
                fontSize="14px"
              >
                <span className="tooltip-content">Delete Media</span>
                {selectedMedia?.length || tagTypeFilters?.length ? (
                  <img
                    src={Delete}
                    alt="select icon"
                    style={{ width: '20px', cursor: 'pointer' }}
                    onClick={() => {
                      setShowDeleteModal(true)
                    }}
                  />
                ) : null}
              </TooltipContainer>

              <TooltipContainer
                width="120px"
                positionLeft="0px"
                positionBottom="0px"
                positionLeftDecs="10px"
                positionBottomDecs="unset"
                positionTopDecs="-40px"
                fontSize="14px"
              >
                <span className="tooltip-content">Share Media</span>
                {selectedMedia?.length || (isFilterApplied && filteredMedia?.length) ? (
                  <img
                    src={ShareIcon}
                    alt="share icon"
                    style={{ width: '20px', cursor: 'pointer' }}
                    onClick={() => {
                      setShowShareModal(true)
                    }}
                  />
                ) : null}
              </TooltipContainer>
              <TooltipContainer
                width="150px"
                positionLeft="0px"
                positionBottom="0px"
                positionLeftDecs="10px"
                positionBottomDecs="unset"
                positionTopDecs="-40px"
                fontSize="14px"
              >
                <span className="tooltip-content">Add/Remove Tags</span>
                {selectedMedia?.length || (isFilterApplied && filteredMedia?.length) ? (
                  <img
                    src={TagIcon}
                    alt="tag icon"
                    style={{ width: '20px', cursor: 'pointer' }}
                    onClick={() => {
                      const dataItems = data?.images?.filter((itm: any) => selectedMedia?.includes(itm?._id))
                      const tags = getCommonTags(dataItems)
                      setSelectedTags(tags)
                      setShowTagModal(true)
                    }}
                  />
                ) : null}
              </TooltipContainer>
              {downloadLoading ? (
                <div className="download-loader">
                  <FullpageLoader />
                </div>
              ) : (
                <TooltipContainer
                  width="100px"
                  positionLeft="0px"
                  positionBottom="0px"
                  positionLeftDecs="10px"
                  positionBottomDecs="unset"
                  positionTopDecs="-40px"
                  fontSize="14px"
                >
                  <span className="tooltip-content">Download</span>
                  {selectedMedia?.length || (isFilterApplied && filteredMedia?.length) ? (
                    <img
                      src={DownloadSvg}
                      alt="download icon"
                      style={{ width: '26px', cursor: 'pointer' }}
                      onClick={handleDownloadClick}
                    />
                  ) : null}
                </TooltipContainer>
              )}
            </FlexRow>
          )}

          {hasMedia ? (
            <FlexRow width="max-content" className="tag-cont">
              <FlexRow className="tags">
                {mediaTypeFilters?.map((itm) => (
                  <Tag
                    itm={{ name: MediaType[itm], type: 'Type' }}
                    onClose={() => handleCloseClick(itm, setTypeSelected, typeSelected)}
                    showRemoveIcon
                    key={itm}
                  />
                ))}

                {tagTypeFilters?.map((itm) => (
                  <Tag
                    itm={{ name: itm, type: 'Tag' }}
                    onClose={() => handleCloseClick(itm, setTagSelected, tagSelected)}
                    showRemoveIcon
                    key={itm}
                  />
                ))}
                {userTypeFilters?.map((itm) => (
                  <Tag
                    itm={{ name: itm, type: 'User' }}
                    onClose={() => handleCloseClick(itm, setUserSelected, userSelected)}
                    showRemoveIcon
                    key={itm}
                  />
                ))}
              </FlexRow>

              <DropdownContainer ref={dropdownRef} className="filter">
                <img
                  src={FilterSvg}
                  className="filter-icon"
                  alt="filter icon"
                  style={{ width: '20px', cursor: 'pointer' }}
                  onClick={() => setShowFilter((p) => !p)}
                />

                {showFilter ? (
                  <DropdownContent style={{ width: '150px', maxHeight: '500px', overflowY: 'scroll', right: '0px' }}>
                    <h3>Filter by</h3>
                    <FilterContainer
                      margin="10px 0 0 0"
                      gap="0px"
                      justifyContent="flex-start"
                      onClick={(e) => e.stopPropagation()}
                      className="media-filter"
                    >
                      <CheckboxList
                        className="first"
                        title=""
                        data={[]}
                        checkedItems={{}}
                        onSelectionChange={(_val) => {
                          setTypeSelected([])
                          setTagSelected([])
                          setUserSelected([])
                        }}
                        allText="All"
                        isCheckedAll={!mediaTypeFilters?.length && !tagTypeFilters?.length && !userTypeFilters?.length}
                      />
                      <CheckboxList
                        title="Type"
                        className="first"
                        data={uniqueValues.mediaTypes}
                        checkedItems={typeSelected}
                        onSelectionChange={(val) => {
                          handleFilterChange(val, setTypeSelected)
                        }}
                        hideAllCheckbox
                      />
                      <CheckboxList
                        title="People"
                        data={uniqueValues.users}
                        checkedItems={userSelected}
                        onSelectionChange={(val) => {
                          handleFilterChange(val, setUserSelected)
                        }}
                        hideAllCheckbox
                      />
                      <CheckboxList
                        checkedItems={tagSelected}
                        title="Tags"
                        data={uniqueValues.tags}
                        hideAllCheckbox
                        onSelectionChange={(val) => {
                          handleFilterChange(val, setTagSelected)
                        }}
                      />
                    </FilterContainer>
                  </DropdownContent>
                ) : null}
              </DropdownContainer>
            </FlexRow>
          ) : null}
        </FlexRow>
      </FlexCol>

      <Styled.MediaCont>
        {(!data?.images?.length && !loading) || (!loading && !filteredMedia?.length) ? (
          <FlexRow justifyContent="center">
            <h2>No Media Found</h2>
          </FlexRow>
        ) : (
          <>
            {Object?.entries(mediaData)
              ?.sort((a, b) => {
                return new Date(b?.[0])?.getTime() - new Date(a?.[0])?.getTime()
              })
              ?.map(([date, mediaList]) => (
                <div key={date}>
                  <h2>{dayjsFormat(date, 'MM-DD-YYYY')}</h2>

                  <Styled.VideoGrid>
                    {mediaList?.map((media: IMedia, idx: number) => (
                      <Styled.VideoCard
                        key={media?._id}
                        className={selectedMedia.includes(media?._id) ? 'isSelected' : ''}
                      >
                        <div
                          style={{ width: '100%', height: '100%', position: 'relative' }}
                          onClick={() => {
                            if (selectionView) {
                              handleMediaSelect(media)
                              return
                            }
                            setSelectedDate(media?.date!)
                            setSelectedIndex(idx)
                            setShowPreview(true)
                          }}
                        >
                          {renderMedia(media?.url, media?.mimetype, false, false, memberId, oppId!, media)}

                          {selectionView ? (
                            <input
                              type="checkbox"
                              checked={selectedMedia.includes(media?._id)}
                              onChange={() => handleMediaSelect(media)}
                              // onChange={() => handleMediaSelect(media?._id)}
                              // onClick={(e) => e.stopPropagation()}
                            />
                          ) : null}

                          <Styled.Timestamp>
                            <div className="fileName">
                              <TooltipContainer
                                width="220px"
                                positionLeft="0px"
                                positionBottom="0px"
                                positionLeftDecs="100px"
                                positionBottomDecs="20px"
                                positionTopDecs="-40px"
                                fontSize="14px"
                              >
                                <span className="tooltip-content">{media?.name}</span>

                                <span className="name">{media?.name}</span>
                              </TooltipContainer>{' '}
                              <span>{dayjsFormat(media.createdAt, 'hh:mma')}</span>
                            </div>
                          </Styled.Timestamp>
                        </div>
                      </Styled.VideoCard>
                    ))}
                  </Styled.VideoGrid>
                </div>
              ))}
          </>
        )}
      </Styled.MediaCont>

      {showPreview ? (
        <MediaPreview
          selectedIndex={selectedIndex}
          allMedia={allMedia}
          allTags={tagsData?.mediaSetting?.tags}
          isMediaSection
          memberId={memberId}
          setTagBool={setTagBool}
          onSuccess={() => {
            setBool((p) => !p)
            setSelectedIndex(0)
          }}
          onClose={() => {
            setShowPreview(false)
          }}
          info={{
            po: state?.poName || data?.PO,
            clientName: state?.clientName || `${data?.firstName || ''} ${data?.lastName || ''}`,
          }}
        />
      ) : null}

      <CustomModal show={showShareModal} className="media">
        <Modal
          title="Share Media"
          onClose={() => {
            !shareMediaLoader && setShowShareModal(false)
            setUrlHash('')
            setSelectedMedia([])
            setSelectionView((p) => !p)
          }}
        >
          <h3>Generate a public shareable link</h3>

          {shareMediaLoader ? (
            <SLoader height={45} width={100} isPercent />
          ) : (
            <div style={{ width: '100%' }}>
              {' '}
              <ShareLink url={`/media/${urlHash}`} />
            </div>
          )}
        </Modal>
      </CustomModal>

      <CustomModal show={showDeleteModal} className="media">
        <Modal
          title="Delete Media"
          onClose={() => {
            setShowDeleteModal(false)
          }}
        >
          <h3 className="text-center">Are you sure you want to delete the selected media?</h3>
          <FlexRow margin="20px 0 0 0">
            <Button
              onClick={() => {
                setShowDeleteModal(false)
              }}
              className="gray"
              disabled={deleteLoading}
            >
              Cancel
            </Button>
            <Button onClick={handleDeleteSelection} isLoading={deleteLoading} className="delete">
              Delete
            </Button>
          </FlexRow>
        </Modal>
      </CustomModal>

      <CustomModal show={showTagModal} className="media overflow">
        <Modal
          title="Add Media Tags"
          onClose={() => {
            setShowTagModal(false)
          }}
        >
          <div style={{ marginTop: '10px' }}>
            {selectedTags?.map((itm: string) => (
              <Tag
                key={itm}
                title={itm}
                showRemoveIcon
                onClose={() => {
                  setSelectedTags((p) => p.filter((tag) => tag !== itm))
                }}
              />
            ))}
          </div>
          <AutoComplete
            labelName="Tags"
            stateName="tags"
            options={tagsData?.mediaSetting?.tags?.filter((tag: string) => !selectedTags?.includes(tag))!}
            setValueOnClick={(val: string) => {
              setSelectedTags((p) => [...p, val])
            }}
            // showAddOption
            // onAddClick={() => {
            //   setShowAddModal(true)
            // }}
            dropdownHeight="300px"
            borderRadius="0px"
          />
          <FlexRow margin="20px 0 0 0">
            <Button
              onClick={() => {
                setShowTagModal(false)
              }}
              className="gray"
              disabled={updateLoading}
            >
              Cancel
            </Button>
            <Button onClick={handleUpdateMedia} isLoading={updateLoading}>
              Update media
            </Button>
          </FlexRow>
        </Modal>
      </CustomModal>
    </Styled.SectionCont>
  )
}

export default Media
