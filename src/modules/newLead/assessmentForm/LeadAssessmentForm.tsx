import { Field, Formik, FormikErrors, FormikTouched, FormikValues } from 'formik'
import React, { Fragment, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'
import { getProfileInfo } from '../../../logic/apis/profile'
import {
  getStep,
  updateActivity,
  updateLeadChecklist,
  updateOppDate,
  updateLeadCheckpoint,
  updateLeadStage,
  deleteLeadCheckpoint,
  completeLeadAction,
} from '../../../logic/apis/sales'
import Checkbox from '../../../shared/checkbox/Checkbox'
import Button from '../../../shared/components/button/Button'
import CustomSelect from '../../../shared/customSelect/CustomSelect'
import { SharedDate } from '../../../shared/date/SharedDate'
import {
  combineDateTime,
  convertStrToKey,
  convertTimeTo12HourFormat,
  findObjectKeyValue,
  getKeysFromObjects,
  getParentIdFromName,
  getSalesPersonNameFromId,
  getParentNameFromId,
  isSuccess,
  isoToMMDD,
  notify,
  parseTimeString,
  startOfDate,
  extractLocalTime,
  dayjsFormat,
  allValuesTrue,
  getDataFromLocalStorage,
} from '../../../shared/helpers/util'
import { InputWithValidation } from '../../../shared/inputWithValidation/InputWithValidation'
import { NormalInput } from '../../../shared/normalInput/NormalInput'
import * as SharedStyled from '../../../styles/styled'
import { I_Checkpoint, I_Step } from '../../crmSettings/CrmSettings'
import * as Styled from './style'
import ReactMarkdown from 'react-markdown'
import { StageGroupEnum, StorageKey } from '../../../shared/helpers/constants'
import { SharedDateAndTime } from '../../../shared/date/SharedDateAndTime'

export type AnyKey = { [key: string]: any }

interface I_FormProps {
  stages: I_Stage[]
  leadData: any
  checkpoints: I_Checkpoint[]
  setLeadData: any
  initFetchLeadOnAction: any
  setInputKeys: any
  fetchActivity: any
  // setStepForToDoNext: React.Dispatch<any>
  setStepForToDoNextSuccessFLag: React.Dispatch<React.SetStateAction<boolean>>
  checkListData: any
  stepForToDoNextFlag: boolean
}

interface I_Data {
  name: string
  type: string
  value: string | Date | number
  checked: boolean
  required: boolean
  options?: string[]
}

export interface I_Activity {
  body: string
  createdBy: string
  createdAt: string
  userName?: string
}

export interface I_Action {
  type: string
  body: string
  due: string
  completedBy: string
  // completedDate: string
  // completedTime: string
  _id?: string
}

export interface I_Value {
  value: string | boolean | number
  checked: boolean
  updatedAt: string
  updatedBy: string
}

export interface I_DataSlice {
  [key: string]: I_Value
}

export interface I_StepData {
  values: I_DataSlice
  completedAt?: string
  completedBy?: string
}

export interface I_User {
  firstName: string
  lastName: string
  preferredName: string
  // username: string
  email: string
  roles: string
}

export interface I_Comment {
  oppNotes: string
  oppTime: string
  createdBy: string
  _id: string
  name?: string
}

export interface I_Stage {
  name: string
  _id: string
  sequence: number
  stageGroup: string
}
export interface InitValuesForDateTime {
  dateForSchedule: string
  timeForSchedule: string
  checked: boolean
}
export interface DateTimeArray {
  dateTime: InitValuesForDateTime[]
}
const scheduleSchema = Yup.object().shape({
  date: Yup.string().required('Required'),
  time: Yup.string().required('Required'),
  scheduledBy: Yup.string(),
})

const LeadAssessmentForm: React.FC<I_FormProps> = (props) => {
  const {
    leadData,
    stages,
    setLeadData,
    initFetchLeadOnAction,
    checkpoints,
    setInputKeys,
    fetchActivity,
    // setStepForToDoNext,
    setStepForToDoNextSuccessFLag,
    checkListData,
    stepForToDoNextFlag,
  } = props
  const [curStage, setCurStage] = useState<any>({})
  const { leadId } = useParams()

  const [scheduling, setScheduling] = useState(-1)

  const [initSchedule, setInitSchedule] = useState<any>({})
  const [dates, setDates] = useState<AnyKey>({})
  const [updateLoading, setUpdateLoading] = useState(false)
  const [parentType, setParentType] = useState(true)
  const [allSteps, setAllSteps] = useState<I_Step[]>([]) // all steps including children
  const [steps, setSteps] = useState<Array<I_Step>>([]) // only parent steps
  const [stepsLoading, setStepsLoading] = useState(true)
  const [buttonFlag, setButtonFlag] = useState(true)
  const [symbols, setSymbols] = useState([])
  const [chkDateFlag, setCHkDateFlag] = useState([])
  const [symbol, setSymbol] = useState('')
  const [symbolDisplay, setSymbolDisplay] = useState('')
  const [timeSchedule, setTimeSchedule] = useState('')
  const [loading, setLoading] = useState(false)
  const [loadingDateCheckpoint, setLoadingDateCheckpoint] = useState('')
  const [initialValues, setInitialValues] = useState<any>({})
  const [userInfo, setUserInfo] = useState<I_User | undefined>()
  const [dateCheckState, setDateCheckState] = useState({})
  const [initValues, setInitValues] = useState<AnyKey>({})
  const [initValuesRequire, setInitValuesRequire] = useState<AnyKey>({})
  const [keyNames, setKeyNames] = useState<string[]>([])

  const navigate = useNavigate()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  // const initvaluesFOrProjectManager = {
  //   projectManager: leadData?.projectManager
  //     ? getSalesPersonNameFromId(leadData?.projectManager, salesManagerDrop)
  //     : '',
  // }

  const dateCheckpointFlag = Object.values(dateCheckState)?.length
    ? Object.values(dateCheckState)?.every(Boolean)
    : true

  useEffect(() => {
    if (checkpoints.length) {
      checkpoints.map((chk: any, idx) => {
        if (chk.stageSet?.includes(curStage._id)) {
          setSymbol(chk?.symbol)
        }

        if (chk.stageSet?.includes(curStage._id) && chk.requiredStage === curStage._id) {
          setDateCheckState((prevValues) => ({
            ...prevValues,
            [`${chk._id}`]: false,
          }))
        }
      })
    }
  }, [checkpoints, curStage])

  useEffect(() => {
    checkpoints.map((chk: any, idx) => {
      if (chk.stageDisplay?.includes(curStage._id)) {
        setSymbolDisplay(chk?.symbol)
      }
    })
  }, [checkpoints, curStage])
  useEffect(() => {
    if (leadData && curStage && stages.length && !stepForToDoNextFlag) {
      const stage: I_Stage | undefined = findCurrStage(leadData, stages)
      if (stage) {
        setCurStage(stage)
        fetchSteps(stage._id)
      }
      let newSchedule: AnyKey = {}
      const localInputKeys: AnyKey = {}
      checkpoints.forEach((chk) => {
        newSchedule[convertStrToKey(chk.name + ' Date')] = leadData[convertStrToKey(chk.name + ' Date')] ?? ''
        localInputKeys[convertStrToKey(chk.name + ' Date')] = chk.name
      })
      setDates(newSchedule)
      setInputKeys((prev: any) => ({ ...localInputKeys, ...prev }))
    }
  }, [leadData, stages, checkpoints, stepForToDoNextFlag])

  useEffect(() => {
    fetchUser()
  }, [])

  const fetchUser = async () => {
    try {
      const response = await getProfileInfo()
      if (isSuccess(response)) {
        let user = response?.data?.data?.user
        let userObject = {
          firstName: user.firstName,
          lastName: user.lastName,
          preferredName: user.preferredName,
          // username: user.username,
          email: user.email,
          roles: 'madmin',
        }
        setUserInfo({ ...userObject })
      } else throw new Error(response?.data?.message)
    } catch (err) {
      console.log('FETCH USER FAILED!', err)
    }
  }

  const findCurrStage = (leadData: any | AnyKey, stages: I_Stage[]) => {
    let curStage: I_Stage | undefined
    stages.forEach((stage) => {
      if (stage.name === leadData.stage || stage._id === leadData.stage) {
        curStage = stage
        return
      }
    })
    return curStage
  }

  const fetchSteps = async (stageId: string) => {
    let steps: I_Step[] = []
    try {
      const response = await getStep(
        { stageId },
        false,
        leadData?.acceptedType ? leadData?.acceptedType : leadData?.oppType,
        leadData?.state
      )

      if (response?.status === 200 || response?.statusCode === 200) {
        const resSteps: I_Step[] = response?.data.data.step
        const localSteps = Array(resSteps.length)
        setStepsLoading(false)
        {
        }

        resSteps.forEach((resStep: I_Step) => {
          // only without parent
          if (!resStep.parent) {
            // localSteps[resStep.sequence] = resStep
            localSteps.push(resStep)
          }
        })

        steps = localSteps.filter((step) => step)?.sort((a, b) => a?.sequence - b?.sequence) // if step is defined

        setSteps(steps)
        // setStepForToDoNext(steps)
        setStepForToDoNextSuccessFLag(true)
        const allStepsLocal = [...steps]
        // push children on different locations
        resSteps.forEach((step) => {
          if (!!step.parent) {
            allStepsLocal.push(step)
          }
        })

        let localInputKeys: AnyKey = {}
        let newInitStepsVal: AnyKey = {}
        let newInitStepsValRequire: AnyKey = {}

        allStepsLocal.forEach((step) => {
          // const stageData = (leadData as AnyKey)[curStage.name]
          const stageData = checkListData
          let value: any
          newInitStepsVal[step._id] = {}
          if (step.fieldType === 'None') {
            value = stageData ? stageData[step._id]?.boolean ?? false : false
            newInitStepsVal[step._id].boolean = value
          } else {
            value = stageData ? stageData[step._id]?.boolean ?? false : false
            newInitStepsVal[step._id].boolean = value
            value = stageData ? stageData[step._id]?.value ?? '' : ''
            newInitStepsVal[step._id].value = value
          }
          localInputKeys[step._id] = step._id
          // localInputKeys[convertStrToKey(step._id + ' Result')] = step._id
        })

        allStepsLocal
          .filter((obj: any) => obj.isRequire)
          .forEach((step) => {
            // const stageData = (leadData as AnyKey)[curStage.name]
            const stageData = checkListData
            let value: any

            // ---------------------------new check--------------------------------------------
            // if (checkListData?.checklists?.hasOwnProperty([convertStrToKey(step.name)])) {
            //   value = stageData ? stageData?.checklists[convertStrToKey(step.name)]?.value ?? false : false
            //   newInitStepsValRequire[convertStrToKey(step.name)] = value
            // }
            // ---------------------------new check--------------------------------------------
            // ---------------------------old check--------------------------------------------
            newInitStepsValRequire[step._id] = {}
            if (step.fieldType === 'None') {
              value = stageData ? stageData[step._id]?.boolean ?? false : false
              newInitStepsValRequire[step._id].boolean = value
            } else {
              value = stageData ? stageData[step._id]?.boolean ?? false : false
              newInitStepsValRequire[step._id].boolean = value
              value = stageData ? stageData[step._id]?.value ?? '' : ''
              newInitStepsValRequire[step._id].value = value
            }
            localInputKeys[step._id] = step._id
            // localInputKeys[convertStrToKey(step._id + ' Result')] = step._id
            // ---------------------------old check--------------------------------------------
          })
        setInputKeys((prev: any) => ({ ...localInputKeys, ...prev }))
        setInitValuesRequire(newInitStepsValRequire)
        setInitValues(newInitStepsVal)
        setAllSteps(allStepsLocal)
      } else {
        notify('Unable to fetch steps', 'error')
        setAllSteps([])
        setSteps([])
        setStepsLoading(false)
      }
    } catch (err) {
      console.log('FETCH ERR STEP', err)
      setAllSteps([])
      setSteps([])
      setStepsLoading(false)
    }
  }

  // could return undefined
  const getStepDataFromOpp = (curStage: I_Stage) => {
    const stageKey = convertStrToKey(curStage?.name)
    return (leadData as AnyKey)[stageKey]
  }

  const getCurStageId = () => {
    let curStageId = -1
    stages!.forEach((stage, idx) => {
      if (stage.name === leadData!.stage || stage._id === leadData!.stage) {
        curStageId = idx
        return
      }
    })
    return curStageId
  }

  const handlePrevStage = async () => {
    try {
      const curStageId = getCurStageId()
      // await handleOppUpdate({ stage: stages![curStageId + 1].name })
      const response = await updateLeadStage({
        currDate: new Date().toISOString(),
        id: leadId!,
        memberId: currentMember._id,
        newStage: stages[curStageId - 1]._id,
      })

      if (isSuccess(response)) {
        if (curStage?.code === 'preparePacket') {
          navigate(`/sales/opportunity/${leadId}`)
        }
        notify('Stage changed!', 'success')
        initFetchLeadOnAction()
        fetchActivity()
      }
    } catch (err) {
      notify('Failed to change stage!', 'error')
      console.log('ERR', err)
    }
  }

  const handleNextStage = async () => {
    try {
      // if (projectManager === '' && curStage?.PMRequired) {
      //   notify('Project Manager must be assigned before moving to the next stage', 'error')
      //   return
      // }
      const curStageId = getCurStageId()
      // await handleOppUpdate({ stage: stages![curStageId + 1].name })
      const response = await updateLeadStage({
        currDate: new Date().toISOString(),
        id: leadId!,
        memberId: currentMember._id!,
        newStage: stages[curStageId + 1]?._id,
      })
      // error massage to show over toaster with it's type validation
      if (isSuccess(response)) {
        notify('Stage changed!', 'success')
        initFetchLeadOnAction()
        fetchActivity()
      } else {
        if (typeof response?.data?.message === 'string') {
          notify(response?.data?.message, 'error')
        } else if (Array.isArray(response?.data?.message)) {
          notify(response?.data?.message.join(', '), 'error')
        } else {
          notify(response?.data?.message, 'error')
        }
      }
    } catch (err) {
      console.log('ERR', err)
    }
  }

  const handleScheduleSubmit = async (sequence: number, values: typeof initSchedule, id: string) => {
    let date1: any, date2: any
    // let updateDate = values.date?.includes('"') ? startOfDate(values.date) : startOfDate(`"${values.date}"`)
    let updateDate = startOfDate(dayjsFormat(values.date, 'YYYY-MM-DD'))
    let currDate: any = combineDateTime(new Date(updateDate), new Date(parseTimeString(values.time))).toISOString()
    if (sequence > 1) {
      const obj: any = checkpoints?.filter((chk) => chk.sequence === sequence - 1 && chk)
      date1 = new Date(leadData?.[obj?.[0]?.symbol])
      date2 = new Date(currDate)
    }
    if (date2 < date1 && sequence > 1) {
      notify(
        `Next step date must be after previous: ${dayjsFormat(date1, 'M/D/YY')} ${new Date(date1).toLocaleTimeString(
          'en-US',
          {
            hour: '2-digit',
            minute: 'numeric',
            hour12: false,
          }
        )}`,
        'error'
      )
      // notify('Next step can not happen before previous step', 'error')
      throw new Error('date error')
    }

    try {
      // setLoading(true)

      // setLeadData((prev: AnyKey) => ({
      //   ...prev,
      //   [convertStrToKey(checkpoints[scheduling].name + ' Date')]: combineDateTime(
      //     new Date(updateDate),
      //     new Date(parseTimeString(values.time))
      //   ).toISOString(),
      // }))

      // handleOppUpdate(scheduleData)
      const res = await updateLeadCheckpoint({
        id: leadId!,
        currDate: combineDateTime(new Date(updateDate), new Date(parseTimeString(values.time))).toISOString(),
        checkpointId: id,
        memberId: currentMember._id,
      })

      const response = await updateOppDate({
        currDate: new Date().toISOString(),
        date: combineDateTime(new Date(updateDate), new Date(parseTimeString(values.time))).toISOString(),
        dateLabel: convertStrToKey(checkpoints[scheduling].name + ' Date'),
        opportunityId: leadId!,
        updatedBy: currentMember._id!, //UserId##
      })
      if (isSuccess(response) || isSuccess(res)) {
        notify('Updated date!', 'success')
        initFetchLeadOnAction()
        fetchActivity()
        // setLoading(false)
      } else notify('Failed to update date!', 'error')
      setScheduling(-1)
    } catch (err) {
      console.log('SCHEDULE ERR', err)
      // setLoading(false)
    }
  }

  const updateField = async (
    values: { [key: string]: string },
    key: string,
    value: string,
    boolean: boolean,
    checked?: boolean,
    name?: string,
    isField?: boolean,
    step?: I_Step
  ) => {
    try {
      setUpdateLoading(true)
      let newActivities: I_Activity[] = leadData?.activities ?? []
      console.log({ firstsdfsfsdf: value }, isField)
      let body =
        typeof checked === 'undefined'
          ? `Changed ${name || key} to ${value}.`
          : isField
          ? `Changed ${name || key} to ${value}.`
          : `Changed ${name || key} to ${checked ? 'done' : 'not done'}.`
      newActivities.push({
        body,
        createdAt: new Date().toISOString(),
        createdBy: currentMember._id,
        userName: `${userInfo?.firstName} ${userInfo?.lastName ?? ''}`?.trim(),
      })
      const resActivity = await updateActivity({
        id: leadId!,
        memberId: currentMember._id!,
        body:
          typeof checked === 'undefined'
            ? `Changed ${name || key} to ${value}`
            : isField
            ? `Changed ${name || key} to ${value}.`
            : `Changed ${name || key} to ${checked ? 'done' : 'not done'}.`,
        currDate: new Date().toISOString(),
      })

      let stepData: any = {}
      if (leadData && leadData?.stepsChecklist?.[curStage._id]) stepData = leadData?.stepsChecklist?.[curStage._id]
      const res = await updateLeadChecklist({
        currDate: new Date().toISOString(),
        key: key,
        value,
        boolean,
        leadId: leadId!,
        stage: curStage._id,
        updatedBy: currentMember._id!, //UserId##
      })

      if (step && step?.activityType !== 'None' && boolean) {
        await completeLeadAction({
          body: step.name,
          currDate: new Date(),
          dueDate: new Date(),
          memberId: currentMember._id!, //UserId##
          leadId: leadId!,
          type: step.activityType,
          id: step._id,
        })
      }

      if (isSuccess(res)) {
        setUpdateLoading(false)
        fetchActivity()
        // fetchUpdateStepChecklist()
        // initFetchLeadOnAction()
      }

      setLeadData((prev: AnyKey) => {
        let newOppObj = {
          ...prev,
          stepsChecklist: {
            ...prev['stepsChecklist'],
            [curStage._id]: {
              ...prev['stepsChecklist']?.[curStage._id],
              [key]: {
                value: value ?? prev['stepsChecklist']?.[curStage._id]?.[key]?.value ?? undefined,
                boolean: !!checked,
                // updatedAt: new Date().toISOString(),
                // updatedBy: currentMember._id!, // Replace with actual UserId
              },
            },
          },
          activities: newActivities,
        }
        return newOppObj
      })
    } catch (err) {
      setUpdateLoading(false)
      console.log('UPDATE FIELD FAILED!', err)
      notify('Failed to update field', 'error')
    }
  }

  const getParentSteps = (curStep: string, allSteps: I_Step[]) => {
    let localParentSteps: any[] = []
    allSteps.map((step) => {
      if (!step.parent || step.parent === '') {
        const hasChildSteps = allSteps.some((childStep) => childStep.parent === step._id)
        localParentSteps.push({ ...step, isChild: hasChildSteps })
      }
    })

    const formatParentSteps = new Array(localParentSteps.length)
    localParentSteps.forEach((parentStep) => {
      formatParentSteps[parentStep.sequence] = parentStep
    })
    return formatParentSteps
  }

  const getChildSteps = (curStep: string, allSteps: I_Step[]) => {
    let localChildSteps: any[] = []
    allSteps.map((step) => {
      if (step.parent === getParentIdFromName(curStep, allSteps)) {
        localChildSteps.push(step)
      }
    })

    const formatChildSteps = new Array(localChildSteps.length)
    localChildSteps.forEach((childStep) => {
      formatChildSteps[childStep.sequence] = childStep
    })
    return formatChildSteps
  }

  const getCommonKeysAndValues = (obj1: any, obj2: any) => {
    const commonKeysAndValues: any = {}
    for (const key in obj1) {
      if (obj1.hasOwnProperty(key) && obj2.hasOwnProperty(key)) {
        commonKeysAndValues[key] = obj2[key]
      }
    }
    return commonKeysAndValues
  }

  const getIsChildByName = (name: string, response: any) => {
    const matchedStep = response.find((step: any) => step?.name === name)
    return matchedStep ? matchedStep.isChild : false
  }

  const getValueComponent = (
    curStep: I_Step,
    errors: FormikErrors<AnyKey>,
    touched: FormikTouched<AnyKey>,
    setFieldValue: (field: string, value: any, shouldValidate?: boolean | undefined) => void,
    values: AnyKey,
    handleSubmit: any,
    renderLinkedTo: (_id: string) => void,
    isChild: boolean,
    isParent: boolean
  ): any => {
    const childSteps: I_Step[] = getChildSteps(curStep.name, allSteps)
    const parntStep = getParentSteps(curStep.name, allSteps)
    let checkedValue = values[curStep?._id]?.boolean
    let otherValue: string = ''
    if (curStep.fieldType !== 'None') otherValue = values[curStep?._id]?.value || ''

    if (curStep.parent === '' || !curStep.hasOwnProperty('parent')) {
      setParentType(true)
    } else {
      setParentType(false)
    }

    // -----------------------------chnages for required----------------------------

    const finalRequiredObject = getCommonKeysAndValues(initValuesRequire, values)
    const datafilter = Object.values(finalRequiredObject)
      .map((v) => v.boolean)
      .filter((v) => typeof v === 'boolean')
    const allValuesAreTrue = datafilter.every((value: any) => value === true)

    setButtonFlag(allValuesAreTrue)

    const formatValue = (value) => {
      if (!value) return ''
      const parts = value?.split('.')
      if (parts.length > 1) {
        // Keep only the first 2 decimal places
        return `${parts[0]}.${parts[1]?.slice(0, 2)}`
      }
      return value
    }

    const handleBlurOrCheck = (
      name: string,
      field: string,
      value: string,
      boolean: boolean,
      checked?: boolean,
      isField?: boolean,
      step?: I_Step
    ) => {
      updateField(values, field, value, boolean, checked, name, isField, step)
    }
    const handleCheck = (step: I_Step) => {
      setStepsLoading(true)
      const boolean = values[step._id].boolean || false
      const value =
        typeof values[step._id].value === 'number' && values[step._id].value === 0
          ? 0
          : values[step._id].value ?? undefined
      // const value = values[step._id].value || undefined
      setFieldValue(`${step._id}.boolean`, !boolean)
      setFieldValue(`${step._id}.value`, value)
      handleBlurOrCheck(step.name, step._id, value, !boolean, !boolean, false, step)
      // }
    }

    const getCurComponent = () => {
      const childdata = childSteps?.filter((v) => v.isRequire)?.map((v) => values[v?._id]?.boolean)
      const finaldata = childdata.every((v) => v === true)

      const isDropdownDisabled = () => {
        if (isParent) {
          if (!isChild) {
            // If it's a child of a parent
            if (!finaldata) {
              // If all children are checked, disable the child checkbox
              return true
            }
            // Child checkbox is enabled if at least one child is unchecked
            return false
          }
          // If it's a parent checkbox
          // Parent checkbox is always enabled regardless of the state of children
          return false
        } else if (isChild) {
          if (values[curStep?.parent].boolean && finaldata) {
            return true
          }
          // if (values[convertStrToKey(getParentNameFromId(curStep?.parent, allSteps))] && finaldata) {
          //   return true
          // }
          return false
        } else if (!isChild) {
          // If it's an independent child checkbox
          // Child checkbox is enabled for independent children
          return false
        }
        // Other checkboxes (not parent or child) are enabled by default
        return false
      }

      const handleChange = (e) => {
        const value = e.target.value
        // Format the value to have at most 2 decimal places
        const formattedValue = formatValue(value)

        setFieldValue(`${curStep._id}.value`, formattedValue)
      }

      const data = getChildTo(curStep._id)
      switch (curStep.fieldType) {
        case 'None':
          return (
            <SharedStyled.FlexBox alignItems="center" margin={!isParent ? '0' : ' 0'}>
              <div>
                <Checkbox
                  margin="0"
                  key={convertStrToKey(curStep.name)}
                  title={convertStrToKey(curStep.name)}
                  value={checkedValue}
                  onChange={() => {
                    handleCheck(curStep)
                    setStepsLoading(true)
                  }}
                  disabled={isDropdownDisabled() || values[curStep?._id]?.value === ''}
                  hideTitle
                ></Checkbox>
              </div>
              <Styled.Text
                whiteSpace="pre"
                fontWeight={!getIsChildByName(curStep?.name, parntStep) ? 'normal' : 'bold'}
              >
                {curStep.name} {curStep.isRequire ? '*' : ''}
                {curStep?.description ? (
                  <SharedStyled.TooltipContainer
                    width="300px"
                    textAlign="left"
                    // maxWidth="300px"
                    // whiteSpace="noWrap"
                    padding="10px"
                    positionLeft="0"
                    positionBottom="0"
                    positionLeftDecs="82px"
                    positionBottomDecs="25px"
                  >
                    <span className="tooltip-content">
                      <ReactMarkdown
                        components={{
                          h1: ({ node, ...props }) => (
                            <h1 style={{ fontSize: '2em', color: 'white', margin: 0 }} {...props} />
                          ),
                          h2: ({ node, ...props }) => (
                            <h2 style={{ fontSize: '1.75em', color: 'white', margin: 0 }} {...props} />
                          ),
                          h3: ({ node, ...props }) => (
                            <h3 style={{ fontSize: '1.5em', color: 'white', margin: 0 }} {...props} />
                          ),
                          h4: ({ node, ...props }) => (
                            <h4 style={{ fontSize: '1.25em', color: 'white', margin: 0 }} {...props} />
                          ),
                          h5: ({ node, ...props }) => (
                            <h5 style={{ fontSize: '1em', color: 'white', margin: 0 }} {...props} />
                          ),
                          h6: ({ node, ...props }) => (
                            <h6 style={{ fontSize: '0.875em', color: 'white', margin: 0 }} {...props} />
                          ),
                          p: ({ node, ...props }) => <p style={{ color: 'white', margin: 0 }} {...props} />,
                          ul: ({ node, ...props }) => <ul style={{ color: 'white', margin: 0 }} {...props} />,
                          ol: ({ node, ...props }) => <ol style={{ color: 'white', margin: 0 }} {...props} />,
                          li: ({ node, ...props }) => <li style={{ color: 'white', margin: 0 }} {...props} />,
                          hr: ({ node, ...props }) => (
                            <hr style={{ margin: 0, border: '1px solid white' }} {...props} />
                          ),
                        }}
                      >
                        {curStep?.description?.replace(/\n/g, '  \n')}
                      </ReactMarkdown>
                    </span>
                    <Styled.IButton>i</Styled.IButton>
                  </SharedStyled.TooltipContainer>
                ) : null}
              </Styled.Text>
            </SharedStyled.FlexBox>
          )
        case 'Date':
          return (
            <SharedStyled.FlexBox alignItems="center" margin={!isParent ? '0' : ' 0'}>
              <div>
                <Checkbox
                  margin="0"
                  key={convertStrToKey(curStep.name)}
                  title={convertStrToKey(curStep.name)}
                  value={values[curStep?._id]?.boolean}
                  onChange={() => {
                    handleCheck(curStep)
                    setStepsLoading(true)
                  }}
                  // disabled={otherValue && finaldata ? false : true}
                  disabled={isDropdownDisabled() || values[curStep?._id]?.value === ''}
                  hideTitle
                ></Checkbox>
              </div>
              <SharedStyled.FlexBox width="100%" gap="12px" alignItems="center">
                <Styled.Text
                  whiteSpace="pre"
                  fontWeight={!getIsChildByName(curStep?.name, parntStep) ? 'normal' : 'bold'}
                  // fontWeight={!isParent ? 'normal' : 'bold'}
                >
                  {curStep.name} {curStep.isRequire ? '*' : ''}{' '}
                  {curStep?.description ? (
                    <SharedStyled.TooltipContainer
                      width="300px"
                      padding="10px"
                      positionLeft="0"
                      positionBottom="0"
                      positionLeftDecs="40px"
                      positionBottomDecs="25px"
                    >
                      <span className="tooltip-content">
                        <ReactMarkdown
                          components={{
                            h1: ({ node, ...props }) => (
                              <h1 style={{ fontSize: '2em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h2: ({ node, ...props }) => (
                              <h2 style={{ fontSize: '1.75em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h3: ({ node, ...props }) => (
                              <h3 style={{ fontSize: '1.5em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h4: ({ node, ...props }) => (
                              <h4 style={{ fontSize: '1.25em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h5: ({ node, ...props }) => (
                              <h5 style={{ fontSize: '1em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h6: ({ node, ...props }) => (
                              <h6 style={{ fontSize: '0.875em', color: 'white', margin: 0 }} {...props} />
                            ),
                            p: ({ node, ...props }) => <p style={{ color: 'white', margin: 0 }} {...props} />,
                            ul: ({ node, ...props }) => <ul style={{ color: 'white', margin: 0 }} {...props} />,
                            ol: ({ node, ...props }) => <ol style={{ color: 'white', margin: 0 }} {...props} />,
                            li: ({ node, ...props }) => <li style={{ color: 'white', margin: 0 }} {...props} />,
                            hr: ({ node, ...props }) => (
                              <hr style={{ margin: 0, border: '1px solid white' }} {...props} />
                            ),
                          }}
                        >
                          {curStep?.description?.replace(/\n/g, '  \n')}
                        </ReactMarkdown>
                      </span>
                      <Styled.IButton>i</Styled.IButton>
                    </SharedStyled.TooltipContainer>
                  ) : null}
                </Styled.Text>
                <SharedDate
                  value={otherValue}
                  labelName={curStep.name}
                  stateName={`${curStep._id}.value`}
                  setFieldValue={setFieldValue}
                  error={touched[curStep._id]?.value && errors[curStep._id]?.value ? true : false}
                  disabled={values[curStep?._id]?.boolean}
                  onBlur={() => {
                    handleBlurOrCheck(curStep.name, curStep._id, values[curStep?._id]?.value, false, false, true)
                  }}
                  isSmall
                  showBorder
                  maxWidth="200px"
                />
              </SharedStyled.FlexBox>
            </SharedStyled.FlexBox>
          )
        case 'Text':
          return (
            <SharedStyled.FlexBox alignItems="center" margin={!isParent ? '0' : ' 0'}>
              <div>
                <Checkbox
                  margin="0"
                  key={convertStrToKey(curStep.name)}
                  title={convertStrToKey(curStep.name)}
                  value={values[curStep?._id]?.boolean}
                  onChange={() => {
                    handleCheck(curStep)
                    setStepsLoading(true)
                  }}
                  // disabled={otherValue && finaldata ? false : true}
                  disabled={isDropdownDisabled() || values[curStep?._id]?.value?.trim() === ''}
                  hideTitle
                ></Checkbox>
              </div>
              <SharedStyled.FlexBox width="100%" gap="12px" alignItems="center">
                <Styled.Text
                  whiteSpace="pre"
                  fontWeight={!getIsChildByName(curStep?.name, parntStep) ? 'normal' : 'bold'}
                >
                  {curStep.name} {curStep.isRequire ? '*' : ''}
                  {curStep?.description ? (
                    <SharedStyled.TooltipContainer
                      width="300px"
                      padding="10px"
                      positionLeft="0"
                      positionBottom="0"
                      positionLeftDecs="40px"
                      positionBottomDecs="25px"
                    >
                      <span className="tooltip-content">
                        <ReactMarkdown
                          components={{
                            h1: ({ node, ...props }) => (
                              <h1 style={{ fontSize: '2em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h2: ({ node, ...props }) => (
                              <h2 style={{ fontSize: '1.75em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h3: ({ node, ...props }) => (
                              <h3 style={{ fontSize: '1.5em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h4: ({ node, ...props }) => (
                              <h4 style={{ fontSize: '1.25em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h5: ({ node, ...props }) => (
                              <h5 style={{ fontSize: '1em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h6: ({ node, ...props }) => (
                              <h6 style={{ fontSize: '0.875em', color: 'white', margin: 0 }} {...props} />
                            ),
                            p: ({ node, ...props }) => <p style={{ color: 'white', margin: 0 }} {...props} />,
                            ul: ({ node, ...props }) => <ul style={{ color: 'white', margin: 0 }} {...props} />,
                            ol: ({ node, ...props }) => <ol style={{ color: 'white', margin: 0 }} {...props} />,
                            li: ({ node, ...props }) => <li style={{ color: 'white', margin: 0 }} {...props} />,
                            hr: ({ node, ...props }) => (
                              <hr style={{ margin: 0, border: '1px solid white' }} {...props} />
                            ),
                          }}
                        >
                          {curStep?.description?.replace(/\n/g, '  \n')}
                        </ReactMarkdown>
                      </span>
                      <Styled.IButton>i</Styled.IButton>
                    </SharedStyled.TooltipContainer>
                  ) : null}
                </Styled.Text>
                <Styled.FlexDivForBorder>
                  {curStep?.lable && (
                    <Styled.UnitDiv marginBottom={curStep?.lable === 'amt: $' && '-2px'}>
                      {curStep?.lable}
                    </Styled.UnitDiv>
                  )}
                  <InputWithValidation
                    forceType="text"
                    value={otherValue}
                    labelName={curStep.name}
                    stateName={`${curStep._id}.value`}
                    error={touched[curStep._id]?.value && errors[curStep._id]?.value ? true : false}
                    disabled={values[curStep?._id]?.boolean}
                    onBlur={() => {
                      handleBlurOrCheck(curStep.name, curStep._id, values[curStep?._id]?.value, false, false, true)
                    }}
                    isSmall
                    showBorder
                    maxWidth="200px"
                  />
                </Styled.FlexDivForBorder>
              </SharedStyled.FlexBox>
            </SharedStyled.FlexBox>
          )
        case 'Number':
        case 'Currency':
          return (
            <SharedStyled.FlexBox alignItems="center" margin={!isParent ? '0' : ' 0'} className="small">
              <div>
                <Checkbox
                  margin="0"
                  key={convertStrToKey(curStep.name)}
                  title={convertStrToKey(curStep.name)}
                  value={values[curStep?._id]?.boolean}
                  onChange={() => {
                    handleCheck(curStep)
                    setStepsLoading(true)
                  }}
                  disabled={isDropdownDisabled() || values[curStep?._id]?.value === ''}
                  hideTitle
                  // disabled={otherValue && finaldata ? false : true}
                ></Checkbox>
              </div>
              <SharedStyled.FlexBox width="100%" gap="12px" alignItems="center">
                <Styled.Text
                  whiteSpace="pre"
                  fontWeight={!getIsChildByName(curStep?.name, parntStep) ? 'normal' : 'bold'}
                >
                  {curStep.name} {curStep.isRequire ? '*' : ''}
                  {curStep?.description ? (
                    <SharedStyled.TooltipContainer
                      width="300px"
                      padding="10px"
                      positionLeft="0"
                      positionBottom="0"
                      positionLeftDecs="40px"
                      positionBottomDecs="25px"
                    >
                      <span className="tooltip-content">
                        <ReactMarkdown
                          components={{
                            h1: ({ node, ...props }) => (
                              <h1 style={{ fontSize: '2em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h2: ({ node, ...props }) => (
                              <h2 style={{ fontSize: '1.75em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h3: ({ node, ...props }) => (
                              <h3 style={{ fontSize: '1.5em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h4: ({ node, ...props }) => (
                              <h4 style={{ fontSize: '1.25em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h5: ({ node, ...props }) => (
                              <h5 style={{ fontSize: '1em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h6: ({ node, ...props }) => (
                              <h6 style={{ fontSize: '0.875em', color: 'white', margin: 0 }} {...props} />
                            ),
                            p: ({ node, ...props }) => <p style={{ color: 'white', margin: 0 }} {...props} />,
                            ul: ({ node, ...props }) => <ul style={{ color: 'white', margin: 0 }} {...props} />,
                            ol: ({ node, ...props }) => <ol style={{ color: 'white', margin: 0 }} {...props} />,
                            li: ({ node, ...props }) => <li style={{ color: 'white', margin: 0 }} {...props} />,
                            hr: ({ node, ...props }) => (
                              <hr style={{ margin: 0, border: '1px solid white' }} {...props} />
                            ),
                          }}
                        >
                          {curStep?.description?.replace(/\n/g, '  \n')}
                        </ReactMarkdown>
                      </span>
                      <Styled.IButton>i</Styled.IButton>
                    </SharedStyled.TooltipContainer>
                  ) : null}
                </Styled.Text>
                <Styled.FlexDivForBorder>
                  {curStep?.lable && (
                    <Styled.UnitDiv marginBottom={curStep?.lable === 'amt: $' ? '-2px' : '0'}>
                      {curStep?.lable}
                    </Styled.UnitDiv>
                  )}
                  <InputWithValidation
                    forceType="number"
                    value={values[curStep?._id]?.value || ''}
                    labelName={curStep.name}
                    stateName={`${curStep._id}.value`}
                    error={touched[curStep._id]?.value && errors[curStep._id]?.value ? true : false}
                    disabled={values[curStep?._id]?.boolean}
                    onChange={curStep.fieldType === 'Currency' ? handleChange : false}
                    onBlur={() => {
                      handleBlurOrCheck(curStep.name, curStep._id, values[curStep?._id]?.value, false, false, true)
                    }}
                    isSmall
                    showBorder
                    maxWidth="200px"
                  />
                </Styled.FlexDivForBorder>
              </SharedStyled.FlexBox>
            </SharedStyled.FlexBox>
          )
        case 'Dropdown':
          return (
            <SharedStyled.FlexBox alignItems="center" margin={!isParent ? '0' : ' 0'}>
              <div>
                <Checkbox
                  margin="0"
                  key={convertStrToKey(curStep.name)}
                  title={convertStrToKey(curStep.name)}
                  value={values[curStep?._id]?.boolean}
                  onChange={() => {
                    handleCheck(curStep)
                    setStepsLoading(true)
                  }}
                  disabled={isDropdownDisabled() || values[curStep?._id]?.value === ''}
                  hideTitle
                  // disabled={otherValue && finaldata ? false : true}
                ></Checkbox>
              </div>
              <SharedStyled.FlexBox width="100%" gap="12px" alignItems="center">
                <Styled.Text
                  whiteSpace="pre"
                  fontWeight={!getIsChildByName(curStep?.name, parntStep) ? 'normal' : 'bold'}
                >
                  {curStep.name} {curStep.isRequire ? '*' : ''}
                  {curStep?.description ? (
                    <SharedStyled.TooltipContainer
                      width="300px"
                      padding="10px"
                      positionLeft="0"
                      positionBottom="0"
                      positionLeftDecs="40px"
                      positionBottomDecs="25px"
                    >
                      <span className="tooltip-content">
                        <ReactMarkdown
                          components={{
                            h1: ({ node, ...props }) => (
                              <h1 style={{ fontSize: '2em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h2: ({ node, ...props }) => (
                              <h2 style={{ fontSize: '1.75em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h3: ({ node, ...props }) => (
                              <h3 style={{ fontSize: '1.5em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h4: ({ node, ...props }) => (
                              <h4 style={{ fontSize: '1.25em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h5: ({ node, ...props }) => (
                              <h5 style={{ fontSize: '1em', color: 'white', margin: 0 }} {...props} />
                            ),
                            h6: ({ node, ...props }) => (
                              <h6 style={{ fontSize: '0.875em', color: 'white', margin: 0 }} {...props} />
                            ),
                            p: ({ node, ...props }) => <p style={{ color: 'white', margin: 0 }} {...props} />,
                            ul: ({ node, ...props }) => <ul style={{ color: 'white', margin: 0 }} {...props} />,
                            ol: ({ node, ...props }) => <ol style={{ color: 'white', margin: 0 }} {...props} />,
                            li: ({ node, ...props }) => <li style={{ color: 'white', margin: 0 }} {...props} />,
                            hr: ({ node, ...props }) => (
                              <hr style={{ margin: 0, border: '1px solid white' }} {...props} />
                            ),
                          }}
                        >
                          {curStep?.description?.replace(/\n/g, '  \n')}
                        </ReactMarkdown>
                      </span>
                      <Styled.IButton>i</Styled.IButton>
                    </SharedStyled.TooltipContainer>
                  ) : null}
                </Styled.Text>
                <CustomSelect
                  setValue={() => {}}
                  dropDownData={curStep?.dropDownOptions ?? []}
                  value={otherValue || ''}
                  labelName={' '}
                  stateName={`${curStep._id}.value`}
                  setFieldValue={setFieldValue}
                  error={touched[curStep._id]?.value && errors[curStep._id]?.value ? true : false}
                  disabled={values[curStep?._id]?.boolean}
                  onBlur={() => {
                    handleBlurOrCheck(curStep.name, curStep._id, values[curStep?._id]?.value, false, false, true)
                  }}
                  innerHeight="52px"
                  margin="0"
                  isSmall
                  showBorder
                  maxWidth="200px"
                  fontSize="13px"
                />
              </SharedStyled.FlexBox>
            </SharedStyled.FlexBox>
          )
        default:
          let data: any = renderLinkedTo(curStep._id)
          return (
            <SharedStyled.FlexBox alignItems="center" margin={!isParent ? '0' : ' 0'}>
              <div>
                <Checkbox
                  margin="0"
                  key={convertStrToKey(curStep.name)}
                  title={convertStrToKey(curStep.name)}
                  value={values[curStep?._id]?.boolean}
                  onChange={() => {
                    handleCheck(curStep)
                    setStepsLoading(true)
                  }}
                  disabled={isDropdownDisabled()}
                  hideTitle
                ></Checkbox>
              </div>
              <Styled.Text
                whiteSpace="pre"
                fontWeight={!getIsChildByName(curStep?.name, parntStep) ? 'normal' : 'bold'}
              >
                {curStep.name} {curStep.isRequire ? '*' : ''}
                {curStep?.description ? (
                  <SharedStyled.TooltipContainer
                    width="300px"
                    padding="10px"
                    positionLeft="0"
                    positionBottom="0"
                    positionLeftDecs="40px"
                    positionBottomDecs="25px"
                  >
                    <span className="tooltip-content">
                      <ReactMarkdown
                        components={{
                          h1: ({ node, ...props }) => (
                            <h1 style={{ fontSize: '2em', color: 'white', margin: 0 }} {...props} />
                          ),
                          h2: ({ node, ...props }) => (
                            <h2 style={{ fontSize: '1.75em', color: 'white', margin: 0 }} {...props} />
                          ),
                          h3: ({ node, ...props }) => (
                            <h3 style={{ fontSize: '1.5em', color: 'white', margin: 0 }} {...props} />
                          ),
                          h4: ({ node, ...props }) => (
                            <h4 style={{ fontSize: '1.25em', color: 'white', margin: 0 }} {...props} />
                          ),
                          h5: ({ node, ...props }) => (
                            <h5 style={{ fontSize: '1em', color: 'white', margin: 0 }} {...props} />
                          ),
                          h6: ({ node, ...props }) => (
                            <h6 style={{ fontSize: '0.875em', color: 'white', margin: 0 }} {...props} />
                          ),
                          p: ({ node, ...props }) => <p style={{ color: 'white', margin: 0 }} {...props} />,
                          ul: ({ node, ...props }) => <ul style={{ color: 'white', margin: 0 }} {...props} />,
                          ol: ({ node, ...props }) => <ol style={{ color: 'white', margin: 0 }} {...props} />,
                          li: ({ node, ...props }) => <li style={{ color: 'white', margin: 0 }} {...props} />,
                          hr: ({ node, ...props }) => (
                            <hr style={{ margin: 0, border: '1px solid white' }} {...props} />
                          ),
                        }}
                      >
                        {curStep?.description?.replace(/\n/g, '  \n')}
                      </ReactMarkdown>
                    </span>
                    <Styled.IButton>i</Styled.IButton>
                  </SharedStyled.TooltipContainer>
                ) : null}
              </Styled.Text>
              &ensp;
              {/*paste your code here */}
            </SharedStyled.FlexBox>
          )
      }
    }

    return (
      <>
        {getCurComponent()}
        <SharedStyled.FlexBox flexDirection="column" gap="0">
          {childSteps.length > 0
            ? childSteps.map((step) => (
                <SharedStyled.FlexRow
                  key={step._id}
                  justifyContent="space-between"
                  margin="0 0 0 1.5rem"
                  width="95%"
                  gap="0"
                >
                  {getValueComponent(
                    step,
                    errors,
                    touched,
                    setFieldValue,
                    values,
                    handleSubmit,
                    renderLinkedTo,
                    true,
                    false
                  )}
                </SharedStyled.FlexRow>
              ))
            : null}
        </SharedStyled.FlexBox>
      </>
    )
  }
  const renderLinkedTo = (_id: string) => {
    const data = steps.filter((v) => v.hasOwnProperty('linkedTo')).filter((v: any) => v.linkedTo === _id)
    if (data.length) {
      return data
    }
    return null
  }

  const getChildTo = (_id: string) => {
    const data = allSteps.filter((v: any) => v.parent === _id)
    if (data.length) {
      return data
    }
    return null
  }
  // 8219a22d-5deb-416f-82a9-c302136fbdd7
  const handleSchedule = async (name: string, sequence: number, id: string, currDate: string, chkDateType: string) => {
    let date1: any, date2: any
    if (sequence > 1) {
      const obj: any = checkpoints?.filter((chk) => chk.sequence === sequence - 1 && chk)
      date1 = new Date(leadData?.[obj?.[0]?.symbol])
      date2 = new Date(currDate)
    }
    try {
      if (date2 < date1 && sequence > 1) {
        notify(
          `Next step date must be after previous: ${dayjsFormat(date1, 'M/D/YY')} ${new Date(date1).toLocaleTimeString(
            'en-US',
            {
              hour: '2-digit',
              minute: 'numeric',
              hour12: false,
            }
          )}`,
          'error'
        )
        throw new Error('date error')
      }
      setLoadingDateCheckpoint(chkDateType)
      // if (curStage.orderRequired) {
      //   if (leadData?.orderId === '' || !leadData?.orderId) {
      //     notify('Need an accepted order before signing', 'error')
      //     setLoadingDateCheckpoint('')
      //   } else {
      //     if (
      //       contractOrder[0].dripEdgeColor !== '' &&
      //       contractOrder[0].flashingColor !== '' &&
      //       contractOrder[0].gutterColor !== '' &&
      //       contractOrder[0].shingleColor !== ''
      //     ) {
      //       const res = await updateLeadCheckpoint(
      //         {
      //           id: leadId!,
      //           companyId: currentCompany._id,
      //           currDate: currDate,
      //           checkpointId: id,
      //           memberId: currentMember._id,
      //         }
      //       )

      //       const resActivity = await updateActivity(
      //         {
      //           id: leadId!,
      //           memberId: currentMember._id!,
      //           body: `${name} set to ${dayjsFormat(currDate, 'M/D/YY')} ${dayjsFormat(currDate, 'h:mm A')}`,
      //           companyId: currentCompany._id,
      //           currDate: new Date().toISOString(),
      //         }
      //       )

      //       if (isSuccess(res)) {
      //         notify('Date Saved Successfully', 'success')
      //         initFetchLeadOnAction()
      //         fetchActivity()
      //         setLoadingDateCheckpoint('')
      //       }
      //     } else {
      //       notify('Order must have colors selected', 'error')
      //     }
      //   }
      // } else {
      const res = await updateLeadCheckpoint({
        id: leadId!,
        currDate: new Date(currDate),
        checkpointId: id,
        memberId: currentMember._id,
      })
      await updateActivity({
        id: leadId!,
        memberId: currentMember._id!,
        body: `${name} set to ${dayjsFormat(currDate, 'M/D/YY')} ${dayjsFormat(currDate, 'h:mm A')}`,
        currDate: new Date().toISOString(),
      })

      if (isSuccess(res)) {
        notify('Date Saved Successfully', 'success')
        initFetchLeadOnAction()
        fetchActivity()
        setLoadingDateCheckpoint('')
      } else {
        setLoadingDateCheckpoint('')
      }
      // }
    } catch (error) {
      console.log(error)
      setLoadingDateCheckpoint('')
    }
  }

  const handleCheckpointDelete = async (symbol: string, name: string) => {
    try {
      const res = await deleteLeadCheckpoint({
        id: leadId!,
        symbol: symbol,
        memberId: currentMember._id,
        name: name,
        date: new Date().toISOString(),
      })
      if (isSuccess(res)) {
        setLeadData((prev: AnyKey) => {
          const updatedData = { ...prev }
          const keyToRemove = symbol
          delete updatedData[keyToRemove]
          return updatedData
        })
        initFetchLeadOnAction()
        fetchActivity()
        notify('Date Removed Successfully', 'success')
      }
      setScheduling(-1)
    } catch (error) {
      console.log(error)
    }
  }
  console.log({ leadData })
  return loading ? (
    <>
      <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} custMarginTop={'50px'} />
    </>
  ) : (
    <Styled.AssessmentWrapper>
      <SharedStyled.ContentHeader as={'h3'} textAlign="left">
        {curStage ? curStage.name : ''}
      </SharedStyled.ContentHeader>
      <div>
        <div style={{ width: '100%' }}>
          {leadData
            ? checkpoints.map((chk: any, idx) =>
                !chk.stageDisplay?.includes(curStage._id) ? (
                  <Fragment key={chk._id}>
                    {chk.stageEditable?.includes(curStage._id) && scheduling !== idx ? (
                      <SharedStyled.FlexBox justifyContent="space-between" margin="0 0 10px 0">
                        <Styled.Text>{chk.name} Date</Styled.Text>

                        <div className="bold">
                          <Styled.Text>
                            &nbsp;
                            {leadData?.[chk?.symbol]
                              ? new Date(findObjectKeyValue(leadData, chk?.symbol)).toLocaleDateString('en-US', {
                                  month: '2-digit',
                                  day: '2-digit',
                                  year: 'numeric',
                                })
                              : ''}
                            &nbsp;
                          </Styled.Text>
                          <Styled.Text>
                            &nbsp;
                            {`@${
                              leadData?.[chk?.symbol]
                                ? new Date(findObjectKeyValue(leadData, chk?.symbol)).toLocaleTimeString('en-US', {
                                    hour: '2-digit',
                                    minute: 'numeric',
                                    hour12: false,
                                  })
                                : ''
                            }`}
                          </Styled.Text>{' '}
                          <Styled.Text
                            variant="link"
                            onClick={() => {
                              setScheduling(idx)
                              setInitSchedule({
                                ...(leadData?.[chk?.symbol]
                                  ? { [chk.symbol]: dayjsFormat(leadData[`${chk.symbol}`], 'YYYY-MM-DDTHH:mm') }
                                  : { [chk.symbol]: '' }),
                                // ? {
                                //     date: new Date(findObjectKeyValue(leadData, chk?.symbol)).toLocaleDateString(
                                //       'en-US',
                                //       {
                                //         month: '2-digit',
                                //         day: '2-digit',
                                //         year: 'numeric',
                                //       }
                                //     ),
                                //     time: new Date(findObjectKeyValue(leadData, chk?.symbol)).toLocaleTimeString(
                                //       'en-US',
                                //       {
                                //         hour: '2-digit',
                                //         minute: 'numeric',
                                //         hour12: false,
                                //       }
                                //     ),
                                //   }
                                // : {
                                //     date: '',
                                //     time: '',
                                //   }
                              })
                            }}
                          >
                            Change
                          </Styled.Text>
                        </div>
                      </SharedStyled.FlexBox>
                    ) : (
                      scheduling === idx && (
                        <SharedStyled.FlexBox margin="10px 0px" flexDirection="column">
                          <Formik
                            initialValues={initSchedule}
                            onSubmit={(values) => {}}
                            // onSubmit={(values) => handleScheduleSubmit(chk.sequence, values, chk._id)}
                            validationSchema={scheduleSchema}
                            validateOnChange={true}
                            validateOnBlur={false}
                            enableReinitialize={true}
                          >
                            {({ handleSubmit, resetForm, values, setFieldValue }) => (
                              <>
                                {console.log({ chk })}
                                <Styled.Text>{chk.name} Date</Styled.Text>
                                <SharedStyled.FlexBox gap="10px" alignItems="flex-end" margin="0 0 10px 0">
                                  <SharedStyled.TwoInputDiv>
                                    <SharedDateAndTime
                                      value={values[chk.symbol]}
                                      labelName={`${chk.name} Date/Time`}
                                      stateName={chk.symbol}
                                      setFieldValue={setFieldValue}
                                    />
                                    {/* <SharedDate value={values.date} labelName="Date" stateName="date" />
                                    <NormalInput
                                      type="time"
                                      value={values.time}
                                      onChange={(e: any) => {
                                        setFieldValue('time', e.target.value)
                                      }}
                                      labelName="Time"
                                      stateName="time"
                                    /> */}
                                  </SharedStyled.TwoInputDiv>
                                  <Button
                                    type="button"
                                    width="max-content"
                                    height="53px"
                                    onClick={() => {
                                      handleSchedule(chk.name, chk.sequence, chk._id, values[chk.symbol], `${chk._id}`)
                                      setScheduling(-1)
                                    }}
                                    disabled={values[chk.symbol] === '' ? true : false}
                                    bgColor={values[chk.symbol] === '' ? '#C1C0C5' : ''}
                                    isLoading={loadingDateCheckpoint === `${chk._id}`}
                                  >
                                    Save
                                  </Button>
                                </SharedStyled.FlexBox>
                                <SharedStyled.FlexBox gap="10px">
                                  {!(extractLocalTime(leadData[`${chk.symbol}`]) && !leadData[`${chk.symbol}`]) && (
                                    <Styled.Text
                                      variant="link"
                                      color="red"
                                      fontSize="12px"
                                      onClick={() => {
                                        const confirmed = window.confirm(
                                          'Are you sure? This is permanent and cannot be undone.'
                                        )
                                        if (confirmed) {
                                          handleCheckpointDelete(chk?.symbol, chk?.name)
                                          resetForm()
                                        }
                                      }}
                                    >
                                      Remove Date
                                    </Styled.Text>
                                  )}
                                  {leadData[convertStrToKey(chk.name + ' Date')] ? (
                                    <Styled.Text
                                      variant="link"
                                      fontSize="12px"
                                      onClick={() => {
                                        setScheduling(-1)
                                        resetForm()
                                      }}
                                    >
                                      Cancel
                                    </Styled.Text>
                                  ) : null}
                                </SharedStyled.FlexBox>
                              </>
                            )}
                          </Formik>
                        </SharedStyled.FlexBox>
                      )
                    )}
                  </Fragment>
                ) : scheduling !== idx ? (
                  <SharedStyled.FlexBox gap="24px" margin="10px 0px" key={chk._id} justifyContent="space-between">
                    <Styled.Text>{chk.name}</Styled.Text>
                    <div className="bold">
                      <Styled.Text>
                        &nbsp;
                        {leadData?.[chk?.symbol]
                          ? new Date(findObjectKeyValue(leadData, chk?.symbol)).toLocaleDateString('en-US', {
                              month: '2-digit',
                              day: '2-digit',
                              year: 'numeric',
                            })
                          : ''}
                        &nbsp;
                      </Styled.Text>

                      <Styled.Text>
                        &nbsp;
                        {`@${
                          leadData?.[chk?.symbol]
                            ? new Date(findObjectKeyValue(leadData, chk?.symbol)).toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: 'numeric',
                                hour12: false,
                              })
                            : ''
                        }`}
                      </Styled.Text>
                    </div>
                  </SharedStyled.FlexBox>
                ) : (
                  <SharedStyled.FlexBox gap="10px" margin="10px 0px" flexDirection="column">
                    <Formik
                      initialValues={initSchedule}
                      onSubmit={(values) => handleScheduleSubmit(chk.sequence, values, chk._id)}
                      validationSchema={scheduleSchema}
                      validateOnChange={true}
                      validateOnBlur={false}
                      enableReinitialize={true}
                    >
                      {({ handleSubmit, resetForm, values, setFieldValue }) => (
                        <>
                          <Styled.Text>{chk.name} Date</Styled.Text>
                          <SharedStyled.TwoInputDiv>
                            <SharedDate value={values.date} labelName="Date" stateName="date" />
                            <NormalInput
                              type="time"
                              value={values.time}
                              onChange={(e: any) => {
                                setFieldValue('time', e.target.value)
                              }}
                              labelName="Time"
                              stateName="time"
                            />
                          </SharedStyled.TwoInputDiv>
                          <SharedStyled.FlexBox gap="10px">
                            <Styled.Text
                              variant="link"
                              onClick={() => {
                                handleSubmit()
                                setScheduling(-1)
                              }}
                            >
                              Submit
                            </Styled.Text>
                          </SharedStyled.FlexBox>
                        </>
                      )}
                    </Formik>
                  </SharedStyled.FlexBox>
                )
              )
            : null}
        </div>
        <div style={{ width: '100%' }}>
          <Formik initialValues={initValues} onSubmit={(values) => {}} enableReinitialize={true}>
            {({ errors, touched, values, setFieldValue, handleSubmit }) => {
              // return stepsLoading ? (
              //   <>
              //     <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} custMarginTop={'50px'} />
              //   </>
              // ) : (
              return (
                <>
                  {steps.length ? (
                    <>
                      {steps
                        .filter((v) => !v.hasOwnProperty('linkedTo'))
                        .map((step, index: number) => (
                          <Styled.Checkpiont
                            key={index}
                            isMarginTrue={getIsChildByName(step?.name, getParentSteps(step.name, allSteps))}
                          >
                            {getValueComponent(
                              step,
                              errors,
                              touched,
                              setFieldValue,
                              values,
                              handleSubmit,
                              renderLinkedTo,
                              false,
                              true
                            )}
                            {/* {renderLinkedTo(step._id)} */}
                          </Styled.Checkpiont>
                        ))}
                    </>
                  ) : (
                    // <Styled.Checkpiont style={{ margin: '10px 0px' }}>No steps found!</Styled.Checkpiont>
                    <></>
                  )}

                  <SharedStyled.FlexBox alignItems="flex-start" gap="8px" width="100%">
                    <SharedStyled.FlexCol>
                      {/* -----------------------------------------changed logic------------------------------------- */}
                      <Formik initialValues={initialValues} onSubmit={(values) => {}} enableReinitialize={true}>
                        {({ values: valuesChild, setFieldValue }) => {
                          // const [keyNames, setKeyNames] = useState<string[]>([])
                          // const convertToCamelCase = (str: string) => {
                          //   return str?.charAt(0)?.toLowerCase() + str?.slice(1)
                          // }
                          const convertToCamelCase = (str: string) => {
                            return str
                              .replace(/\s+/g, '') // Remove spaces
                              .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match, index) => {
                                if (+match === 0) return '' // Remove leading zeros
                                return index === 0 ? match.toLowerCase() : match.toUpperCase()
                              })
                          }
                          useEffect(() => {
                            const newKeyNames = checkpoints
                              .filter((chk) => chk.stageSet?.includes(curStage._id))
                              .map((chk: any) => {
                                return chk.symbol
                              })

                            setKeyNames(newKeyNames)
                          }, [checkpoints])

                          useEffect(() => {
                            const matchedData = keyNames
                              .filter((key) => key in leadData)
                              .map((key) => ({ key, value: leadData[key] }))

                            const newInitialValues: any = {}

                            matchedData?.forEach(({ key, value }) => {
                              newInitialValues[key] = dayjsFormat(new Date(value), 'YYYY-MM-DDTHH:mm')
                              // newInitialValues[key] =
                              //   new Date(value).toLocaleDateString('en-US', {
                              //     month: '2-digit',
                              //     day: '2-digit',
                              //     year: 'numeric',
                              //   }) || ''

                              // newInitialValues[key.split('Date')[0] + 'Time'] =
                              //   new Date(value).toLocaleTimeString('en-US', {
                              //     hour: '2-digit',
                              //     minute: 'numeric',
                              //     hour12: false,
                              //   }) || ''

                              newInitialValues[key.split('Date')[0]] = false
                            })
                            setInitialValues(newInitialValues)
                            // if(matchedData){
                            //   setInitialValues(matchedData)
                            // }
                          }, [keyNames])

                          const handleCheck = (id: string, name: string) => {
                            // setStepsLoading(true)
                            // console.log(first)
                            const checked: any = valuesChild[`${id}`]
                            setFieldValue(id, !checked)
                            updateField(valuesChild, id, '', !checked, !checked, name)
                            // updateField(valuesChild, convertStrToKey(`${name}-chk`), !checked, !checked)
                            // }
                          }

                          return (
                            <>
                              {leadData &&
                                checkpoints.map((chk: any, idx: number) => {
                                  useEffect(() => {
                                    // if (chk.stageSet?.includes(curStage._id)) {
                                    if (leadData[chk.symbol] === undefined) {
                                      setFieldValue(chk.symbol, '')
                                      // setFieldValue(convertToCamelCase(`${chk.name}Time`), '')
                                      setFieldValue(`${chk._id}`, false)
                                    } else {
                                      setFieldValue(
                                        chk.symbol,
                                        dayjsFormat(leadData[`${chk.symbol}`], 'YYYY-MM-DDTHH:mm')
                                      )
                                      // setFieldValue(
                                      //   convertToCamelCase(`${chk.name}Time`),
                                      //   extractLocalTime(leadData[`${chk.symbol}`])
                                      // )
                                      setFieldValue(
                                        `${chk._id}`,
                                        leadData.stepsChecklist?.[curStage._id]?.[`${chk._id}`]?.boolean
                                      )
                                    }
                                    // }
                                  }, [leadData?.stepsChecklist?.[curStage._id]])

                                  useEffect(() => {
                                    if (chk.stageSet?.includes(curStage._id) && chk.requiredStage === curStage._id) {
                                      setDateCheckState((prevValues) => ({
                                        ...prevValues,
                                        [`${chk._id}`]: valuesChild[`${chk._id}`],
                                      }))
                                    }
                                  }, [valuesChild[`${chk._id}`]])

                                  return (
                                    chk.stageSet?.includes(curStage._id) && (
                                      <div style={{ width: '100%' }} key={chk._id}>
                                        {/* {console.log(
                                          allValuesTrue(chkDateFlag),
                                          'ujjwal',
                                          valuesChild,
                                          `${chk.name}-chk`,
                                          valuesChild[convertStrToKey(`${chk.name}-chk`)]
                                        )} */}

                                        <SharedStyled.FlexBox alignItems="center">
                                          <div>
                                            <Checkbox
                                              margin="0"
                                              key={valuesChild[`${chk._id}`]}
                                              title={`${chk.name} Date`}
                                              // name={`${chk.name}Checkbox`}
                                              value={valuesChild[`${chk._id}`]}
                                              // disabled={leadData?.[symbol] ? false : true}
                                              onChange={() => {
                                                handleCheck(chk._id, chk.name)
                                                if (chk.requiredStage === curStage._id) {
                                                  setDateCheckState((prevValues) => ({
                                                    ...prevValues,
                                                    [`${chk._id}`]: !valuesChild[`${chk._id}`],
                                                  }))
                                                }
                                              }}
                                              hideTitle
                                              disabled={
                                                extractLocalTime(leadData[`${chk.symbol}`]) &&
                                                !leadData[`${chk.symbol}`]
                                              }
                                            />
                                          </div>

                                          <Styled.Text style={{ textTransform: 'capitalize' }} whiteSpace="pre">
                                            {`${chk.name} Date`} {chk.requiredStage === curStage._id && <span>*</span>}
                                          </Styled.Text>
                                        </SharedStyled.FlexBox>
                                        {valuesChild[`${chk._id}`] ? (
                                          <>
                                            <Styled.Text fontWeight="bold">
                                              {dayjsFormat(leadData[`${chk.symbol}`], 'M/D/YY') ??
                                                valuesChild[`${chk.symbol}`]}{' '}
                                              @{extractLocalTime(leadData[`${chk.symbol}`])}
                                            </Styled.Text>
                                          </>
                                        ) : (
                                          // <Styled.Text fontWeight="bold">{` ${valuesChild.dateForSchedule} @ ${valuesChild.timeForSchedule}`}</Styled.Text>
                                          <>
                                            <SharedStyled.FlexBox gap="10px" alignItems="flex-end" margin="0 0 10px 0">
                                              {/* <Styled.Text fontWeight="bold">{chk.name}</Styled.Text> */}
                                              <SharedStyled.TwoInputDiv>
                                                <SharedDateAndTime
                                                  value={valuesChild[chk.symbol]}
                                                  labelName={`${chk.name} Date/Time`}
                                                  stateName={chk.symbol}
                                                  setFieldValue={setFieldValue}
                                                  // error={touched.needsAssessmentDate && errors.needsAssessmentDate ? true : false}
                                                />
                                                {/* <SharedDate
                                                  value={valuesChild[chk.symbol] || ''}
                                                  // value={leadData[`${chk.symbol}`] ?? valuesChild[chk.symbol]}
                                                  labelName="Date"
                                                  stateName={chk.symbol}
                                                />
                                                <NormalInput
                                                  type="time"
                                                  onChange={(e: any) => {
                                                    setFieldValue(convertToCamelCase(`${chk.name}Time`), e.target.value)
                                                  }}
                                                  value={
                                                    // extractLocalTime(leadData[`${chk.symbol}`]) ??
                                                    // valuesChild[convertToCamelCase(`${chk.name}Time`)]
                                                    // extractLocalTime(leadData[`${chk.symbol}`]) ??
                                                    valuesChild[convertToCamelCase(`${chk.name}Time`)]
                                                  }
                                                  labelName="Time"
                                                  stateName={convertToCamelCase(`${chk.name}Time`)}
                                                /> */}
                                              </SharedStyled.TwoInputDiv>

                                              {/* {leadData?.[symbol] ? null : ( */}
                                              <Button
                                                type="button"
                                                width="max-content"
                                                height="53px"
                                                disabled={
                                                  valuesChild[chk.symbol] === '' ||
                                                  valuesChild[convertToCamelCase(`${chk.name}Time`)] === ''
                                                    ? true
                                                    : false
                                                }
                                                bgColor={
                                                  valuesChild[chk.symbol] === '' ||
                                                  valuesChild[convertToCamelCase(`${chk.name}Time`)] === ''
                                                    ? '#C1C0C5'
                                                    : ''
                                                }
                                                onClick={() => {
                                                  handleSchedule(
                                                    chk.name,
                                                    chk.sequence,
                                                    chk._id,
                                                    new Date(valuesChild[chk.symbol]),
                                                    // combineDateTime(
                                                    //   new Date(
                                                    //     startOfDate(dayjsFormat(valuesChild[chk.symbol], 'YYYY-MM-DD'))
                                                    //     // valuesChild[chk.symbol]?.includes('"')
                                                    //     //   ? startOfDate(valuesChild[chk.symbol])
                                                    //     //   : startOfDate(`"${valuesChild[chk.symbol]}"`)
                                                    //   ),
                                                    //   new Date(
                                                    //     parseTimeString(
                                                    //       valuesChild[convertToCamelCase(`${chk?.name}Time`)]
                                                    //     )
                                                    //   )
                                                    // ).toISOString(),
                                                    `${chk._id}`
                                                  )
                                                }}
                                                isLoading={loadingDateCheckpoint === `${chk._id}`}
                                              >
                                                Save
                                              </Button>
                                              {/* )} */}
                                            </SharedStyled.FlexBox>
                                            {!(
                                              extractLocalTime(leadData[`${chk.symbol}`]) && !leadData[`${chk.symbol}`]
                                            ) && (
                                              <Styled.Text
                                                variant="link"
                                                color="red"
                                                fontSize="12px"
                                                onClick={() => {
                                                  const confirmed = window.confirm(
                                                    'Are you sure? This is permanent and cannot be undone.'
                                                  )
                                                  if (confirmed) {
                                                    handleCheckpointDelete(chk?.symbol, chk?.name)
                                                  }
                                                }}
                                              >
                                                Remove Date
                                              </Styled.Text>
                                            )}
                                          </>
                                        )}
                                      </div>
                                    )
                                  )
                                })}
                            </>
                          )
                        }}
                      </Formik>

                      <SharedStyled.FlexBox alignItems="flex-start" gap="8px" width="100%" marginTop="12px">
                        <Button
                          type="button"
                          className="fit"
                          onClick={handlePrevStage}
                          disabled={getCurStageId() === 0 || updateLoading}
                        >
                          Back
                        </Button>
                        <Button
                          type="button"
                          className="fit"
                          disabled={!buttonFlag || !dateCheckpointFlag || !stages[getCurStageId() + 1]?._id}
                          onClick={handleNextStage}
                        >
                          Next
                        </Button>
                      </SharedStyled.FlexBox>
                    </SharedStyled.FlexCol>
                  </SharedStyled.FlexBox>
                </>
              )

              // )
            }}
          </Formik>
        </div>
      </div>
    </Styled.AssessmentWrapper>
  )
}

export default LeadAssessmentForm
