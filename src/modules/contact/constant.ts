export const typeDropdown = ['Lead', 'Client', 'Vendor', 'Team Member', 'Partner', 'Competitor', 'Linked', 'Other']

export enum Types {
  Lead = 'lead',
  Client = 'client',
  Vendor = 'vendor',
  'Team Member' = 'teamMember',
  Partner = 'partner',
  Competitor = 'competitor',
  Linked = 'linked',
  Other = 'other',
  Spam = 'spam',
}

export const TypesDropdown = [
  { name: 'Lead', _id: 'lead' },
  { name: 'Client', _id: 'client' },
  { name: 'Vendor', _id: 'vendor' },
  { name: 'Team Member', _id: 'teamMember' },
  { name: 'Partner', _id: 'partner' },
  { name: 'Competitor', _id: 'competitor' },
  { name: 'Linked', _id: 'linked' },
  { name: 'Other', _id: 'other' },
  { name: 'Spam', _id: 'spam' },
]
