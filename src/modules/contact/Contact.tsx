import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { getReferres, getSalesPersonAndPM } from '../../logic/apis/company'
import TabBar from '../../shared/components/tabBar/TabBar'
import ActionButtons from '../../shared/components/actionButtons'
import { CustomModal } from '../../shared/customModal/CustomModal'
import FilterSvg from '../../assets/newIcons/filter.svg'
import { convertFilters, formatPhoneNumber, isSuccess, notify } from '../../shared/helpers/util'
import * as SharedStyled from '../../styles/styled'
import ReferrerModal from '../Refferer/components/referrerModal/ReferrerModal'
import { SettingsCont } from '../units/style'
import { AddCityModal } from './components/addCityModal/AddCityModal'
import { AddNewContactModal } from './components/addNewContactModal/AddNewContactModal'
import DeletedContact from './components/deletedContact/DeletedContact'
import * as Styled from './style'
import FiltersPanel from '../../shared/advancedFilter/FiltersPanel'
import { DropdownContainer } from '../../shared/dropdownWithCheckboxes/style'
import { Types, TypesDropdown } from './constant'
import { PaginatedTable } from '../../shared/table/PaginatedTable'
import { useNavigate } from 'react-router-dom'
import useDebounce from '../../shared/hooks/useDebounce'
import { ProfileCont } from '../../shared/components/profileInfo/style'
import { AvatarSvg } from '../../shared/helpers/images'
import { getContacts } from '../../logic/apis/contact'
import { convertFiltersToMongoQuery } from '../../shared/advancedFilter/constant'
import { I_SalesPerson } from '../sales/AddOpportunityModal'

interface I_Data {
  clientName: string
  status: string
  address: string
  phone: string
  email: string
}

const Contact = () => {
  const [loading, setLoading] = useState<boolean>(false)
  const [showAddCityModal, setShowAddCityModal] = useState<boolean>(false)
  const [addNewClientModal, setShowAddNewClientModal] = useState(false)
  const [refererres, setRefererres] = useState<any>([])
  const [referrerModal, setShowReferrerModal] = useState(false)
  const [referrerValue, setReferrerValue] = useState<any>([])
  const [activeTab, setActiveTab] = useState(0)
  const [showFilter, setShowFilter] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const [totalItems, setTotalItems] = useState(0)
  const [totalPages, setTotalPages] = useState(0)

  const [pageIndex, setPageIndex] = useState(0)
  const [pageSize, setpageSize] = useState(0)
  // State for search and filters
  const [searchValue, setSearchValue] = useState('')
  const [advancedFilters, setAdvancedFilters] = useState<Record<string, any[]>>({})
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [data, setData] = useState<I_Data[]>([])
  const fetchIdRef = useRef(0)
  const debouncedValue = useDebounce(searchValue, 500)
  const [salesPersonDrop, setSalesPersonDrop] = useState<I_SalesPerson[]>([])

  const loadMoreRef = useRef(null)
  // const [referrerDropdownData, setReferrerDropdownData] = useState<any>([])
  const navigate = useNavigate()
  // Handle click outside to close the filter dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowFilter(false)
      }
    }

    if (showFilter) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showFilter])

  useEffect(() => {
    initFetchReferrers()
  }, [])

  // useEffect(() => {
  //   const filterWithNameAndSymbol = refererres?.map((item: any) => item.name)
  //   // filterWithNameAndSymbol.push('--Add New--')
  //   setReferrerDropdownData(filterWithNameAndSymbol)
  // }, [refererres])

  const initFetchReferrers = async () => {
    try {
      const res = await getReferres(false, true)
      if (isSuccess(res)) {
        const { referrers } = res?.data?.data
        setRefererres(referrers)
      }
    } catch (error) {
      console.log(error)
    }
  }
  useEffect(() => {
    if (referrerValue === '--Add New--') {
      setShowReferrerModal(true)
    }
  }, [referrerValue])

  const fetchData = useCallback(
    async ({ pageSize, pageIndex, search, advancedFilters }: any) => {
      try {
        // This will get called when the table needs new data
        setData([])
        setLoading(true)
        let receivedData: any = []

        // Build query parameters from advanced filters
        const queryParams: any = {
          deleted: activeTab === 0 ? false : true,
          limit: pageSize || 10,
          skip: pageIndex + 1,
          search,
          filter: convertFilters(advancedFilters || []),
        }

        // // Add any additional filters from the advanced filter panel
        // if (Object.keys(advancedFilters).length > 0) {
        //   const mongoQuery = convertFiltersToMongoQuery(advancedFilters)
        //   console.log('Advanced filters query:', mongoQuery)

        //   // Add filters to query params
        //   // This is just an example - you'll need to adapt this to your API's requirements
        //   if (Object.entries(mongoQuery).length) {
        //     queryParams.status = mongoQuery
        //   }
        // }

        const contactResponse = await getContacts(queryParams)

        if (isSuccess(contactResponse)) {
          let statusRes = contactResponse?.data?.data?.data
          statusRes.forEach((res: any) => {
            receivedData.push({
              fullName: res?.fullName,
              firstName: res?.firstName,
              businessName: res?.businessName,
              street: res?.street,
              city: res?.city,
              state: res?.state,
              zip: res?.zip,
              phone: formatPhoneNumber(res?.phone, '') || '--',
              email: res?.email,
              contactId: res._id,
              leadSource: res?.leadSourceId?.name || '--',
              isBusiness: res?.isBusiness || false,
              isDeleted: false,
              name: res?.name,
              type: Object.entries(Types).find(([_, v]) => v === res?.type)?.[0] || '--',
            })
          })
          const pagination = contactResponse?.data?.data?.pagination || {
            totalItems: 0,
            totalPages: 0,
          }
          console.log({ receivedData })
          setData(receivedData)
          setTotalItems(pagination.totalItems)
          setTotalPages(pagination.totalPages)
        } else {
          notify(contactResponse?.data?.message, 'error')
        }

        // Give this fetch an ID
        const fetchId = ++fetchIdRef.current
      } catch (error) {
        console.error('TeamTable fetchData error', error)
      } finally {
        setLoading(false)
      }
    },
    [detailsUpdate, activeTab]
  )

  const getPositionMembers = async () => {
    try {
      const response = await getSalesPersonAndPM()
      if (isSuccess(response)) {
        setSalesPersonDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }
  useEffect(() => {
    getPositionMembers()
  }, [])

  // Fetch data when activeTab, advancedFilters, or searchValue change
  useEffect(() => {
    fetchData({ pageSize: pageSize, pageIndex: pageIndex, search: debouncedValue, advancedFilters })
  }, [advancedFilters, debouncedValue])
  console.log({ advancedFilters }, convertFilters(advancedFilters || []))
  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name', // accessor is the "key" in the data
        Cell: (props: any) => {
          return (
            <ProfileCont>
              <img src={AvatarSvg} alt="hello" />
              <SharedStyled.FlexCol gap="2px">
                <h1>
                  {props?.row?.original?.isBusiness
                    ? props?.row?.original?.fullName || props?.row?.original?.businessName
                    : props?.row?.original?.fullName}
                </h1>
                <p>{props?.row?.original?.email}</p>
              </SharedStyled.FlexCol>
            </ProfileCont>
          )
        },
      },
      {
        Header: 'Address',
        accessor: 'address',
        Cell: (props: any) => {
          return (
            <Styled.AddressWrap>
              {props?.row?.original?.street ? <p>{props?.row?.original?.street},</p> : null}

              <p>
                {props?.row?.original?.city ? `${props?.row?.original?.city},` : null} {props?.row?.original?.state}{' '}
                {props?.row?.original?.zip}
              </p>
            </Styled.AddressWrap>
          )
        },
      },
      {
        Header: 'Phone',
        accessor: 'phone',
      },
      {
        Header: 'Lead Source',
        accessor: 'leadSource',
      },
      {
        Header: 'Type',
        accessor: 'type',
      },
    ],
    []
  )
  const fields: {
    label: string
    key: string
    type: 'string' | 'dropdown' | 'date' | 'number'
    values?: any
  }[] = [
    { label: 'Address', key: 'fullAddress', type: 'string' },
    { label: 'Assigned To', key: 'salesPerson', type: 'dropdown', values: salesPersonDrop },
    { label: 'Birth Date', key: 'dateOfBirth', type: 'date' },
    { label: 'Business Name', key: 'businessName', type: 'string' },
    { label: 'City', key: 'city', type: 'string' },
    { label: 'Created', key: 'createdAt', type: 'date' },
    { label: 'Referrals', key: 'referrer', type: 'number' },
    { label: 'State', key: 'state', type: 'string' },
    { label: 'Status', key: 'type', type: 'dropdown', values: TypesDropdown },
  ]

  return (
    <SettingsCont gap="24px">
      <SharedStyled.SectionTitle>Contacts</SharedStyled.SectionTitle>

      <SharedStyled.FlexRow alignItems="flex-start">
        <SharedStyled.FlexCol gap="10px" width="100%">
          <TabBar
            onTabChange={setActiveTab}
            tabs={[
              {
                title: 'Active',
                render: () => (
                  <>
                    {/* Action buttons row - just below tabs and above table */}
                    <SharedStyled.FlexRow justifyContent="space-between" gap="12px" margin="16px 0" padding="0 16px">
                      <ActionButtons
                        onAddClick={() => setShowAddNewClientModal(true)}
                        onMessageClick={() => console.log('Message clicked')}
                        onEmailClick={() => console.log('Email clicked')}
                        onAddTagsClick={() => console.log('Add Tags clicked')}
                        onRemoveTagsClick={() => console.log('Remove Tags clicked')}
                        onDeleteClick={() => console.log('Delete clicked')}
                        onExportClick={() => console.log('Export clicked')}
                      />
                      <SharedStyled.TooltipContainer
                        width="250px"
                        positionLeft="4px"
                        positionBottom="0px"
                        positionLeftDecs="0px"
                        positionBottomDecs="40px"
                      >
                        <span className="tooltip-content">
                          Search By Name, Email (Primary + Addition), Business Name, Tags or Phone (Primary + Addition).
                          Customize here
                        </span>
                        <div>
                          <Styled.SearchInput
                            type="search"
                            placeholder="Search..."
                            value={searchValue}
                            onChange={(e) => setSearchValue(e.target.value)}
                          />
                        </div>
                      </SharedStyled.TooltipContainer>
                    </SharedStyled.FlexRow>

                    <PaginatedTable
                      columns={columns}
                      data={data || []}
                      loading={loading}
                      totalItems={totalItems}
                      pageCount={totalPages}
                      fetchData={fetchData}
                      setPageIndex={setPageIndex}
                      setpageSize={setpageSize}
                      onRowClick={(data) => {
                        navigate(`/contact/profile/${data.contactId}/${data.isDeleted}`)
                      }}
                      isLoadMoreLoading={loading}
                    />
                  </>
                ),
              },
              {
                title: 'Inactive',
                render: () => (
                  <>
                    <PaginatedTable
                      columns={columns}
                      data={data || []}
                      loading={loading}
                      totalItems={totalItems}
                      pageCount={totalPages}
                      fetchData={fetchData}
                      setPageIndex={setPageIndex}
                      setpageSize={setpageSize}
                      onRowClick={(data) => {
                        navigate(`/contact/profile/${data.contactId}/${data.isDeleted}`)
                      }}
                      isLoadMoreLoading={loading}
                    />
                  </>
                ),
                // render: () => <DeletedContact />,
              },
            ]}
            filterComponent={
              activeTab === 0 ? (
                <SharedStyled.FlexRow width="auto" gap="16px" alignItems="center">
                  {/* Search Input */}

                  {/* Filter Icon */}
                  <DropdownContainer ref={dropdownRef} marginTop={'0px'}>
                    <Styled.FilterButtonWrapper onClick={() => setShowFilter((prev) => !prev)}>
                      Filter
                      <img
                        src={FilterSvg}
                        className="filter-icon"
                        alt="filter icon"
                        style={{ width: '20px', cursor: 'pointer' }}
                      />
                      {/* Filter count badge */}
                      {Object.keys(advancedFilters).length > 0 && (
                        <div
                          style={{
                            position: 'absolute',
                            top: '-8px',
                            right: '-8px',
                            backgroundColor: '#3b82f6',
                            color: 'white',
                            borderRadius: '50%',
                            width: '20px',
                            height: '20px',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            fontSize: '12px',
                            fontWeight: 'bold',
                          }}
                        >
                          {Object.keys(advancedFilters).length}
                        </div>
                      )}
                    </Styled.FilterButtonWrapper>

                    {showFilter && (
                      <div
                        style={{
                          position: 'absolute',
                          right: 0,
                          top: '40px',
                          zIndex: 100,
                          width: '350px',
                          backgroundColor: '#fff',
                          boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                          borderRadius: '8px',
                          overflow: 'hidden',
                          maxHeight: '80vh',
                          overflowY: 'auto',
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div style={{ padding: '16px' }}>
                          <h3 style={{ marginTop: 0, marginBottom: '16px' }}>Filter by</h3>
                          <FiltersPanel
                            fields={fields}
                            width="100%"
                            background="#ffffff"
                            initialFilters={advancedFilters}
                            onResetAll={() => setShowFilter(false)}
                            onFiltersApplied={(query) => {
                              setAdvancedFilters(query)
                              // This will trigger a re-fetch of data with the new filters
                            }}
                          />
                        </div>
                      </div>
                    )}
                  </DropdownContainer>
                </SharedStyled.FlexRow>
              ) : null
            }
          />
        </SharedStyled.FlexCol>
      </SharedStyled.FlexRow>

      <CustomModal show={addNewClientModal}>
        <AddNewContactModal
          setShowAddNewClientModal={setShowAddNewClientModal}
          setDetailsUpdate={setDetailsUpdate}
          detailsUpdate={detailsUpdate}
          // setShowAddCityModal={setShowAddCityModal}
          onClose={() => {
            setShowAddNewClientModal(false)
          }}
          // referrerDropdownData={referrerDropdownData}
          refererres={refererres}
          setReferrerValue={setReferrerValue}
          setShowReferrerModal={setShowReferrerModal}
        />
      </CustomModal>
      <CustomModal show={referrerModal}>
        <ReferrerModal
          onClose={() => {
            setShowReferrerModal(false)
          }}
          onComplete={() => {
            initFetchReferrers()
          }}
        />
      </CustomModal>
      <CustomModal show={showAddCityModal}>
        <AddCityModal setShowAddCityModal={setShowAddCityModal} action="Add City" setDetailsUpdate={setDetailsUpdate} />
      </CustomModal>
    </SettingsCont>
  )
}

export default Contact
