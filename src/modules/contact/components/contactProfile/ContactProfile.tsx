import { Field, FieldArray, Form, Formik } from 'formik'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'
import AutoCompleteAddress from '../../../../shared/autoCompleteAdress/AutoCompleteAddress'
import Button from '../../../../shared/components/button/Button'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { onlyNumber, onlyText } from '../../../../shared/helpers/regex'
import {
  formatPhoneNumber,
  getDigitsFromPhone,
  getEnumValue,
  getIdFromName,
  getKeysFromObjects,
  getNameFrom_Id,
  getValueByKeyAndMatch,
  isSuccess,
  notify,
  splitFullName,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { SharedPhone } from '../../../../shared/sharedPhone/SharedPhone'
import { Table } from '../../../../shared/table/Table'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import { AddressWrap, IntendWidth } from '../../style'
import { AddCityModal } from '../addCityModal/AddCityModal'
import * as Styled from './style'
import { getCampaigns, getLeadSources } from '../../../../logic/apis/leadSource'
import Toggle from '../../../../shared/toggle/Toggle'
import { getReferres } from '../../../../logic/apis/company'
import '../../../../shared/helpers/yupExtension'
import { I_Contacts, mergeSourceAndCampaignNames } from '../addNewContactModal/AddNewContactModal'
import Contacts from '../addNewContactModal/components/ContactCard'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import {
  getFormattedLeadSrcData,
  getLeadSrcDropData,
  getLeadSrcDropdownId,
  getLeadSrcDropdownName,
} from '../../../leadSource/LeadSource'
import useFetch from '../../../../logic/apis/useFetch'
import AutoCompleteIndentation from '../../../../shared/autoCompleteIndentation/AutoCompleteIndentation'
import Tracking from '../Tracking'
import Actions from '../Actions'
import {
  addLinkedContact,
  deleteContact,
  getContactById,
  getContactOpportunities,
  getContacts,
  getLinkedContact,
  getSearchedContact,
  migrateContact,
  permanentDeleteContact,
  removeLinkedContact,
  restoreContact,
  updateContact,
} from '../../../../logic/apis/contact'
import { getProjectTypes } from '../../../../logic/apis/projects'
import { SharedDate } from '../../../../shared/date/SharedDate'
import SearchableDropdown from '../../../../shared/searchableDropdown/SearchableDropdown'
import ContactCard from '../addNewContactModal/components/ContactCard'
import { AddRelationshipContact } from '../addRelationshipContactModal/AddRelationshipContact'
import { SLoader } from '../../../../shared/components/loader/Loader'
import { LoadScript } from '@react-google-maps/api'
import { getConfig } from '../../../../config'
import { SharedDateAndTime } from '../../../../shared/date/SharedDateAndTime'
import { Types } from '../../constant'
import ContactCardToBeAdded from '../addNewContactModal/components/ContactCardToBeAdded'

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  businessName: string
  firstName: string
  lastName: string
  city: string
  street: string
  state: string
  zip: string
  phone: string
  email: string
  tags: string[]
  contacts: I_Contacts[]
  tracking: any
  leadSourceName: string
  referredBy: string
  invalidLeadNote: string
  lostNote: string
  notes: string
  type: string
  isBusiness: boolean
  // businessName?: string
  fullName?: string
  dateReceived?: string
  workType?: string
  firstPurchase?: string
  invalidLeadReason?: string
  lostReason?: string
  dateOfBirth?: string
}

interface I_Data {
  name: string
  project: string
  price: string
  workType: string
  property: string
  value: string
}

const ContactProfile = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    businessName: '',
    firstName: '',
    lastName: '',
    city: '',
    street: '',
    state: '',
    zip: '',
    phone: '',
    contacts: [],
    tags: [],
    dateReceived: '',
    type: '',
    workType: '',
    firstPurchase: '',
    invalidLeadReason: '',
    invalidLeadNote: '',
    lostNote: '',
    lostReason: '',
    dateOfBirth: '',
    tracking: {},
    email: '',
    leadSourceName: '',
    referredBy: '',
    notes: '',
    isBusiness: false,
    // businessName: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false)
  const [permanentdeleteLoading, setPermanentDeleteLoading] = useState<boolean>(false)
  const [unDeleteLoading, setUnDeleteLoading] = useState<boolean>(false)
  // const [lat, setLat] = useState('')
  // const [long, setLong] = useState('')
  const [shimmerLoading, setShimmerLoading] = useState<boolean>(true)
  const [refererres, setRefererres] = useState<any>([])
  const [toggleHeading, setToggleHeading] = useState<{ [key: string]: boolean }>({})
  const [showAddCityModal, setShowAddCityModal] = useState<boolean>(false)
  const [dataUpdate, setDataUpdate] = useState<boolean>(false)
  const [showOpp, setShowOpp] = useState<boolean>(false)
  const [showEditAddress, setShowEditAddress] = useState(false)
  const [leadsrcDrop, setLeadsrcDrop] = useState([])
  const [leadSrcData, setLeadSrcData] = useState([])
  const [showAddContact, setShowAddContact] = useState<boolean>(false)
  const [addContact, setAddContact] = useState<boolean>(false)
  const [contactIndex, setContactIndex] = useState(-1)

  const [opportunityData, setOpportunityData] = useState([])
  // const [clientFirstName, setClientFirstName] = useState('')
  // const [duration, setDuration] = useState(0)
  // const [distance, setDistance] = useState(0)
  const [mergeModal, setMergeModal] = useState(false)
  const [matchingClients, setMatchingClients] = useState<any[]>([])
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null)
  const [isBusiness, setIsBusiness] = useState(initialValues?.isBusiness)
  const [pendingClientData, setPendingClientData] = useState<InitialValues | null>(null)
  const [selectedLeadSourceObject, setSelectedLeadSourceObject] = useState<any>()
  const [relationshipContact, setAddRelationshipContact] = useState<any>({})

  const { contactId, isDeleted } = useParams()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, companySettingForAll } = globalSelector.company

  useEffect(() => {
    setIsBusiness(initialValues?.isBusiness)
  }, [initialValues?.isBusiness])

  useEffect(() => {
    if (leadSrcData?.length) {
      const data = getLeadSrcDropData(leadSrcData)
      setLeadsrcDrop(data)
    }
  }, [leadSrcData?.length])
  /**
   * ClientProfileSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const ClientProfileSchema = Yup.object().shape({
    // firstName: Yup.string().when('businessName', (value, schema) => {
    //   if (value && isBusiness) return schema.optional()
    //   return schema.min(1, 'Too Short!').max(50, 'Too Long!').required('Required').matches(onlyText, 'Enter Valid Name')
    // }),
    firstName: Yup.string().required('Required'),
    lastName: Yup.string().min(1, 'Too Short!').max(50, 'Too Long!').matches(onlyText, 'Enter Valid Name'),
    leadSourceName: Yup.string(),
    notes: Yup.string(),
    street: Yup.string(),
    city: Yup.string(),
    state: Yup.string(),
    zip: Yup.string().matches(onlyNumber, 'Must be a number'),
    phone: Yup.string().required(),
    email: Yup.string().trimEmail().email('Invalid email'),
    // businessName: Yup.string()
    //   .min(2, 'Too Short!')
    //   .max(50, 'Too Long!')
    //   .matches(onlyText, 'Enter Valid Name')
    //   .required('Required'),
  })

  const [tableLoading, setTableLoading] = useState(false)
  const navigate = useNavigate()

  const [projectTypesDrop, setProjectTypesDrop] = useState<any>([])
  const [projectTypes, setProjectTypes] = useState<any>([])
  const [linkedContactData, setLinkedContactData] = useState<any>([])
  const [linkedContactLoading, setLinkedContactLoading] = useState(true)

  useEffect(() => {
    if (contactId) {
      initFetch()
      fetchLinkedContact()
    }
  }, [contactId])

  const fetchLinkedContact = async () => {
    setLinkedContactLoading(true)
    try {
      const res = await getLinkedContact(contactId!)
      if (isSuccess(res)) {
        setLinkedContactData(res?.data?.data?.linkedContacts)
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setLinkedContactLoading(false)
    }
  }

  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name }: { _id: string; name: string }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
        }))
        setProjectTypesDrop([
          ...object,
          {
            name: 'Unknown',
            id: 'unknown',
            value: 'unknown',
            label: 'unknown',
          },
        ])
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const fetchSearchContact = async (query: string) => {
    try {
      const res = await getSearchedContact(query, {
        fields: { fullName: 1, firstName: 1, lastName: 1, email: 1, phone: 1, street: 1, city: 1, state: 1, zip: 1 },
      })
      if (isSuccess(res)) {
        return res // ✅ return the successful result
      } else {
        return null // or [] or undefined depending on expected return shape
      }
    } catch (error) {
      console.error({ error })
      return null // or [] or undefined
    }
  }

  const getOpportunities = async () => {
    setTableLoading(true)
    try {
      const res = await getContactOpportunities(contactId!)
      setOpportunityData(res?.data?.data?.clientOpps)
    } catch (error) {
      console.error('Error=====>', error)
    } finally {
      setTableLoading(false)
    }
  }

  useEffect(() => {
    initFetchReferrers()
  }, [])

  const initFetchReferrers = async () => {
    try {
      const res = await getReferres(false, true)
      if (isSuccess(res)) {
        const { referrers } = res?.data?.data
        setRefererres(referrers)
      }
    } catch (error) {
      console.log(error)
    }
  }

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: (row: any) => `${row?.contactId?.fullName}`,
      },
      {
        Header: 'PO#',
        accessor: (row: any) => `${row?.PO}-${row?.num}`,
      },
      {
        Header: 'Price',
        accessor: (row: any) => `${row?.price ?? '----'}`,
      },
      {
        Header: 'Work Type',
        accessor: (row: any) => row?.oppType?.name,
      },
      {
        Header: 'Property',
        accessor: 'street',
        Cell: (props: any) => {
          return (
            <AddressWrap>
              {props?.row?.original?.street ? <p>{props?.row?.original?.street},</p> : null}
              {props?.row?.original?.city ? (
                <p>
                  {props?.row?.original?.city}, {props?.row?.original?.state} {props?.row?.original?.zip}
                </p>
              ) : null}
            </AddressWrap>
          )
        },
      },
      {
        Header: 'Value',
        accessor: (row: any) => `${row?.value ?? '----'}`,
      },
    ],
    []
  )

  useEffect(() => {
    window?.scrollTo(0, 0)
    getOpportunities()
  }, [])

  const deleteClientFunc = async () => {
    try {
      setDeleteLoading(true)

      let response = await deleteContact(contactId!)
      if (response?.data?.statusCode === 204) {
        notify('Contact Profile Deleted Successfully', 'success')
        setDeleteLoading(false)
        navigate(`/contact`)
      } else {
        setDeleteLoading(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('deleteClientFunc error', error)
    }
  }

  const permanentDeleteClientFunc = async () => {
    try {
      setPermanentDeleteLoading(true)

      let response = await permanentDeleteContact(contactId!)
      if (response?.data?.statusCode === 204) {
        notify('Contact Profile Deleted Permanently', 'success')
        setPermanentDeleteLoading(false)
        navigate(`/contact`)
      } else {
        setPermanentDeleteLoading(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('deleteClientFunc error', error)
    }
  }

  const restoreClientFunc = async () => {
    try {
      setUnDeleteLoading(true)

      let response = await restoreContact(contactId!)
      if (response?.data?.statusCode === 200) {
        notify('Contact Profile Restored Successfully', 'success')
        setUnDeleteLoading(false)
        navigate(`/contact`)
      } else {
        setUnDeleteLoading(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('deleteClientFunc error', error)
    }
  }

  const getClientDetails = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let clId: any = contactId
      const clientResponse = await getContactById(clId)

      if (clientResponse?.data?.statusCode === 200) {
        let statusRes = clientResponse?.data?.data?.contact || {}
        let dataObj = {
          ...statusRes,
          // firstName: statusRes?.isBusiness ? statusRes?.fullName : statusRes?.firstName,
        }
        delete dataObj.updatedAt
        delete dataObj.createdBy
        delete dataObj.createdAt
        delete dataObj.__v
        delete dataObj.companyId
        delete dataObj.deleted
        delete dataObj._id

        setInitialValues({
          ...dataObj,
          referredBy: getNameFrom_Id(dataObj?.referredBy, refererres),
          tracking: dataObj?.tracking || {},
          // contacts: linkedContactData?.linkedContacts || [],
          type: Object.entries(Types).find(([_, v]) => v === dataObj?.type)?.[0] || '',
          workType: getValueByKeyAndMatch('name', dataObj?.workType, 'id', projectTypesDrop),
          // leadSourceName: getNameFrom_Id(dataObj?.leadSource, leadsrcDrop),
          leadSourceName: getLeadSrcDropdownName(dataObj?.campaignId || dataObj?.leadSourceId, leadSrcData)?.sourceName,
        })
      } else {
        notify(clientResponse?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getDetails error', error)
    } finally {
      setShimmerLoading(false)
    }
  }

  useEffect(() => {
    if (refererres.length && leadsrcDrop.length && leadSrcData.length && projectTypesDrop?.length) {
      getClientDetails()
    }
  }, [dataUpdate, refererres, leadsrcDrop, leadSrcData, projectTypesDrop])

  const getLeadSrcData = async () => {
    try {
      const leadSourceResponse = await getLeadSources({ limit: '100', active: true }, false)
      if (leadSourceResponse?.data?.statusCode === 200) {
        let statusRes = leadSourceResponse?.data?.data?.leadSource
        setLeadSrcData(statusRes)
      } else notify(leadSourceResponse?.data?.message, 'error')
    } catch (err) {
      // notify('Failed to fetch lead sources!', 'error')
      console.log('Lead source fetch error', err)
    }
  }

  useEffect(() => {
    getLeadSrcData()
  }, [])
  console.log({ projectTypesDrop })
  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      const result = getLeadSrcDropdownId(submittedValues?.leadSourceName || '', leadSrcData)
      const workTypeId = getValueByKeyAndMatch('id', submittedValues?.workType, 'name', projectTypesDrop)
      // const leadsourceId = getIdFromName(submittedValues?.leadSourceName, leadsrcDrop)
      const leadsourceId = result?.leadSourceId
      const campaignId = result?.campaignId || null
      const campaignName = result?.campaignName || null
      const referredById =
        leadsourceId === '8c115e3c-2f31-4418-b0d6-fe1c0f5bbbc6'
          ? getIdFromName(submittedValues?.referredBy, refererres)
          : null

      setPendingClientData(submittedValues)
      setLoading(true)
      let { email, contacts, leadSourceName, linkedContacts, lostNote, invalidLeadNote, ...restSubmitted }: any =
        submittedValues

      let clId: any = contactId
      let phone = getDigitsFromPhone(submittedValues?.phone)
      let dataObj: any = {
        ...restSubmitted,
        dateOfBirth: submittedValues.dateOfBirth || undefined,
        dateReceived: submittedValues.dateReceived || undefined,
        firstPurchase: submittedValues.firstPurchase || undefined,
        isBusiness,
        workType: workTypeId || undefined,
        type: Types[submittedValues.type as keyof typeof Types] || undefined,
        businessName: isBusiness ? submittedValues?.businessName?.trim() : undefined,
        firstName: submittedValues?.firstName?.trim(),
        lastName: submittedValues?.lastName?.trim(),
        fullName: `${submittedValues?.firstName?.trim() || ''} ${submittedValues?.lastName?.trim() || ''}`?.trim(),
        phone,
        lostNote: lostNote || undefined,
        invalidLeadNote: invalidLeadNote || undefined,
        leadSourceId: leadsourceId,
        createdBy: currentMember._id,
        referredBy: referredById,
        campaignId,
        campaignName,
      }

      console.log({ submittedValues }, dataObj)

      const firstName = submittedValues?.firstName?.trim()
      const lastName = submittedValues?.lastName?.trim()
      const fullName = `${firstName || ''} ${lastName || ''}`.trim()

      // Always fetch contacts by fullName
      const existingClients = await getSearchedContact(fullName, {
        fields: { firstName: 1, lastName: 1, isBusiness: 1 },
      })

      if (isSuccess(existingClients)) {
        const clientList = existingClients?.data?.data?.contact || []

        const duplicates = clientList.filter((client: any) => {
          const isSameClient = client._id !== clId

          if (isBusiness) {
            // Only check if isBusiness is true
            return client.isBusiness && isSameClient
          } else {
            // For normal (non-business) check by name
            const matchesFirst = client?.firstName?.trim()?.toLowerCase() === firstName?.toLowerCase()
            const matchesLast = client?.lastName?.trim()?.toLowerCase() === lastName?.toLowerCase()
            return matchesFirst && matchesLast && isSameClient
          }
        })

        if (duplicates.length > 0) {
          setMatchingClients(duplicates)
          setMergeModal(true)
          setLoading(false)
          return
        }
      }

      // const linkedIds = dataObj?.contacts?.map((v) => v._id || '0e5bb272-df5a-4272-858e-7422e894ef4d')
      // console.log({ linkedIds })

      let response = await updateContact(dataObj, clId)
      // if (isSuccess(response)) {
      // addLinkedContact(linkedIds, clId)
      // }
      if (isSuccess(response)) {
        notify('Contact Profile Edited Successfully', 'success')
        setLoading(false)
      } else {
        setLoading(false)
      }
    } catch (error) {
      console.error('Contact Profile handleSubmit', error)
      setLoading(false)
    }
  }

  const handleContinueUpdate = async () => {
    setMergeModal(false) // Close the modal
    if (!pendingClientData) return // Ensure we have data to update

    setLoading(true)
    let phone = getDigitsFromPhone(pendingClientData?.phone)
    // let leadsourceId = getIdFromName(pendingClientData?.leadSourceName, leadsrcDrop)
    const result = getLeadSrcDropdownId(pendingClientData?.leadSourceName, leadSrcData)

    const leadsourceId = result?.leadSourceId
    const campaignId = result?.campaignId || null
    const workTypeId = getValueByKeyAndMatch('id', pendingClientData?.workType, 'name', projectTypesDrop)

    let referredById =
      leadsourceId === '8c115e3c-2f31-4418-b0d6-fe1c0f5bbbc6'
        ? getIdFromName(pendingClientData?.referredBy, refererres)
        : undefined
    let dataObj = {
      ...pendingClientData,
      isBusiness,
      businessName: isBusiness ? pendingClientData?.businessName?.trim() : undefined,
      firstName: pendingClientData?.firstName?.trim(),
      lastName: pendingClientData?.lastName?.trim(),
      phone,
      campaignId,
      type: Types[pendingClientData.type as keyof typeof Types] || undefined,
      workType: workTypeId || undefined,
      leadSourceId: leadsourceId,
      createdBy: currentMember._id,
      clientId: contactId,
      referredBy: referredById,
    }

    try {
      let response = await updateContact(dataObj, contactId)
      if (response?.data?.statusCode === 200) {
        notify('Contact Profile Edited Successfully', 'success')
      }
    } catch (error) {
      console.error('Error updating client', error)
    } finally {
      setLoading(false)
    }
  }

  const handleMergeConfirm = async () => {
    try {
      const res = await migrateContact(contactId!, selectedClientId!)
      if (isSuccess(res)) {
        notify('Contact Profile Merged Successfully', 'success')
        navigate(`/contact`)
      }
    } catch (error) {
    } finally {
      setMergeModal(false)
      setLoading(false)
    }
  }

  const toggleCount = (type: string) => {
    setToggleHeading((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  const handleRemove = async (id: string) => {
    try {
      const res = await removeLinkedContact(contactId!, id)
      if (isSuccess(res)) {
        notify('Linked Contact Removed Successfully', 'success')
        fetchLinkedContact()
      }
    } catch (error) {
      console.log({ error })
    }
  }

  return (
    <>
      {shimmerLoading ? (
        <>
          <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
        </>
      ) : (
        <Formik
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={ClientProfileSchema}
          enableReinitialize={true}
          validateOnChange={true}
          validateOnBlur={false}
        >
          {({ errors, touched, values, setFieldValue, handleChange }) => {
            // useEffect(() => {
            //   if (values?.fullName) {
            //     const { firstName, lastName } = splitFullName(values?.businessName || values?.fullName)
            //     setFieldValue('firstName', firstName)
            //     setFieldValue('lastName', lastName)
            //     isBusiness && setFieldValue('businessName', `${firstName} ${lastName}`)
            //     // isBusiness && setClientFirstName(firstName)
            //   }
            // }, [values?.fullName, isBusiness])

            useEffect(() => {
              if (isBusiness) {
                if (values?.firstName !== '' || values?.lastName !== '')
                  setFieldValue('firstName', `${values?.firstName} ${values?.lastName || ''}`)
                setFieldValue('lastName', '')
              } else {
                const { firstName, lastName } = values?.lastName
                  ? splitFullName(`${values?.firstName} ${values?.lastName}`)
                  : splitFullName(values?.firstName)
                setFieldValue('firstName', firstName)
                setFieldValue('lastName', lastName)
              }
            }, [isBusiness])

            useEffect(() => {
              if (values.leadSourceName !== '') {
                const result = getLeadSrcDropdownId(values.leadSourceName || '', leadSrcData)
                setSelectedLeadSourceObject(result.leadSourceObject)
              }
            }, [values.leadSourceName])

            const handleRemoveToBeAdded = (index: number, contacts: any[]) => {
              const updated = [...contacts]
              updated.splice(index, 1)
              setFieldValue('contacts', updated || [])
            }

            // console.log({ errors, values }, selectedLeadSourceObject)
            return (
              <>
                <Styled.ClientProfileContainer>
                  <LoadScript
                    googleMapsApiKey={getConfig().googleAddressApiKey}
                    //  @ts-ignore
                    libraries={['places']}
                    loadingElement={<SLoader height={35} width={100} isPercent />}
                  >
                    <Form className="form">
                      <SharedStyled.Content width="100%" gap="10px" disableBoxShadow={true} noPadding={true}>
                        <SharedStyled.SectionTitle>Contact Profile</SharedStyled.SectionTitle>

                        <Toggle
                          title="Business"
                          isToggled={isBusiness}
                          onToggle={() => {
                            setIsBusiness((prev) => !prev)
                          }}
                        />
                        {isBusiness ? (
                          <InputWithValidation
                            labelName="Business Name*"
                            stateName="businessName"
                            error={touched.businessName && errors.businessName ? true : false}
                            twoInput={true}
                          />
                        ) : null}
                        <SharedStyled.TwoInputDiv>
                          <InputWithValidation
                            labelName="Primary First Name*"
                            stateName="firstName"
                            error={touched.firstName && errors.firstName ? true : false}
                            twoInput={true}
                          />
                          <InputWithValidation
                            labelName="Primary Last Name"
                            stateName="lastName"
                            error={touched.lastName && errors.lastName ? true : false}
                            twoInput={true}
                          />
                        </SharedStyled.TwoInputDiv>

                        <SharedStyled.TwoInputDiv>
                          <SharedPhone
                            labelName="Primary Phone*"
                            stateName="phone"
                            onChange={handleChange('phone')}
                            value={values.phone}
                            error={touched.phone && errors.phone ? true : false}
                          />

                          <InputWithValidation
                            labelName="Primary Email"
                            stateName="email"
                            error={touched.email && errors.email ? true : false}
                          />
                        </SharedStyled.TwoInputDiv>

                        <SharedStyled.FlexCol gap="6px">
                          <br />
                          <div>
                            {' '}
                            <SharedStyled.Text
                              color={`${colors.darkGrey}`}
                              textAlign="left"
                              fontWeight="bold"
                              fontSize="16px"
                            >
                              Linked Contacts{' '}
                            </SharedStyled.Text>
                            <Button
                              padding="4px 10px"
                              width="max-content"
                              type="button"
                              onClick={() => {
                                setShowAddContact((pre) => !pre)
                              }}
                            >
                              +
                            </Button>
                          </div>
                          {showAddContact ? (
                            <IntendWidth>
                              <SharedStyled.FlexCol width="100%" gap="10px" alignItems="center">
                                <SearchableDropdown
                                  label="Search Contacts"
                                  placeholder="Type to search"
                                  searchFunction={fetchSearchContact}
                                  displayKey={'fullName'}
                                  onSelect={(item: any) => {
                                    setAddContact(true)
                                    setAddRelationshipContact(item)
                                    setContactIndex(values?.contacts?.length || 0)
                                  }}
                                  resultExtractor={(res) => res?.data?.data?.contacts || []}
                                  onAddClick={() => {
                                    setAddContact(true)
                                    setContactIndex(-1)
                                  }}
                                  addNewText="Add New"
                                  showAddOption
                                />

                                {addContact && (
                                  <AddRelationshipContact
                                    onClose={() => {
                                      setAddContact(false)
                                      setAddRelationshipContact({})
                                    }}
                                    companySettingForAll={companySettingForAll}
                                    contacts={values.contacts || []}
                                    setFieldValue={setFieldValue}
                                    index={contactIndex}
                                    relationshipContact={relationshipContact}
                                    contactId={contactId}
                                    fetchLinkedContact={fetchLinkedContact}
                                  />
                                )}
                              </SharedStyled.FlexCol>
                              {/* {values?.contacts?.map((v, index: number) => (
                                <ContactCardToBeAdded
                                  contact={v}
                                  key={index}
                                  onRemove={() => handleRemoveToBeAdded(index, values?.contacts)}
                                  onClick={() => {
                                    setContactIndex(index)
                                    setAddContact(true)
                                  }}
                                />
                              ))} */}
                            </IntendWidth>
                          ) : null}
                          <IntendWidth>
                            {linkedContactData?.map((v: any, index: number) => (
                              <ContactCard
                                contact={v.id || {}}
                                isLoading={linkedContactLoading}
                                key={index}
                                onRemove={() => handleRemove(v?.id?._id ?? '')}
                                onClick={() => {
                                  navigate(`/contact/profile/${v?.id?._id ?? ''}/${isDeleted}`)
                                  // setContactIndex(index)
                                  // setAddContact(true)
                                }}
                              />
                            ))}
                          </IntendWidth>
                        </SharedStyled.FlexCol>
                        <CustomSelect
                          value={values.type}
                          labelName="Type"
                          stateName="type"
                          dropDownData={Object.keys(Types)}
                          setFieldValue={setFieldValue}
                          setValue={() => {}}
                          margin="10px 0 0 0"
                          error={touched.type && errors.type ? true : false}
                        />
                        {leadSrcData?.length ? (
                          <AutoCompleteIndentation
                            labelName="Lead Source"
                            stateName={`leadSourceName`}
                            isLeadSource
                            dropdownHeight="180px"
                            error={touched.leadSourceName && errors.leadSourceName ? true : false}
                            borderRadius="0px"
                            setFieldValue={setFieldValue}
                            options={mergeSourceAndCampaignNames(leadSrcData)}
                            formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                            value={values.leadSourceName!}
                            setValueOnClick={(val: string) => {
                              setFieldValue('leadSourceName', val)
                            }}
                            className="material-autocomplete"
                            isIndentation={true}
                          />
                        ) : null}

                        {selectedLeadSourceObject?.code === 'referral' && (
                          <SharedStyled.FlexBox width="100%" justifyContent="end">
                            <CustomSelect
                              labelName="Referrer"
                              stateName="referredBy"
                              error={touched.referredBy && errors.referredBy ? true : false}
                              setFieldValue={setFieldValue}
                              setValue={() => {}}
                              value={values.referredBy}
                              dropDownData={getKeysFromObjects(refererres, 'name')}
                              innerHeight="52px"
                              className="top"
                              maxWidth="95%"
                            />
                          </SharedStyled.FlexBox>
                        )}

                        <SharedStyled.FlexRow justifyContent="flex-start" alignItems="flex-start">
                          <div style={{ width: '100%' }}>
                            <SharedStyled.FlexRow>
                              <b>Address - </b>
                              <p
                                className="link"
                                onClick={() => {
                                  setShowEditAddress(!showEditAddress)
                                }}
                              >
                                {showEditAddress ? 'Confirm' : 'Edit'}
                              </p>
                            </SharedStyled.FlexRow>

                            {showEditAddress ? (
                              <AutoCompleteAddress
                                setFieldValue={setFieldValue}
                                street={'street'}
                                city={'city'}
                                state={'state'}
                                zip={'zip'}
                                sourceAddress={companySettingForAll?.address}
                                companyLatLong={companySettingForAll}
                                noLoadScript={true}
                              />
                            ) : (
                              <AddressWrap className="font">
                                <p>{values?.street}</p>
                                <p>
                                  {values?.city}, {values.state} {values.zip}
                                </p>
                              </AddressWrap>
                            )}
                          </div>
                        </SharedStyled.FlexRow>

                        {/* <Styled.LabelDiv textAlign="left" width="100%" marginTop="8px">
                      Notes
                    </Styled.LabelDiv> */}
                        <Styled.TextArea
                          component="textarea"
                          placeholder="Notes"
                          as={Field}
                          name="notes"
                          margintop="8px"
                          height="52px"
                        ></Styled.TextArea>

                        <SharedDateAndTime
                          value={values?.dateReceived || ''}
                          labelName="Date Received"
                          stateName="dateReceived"
                          error={!!(touched?.dateReceived && errors?.dateReceived)}
                          setFieldValue={setFieldValue}
                        />
                        <CustomSelect
                          labelName="Work Type"
                          stateName="workType"
                          value={values?.workType || ''}
                          error={!!(touched?.workType && errors?.workType)}
                          setFieldValue={setFieldValue}
                          setValue={() => {}}
                          dropDownData={[...projectTypesDrop.map(({ name }: { name: string }) => name)]}
                          innerHeight="52px"
                          margin="10px 0 0 0"
                        />

                        <SharedDate
                          value={values?.firstPurchase || ''}
                          labelName="First Purchase"
                          stateName="firstPurchase"
                          error={!!(touched?.firstPurchase && errors?.firstPurchase)}
                          setFieldValue={setFieldValue}
                        />

                        <CustomSelect
                          labelName="Invalid Lead Reason"
                          stateName="invalidLeadReason"
                          value={values?.invalidLeadReason || ''}
                          error={!!(touched?.invalidLeadReason && errors?.invalidLeadReason)}
                          setFieldValue={setFieldValue}
                          setValue={() => {}}
                          dropDownData={[
                            '',
                            'Service Not Provided',
                            'Outside Service Area',
                            'Purchase Material Only',
                            'Looking For Work',
                            'Unreachable',
                            'Spam',
                            'Other (Describe in notes)',
                          ]}
                          innerHeight="52px"
                          margin="10px 0 0 0"
                        />
                        <IntendWidth>
                          {values?.invalidLeadReason === 'Other (Describe in notes)' && (
                            <InputWithValidation
                              labelName="Invalid Lead Notes"
                              stateName="invalidLeadNote"
                              error={touched.invalidLeadNote && errors.invalidLeadNote ? true : false}
                            />
                          )}
                        </IntendWidth>

                        <CustomSelect
                          labelName="Lost Reason"
                          stateName="lostReason"
                          value={values?.lostReason || ''}
                          error={!!(touched?.lostReason && errors?.lostReason)}
                          setFieldValue={setFieldValue}
                          setValue={() => {}}
                          dropDownData={[
                            '',
                            'Too Expensive',
                            'Price Shopping',
                            'Went With Other Provider',
                            'Discuss With Partner',
                            'Wants to Wait',
                            'Ghosted',
                            'Other (Describe in notes)',
                          ]}
                          innerHeight="52px"
                          margin="10px 0 0 0"
                        />

                        <IntendWidth>
                          {values?.lostReason === 'Other (Describe in notes)' && (
                            <InputWithValidation
                              labelName="Lost Reason Notes"
                              stateName="lostNote"
                              error={touched.lostNote && errors.lostNote ? true : false}
                            />
                          )}
                        </IntendWidth>

                        <SharedDate
                          value={values?.dateOfBirth || ''}
                          labelName="Date of Birth"
                          stateName="dateOfBirth"
                          error={!!(touched?.dateOfBirth && errors?.dateOfBirth)}
                          setFieldValue={setFieldValue}
                        />

                        <SharedStyled.Text variant="link" fontWeight="700" fontSize="18px" margin="10px auto 0 0">
                          {' '}
                          <span onClick={() => toggleCount('Actions')}>
                            {!toggleHeading[`Actions`] ? <>&#9654;</> : <>&#9660;</>}
                            &nbsp; Actions
                          </span>
                        </SharedStyled.Text>
                        {toggleHeading['Actions'] ? (
                          <Actions values={values} errors={errors} setFieldValue={setFieldValue} touched={touched} />
                        ) : null}

                        <SharedStyled.Text variant="link" fontWeight="700" fontSize="18px" margin="10px auto 0 0">
                          {' '}
                          <span onClick={() => toggleCount('Tracking')}>
                            {!toggleHeading[`Tracking`] ? <>&#9654;</> : <>&#9660;</>}
                            &nbsp; Tracking
                          </span>
                        </SharedStyled.Text>

                        {toggleHeading['Tracking'] ? <Tracking tracking={values.tracking} /> : null}

                        <SharedStyled.ButtonContainer marginTop="20px" justifyContent="flex-start">
                          <Button type="submit" isLoading={loading} width="max-content">
                            Save Changes
                          </Button>
                          {isDeleted === 'false' && !opportunityData?.length && (
                            <Button
                              type="button"
                              className="delete"
                              width="max-content"
                              isLoading={deleteLoading}
                              onClick={() => deleteClientFunc()}
                            >
                              Make Inactive
                            </Button>
                          )}
                          {isDeleted === 'true' && (
                            <>
                              <Button
                                type="button"
                                width="max-content"
                                bgColor={colors.green}
                                onClick={() => restoreClientFunc()}
                                isLoading={unDeleteLoading}
                              >
                                Un-Delete Contact
                              </Button>
                              <Button
                                type="button"
                                width="max-content"
                                className="delete"
                                onClick={() => permanentDeleteClientFunc()}
                                isLoading={permanentdeleteLoading}
                              >
                                Permanently Delete Contact
                              </Button>
                            </>
                          )}
                        </SharedStyled.ButtonContainer>
                      </SharedStyled.Content>
                    </Form>
                  </LoadScript>
                </Styled.ClientProfileContainer>

                <Styled.OpportunityContainer marginTop="50px" width="100%">
                  <SharedStyled.FlexBox width="100%" alignItems="flex-start" gap="5px">
                    <SharedStyled.SectionTitle>Opportunities</SharedStyled.SectionTitle>
                  </SharedStyled.FlexBox>
                  <Table
                    columns={columns}
                    data={opportunityData}
                    loading={tableLoading}
                    // pageCount={pageCount}
                    fetchData={() => {}}
                    noSearch={true}
                    onRowClick={(val) => {
                      navigate(`/${getEnumValue(val?.stage?.stageGroup)}/opportunity/${val?._id}`)
                    }}
                  />
                </Styled.OpportunityContainer>
              </>
            )
          }}
        </Formik>
      )}

      <CustomModal show={showAddCityModal}>
        <AddCityModal setShowAddCityModal={setShowAddCityModal} action="Add City" setDetailsUpdate={setDataUpdate} />
      </CustomModal>

      <CustomModal show={mergeModal} toggleModal={() => setMergeModal(false)}>
        <SharedStyled.ModalContainer style={{ width: '700px' }}>
          <SharedStyled.ModalHeaderContainer>
            <SharedStyled.FlexRow>
              <img src={UnitSvg} alt="modal icon" />
              <SharedStyled.FlexCol>
                <SharedStyled.ModalHeader>Merge Contact</SharedStyled.ModalHeader>
              </SharedStyled.FlexCol>
            </SharedStyled.FlexRow>
          </SharedStyled.ModalHeaderContainer>

          <SharedStyled.SettingModalContentContainer>
            <SharedStyled.FlexBox width="100%" flexDirection="column" gap="10px">
              <SharedStyled.Text fontSize="16px">
                There are existing contacts with the same name, would you like to merge this contact into the contact
                with that name?
                <br /> <br /> Select a contact to merge into:
              </SharedStyled.Text>
              {matchingClients?.map((client) => (
                <SharedStyled.FlexRow>
                  <SharedStyled.CheckboxZoneLabel
                    margin={0}
                    key={client._id}
                    style={{ display: 'block', margin: '8px 0' }}
                  >
                    <input
                      type="radio"
                      name="mergeClient"
                      value={client._id}
                      onChange={(e) => setSelectedClientId(e.target.value)}
                    />
                  </SharedStyled.CheckboxZoneLabel>

                  <SharedStyled.FlexCol gap="5px" margin="10px 0 10px 10px">
                    <SharedStyled.Text fontWeight="700" margin="0 0 0 20px" fontSize="14px">
                      {client?.firstName} {client?.lastName || ''}
                    </SharedStyled.Text>
                    <SharedStyled.Text margin="0 0 0 20px" fontSize="14px">
                      {client?.street}
                    </SharedStyled.Text>
                    <SharedStyled.Text margin="0 0 0 20px" fontSize="14px">
                      {client?.city}, {client?.state} {client?.zip}
                    </SharedStyled.Text>
                  </SharedStyled.FlexCol>

                  <SharedStyled.FlexCol gap="5px" margin="10px 0 10px 10px">
                    <SharedStyled.Text margin="0 0 0 20px" fontSize="14px">
                      {formatPhoneNumber(client?.phone, '')}
                    </SharedStyled.Text>
                    <SharedStyled.Text margin="0 0 0 20px" fontSize="14px">
                      {client?.email}
                    </SharedStyled.Text>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
              ))}

              <SharedStyled.Warning>
                Note: opportunities from the current client will be migrated to the selected client.
              </SharedStyled.Warning>
              <SharedStyled.FlexBox gap="10px">
                <Button onClick={handleMergeConfirm} disabled={!selectedClientId}>
                  Confirm Merge
                </Button>
                <Button onClick={handleContinueUpdate}>Cancel</Button>
              </SharedStyled.FlexBox>
            </SharedStyled.FlexBox>
          </SharedStyled.SettingModalContentContainer>
        </SharedStyled.ModalContainer>
      </CustomModal>
    </>
  )
}

export default ContactProfile
