import styled from 'styled-components'
import { Nue } from '../../shared/helpers/constants'
import { colors, screenSizes } from '../../styles/theme'

export const ClientContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  padding: 30px;
  place-items: start;
  max-width: 1280px;
  width: 100%;
  @media (min-width: 2000px) {
    margin: 0 auto;
  }
  .form {
    width: 100%;
    height: 100%;
  }
`

export const AddressWrap = styled.div`
  p {
    font-family: ${Nue.regular};
    font-size: 12px;
  }

  &.font {
    display: flex;
    flex-direction: column;
    gap: 2px;
    margin-top: 4px;
    p {
      font-family: ${Nue.regular};
      font-size: 16px;
    }
  }
`
export const FormGrid = styled.div`
  display: grid;
  width: 100%;
  grid-template-columns: 1fr;
  gap: 1rem;
`

export const IntendWidth = styled.div`
  width: 90%;
  margin-left: auto;
`

export const RightAlign = styled.div`
  display: flex;
  justify-content: flex-end;
`
export const SearchInput = styled.input`
  padding: 6px 14px;
  font-size: 16px;
  max-width: 200px;
  width: 100%;
  border: none;
  outline: none;
  border: 1px solid ${colors.grey3};
  border-radius: 5px;
  @media (min-width: ${screenSizes.M}px) {
    max-width: 320px;
    padding: 10px 14px;
  }
`
export const FilterButtonWrapper = styled.div`
  display: flex;
  cursor: pointer;
  border-radius: 5px;
  align-items: center;
  background: #cfcfcf;
  padding: 10px 18px;
  position: relative; /* Added for badge positioning */
  gap: 8px; /* Added for spacing between text and icon */
`
