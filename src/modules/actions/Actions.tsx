import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { ButtonCont, SettingsCont } from '../units/style'
import * as SharedStyled from '../../styles/styled'
import Button from '../../shared/components/button/Button'
import { Table } from '../../shared/table/Table'
import TabBar from '../../shared/components/tabBar/TabBar'
import DeletedActions from '../deleted/deletedActions/DeletedActions'
import { useSelector } from 'react-redux'
import { deleteSalesAction, getSalesActionByMemberId } from '../../logic/apis/sales'
import { isSuccess, notify } from '../../shared/helpers/util'
import { CustomModal } from '../../shared/customModal/CustomModal'
import ActionModal from '../opportunity/components/actionModal/ActionModal'
import { DeleteIcon } from '../../assets/icons/DeleteIcon'
import { EditIcon } from '../../assets/icons/EditIcon'
import { SLoader } from '../../shared/components/loader/Loader'
import ActionsDashboard from './components/ActionsDashboard'

const Actions = () => {
  const [loading, setLoading] = useState(false)
  const [actions, setActions] = useState<any>([])
  const [actionModal, setActionModal] = useState(false)
  const [actionData, setActionData] = useState<any>()
  const [activeTab, setActiveTab] = useState(0)

  const globalSelector = useSelector((state: any) => state)
  const { currentMember } = globalSelector.company

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: 'name',
      },
      {
        Header: 'Type',
        accessor: 'type',
      },
      {
        Header: 'Action',
        accessor: 'action',
      },
    ],
    []
  )

  const handleDelete = async (actionId: string) => {
    try {
      const res = await deleteSalesAction({ actionId: actionId! })
      if (isSuccess(res)) {
        fetchActionData()
        notify('Action deleted successfully', 'success')
      }
    } catch (error) {
      console.log({ error })
    }
  }
  const fetchActionData = useCallback(async () => {
    if (currentMember?._id) {
      try {
        let receivedData: any = []
        setLoading(true)
        const res = await getSalesActionByMemberId(currentMember?._id, false)
        if (isSuccess(res)) {
          const actions = res.data.data.salesAction?.actions
          actions?.forEach((res: any, _index: number) => {
            receivedData.push({
              name: res?.name,
              type: res?.type || '-',
              action: (
                <>
                  <SharedStyled.FlexBox width="100%" alignItems="center" gap="10px">
                    <SharedStyled.IconContainer
                      className="edit"
                      onClick={() => {
                        setActionData({
                          name: res?.name,
                          type: res?.type,
                          _id: res?._id,
                        })
                        setActionModal(true)
                        // setShowEditPositionPopUp(true)
                      }}
                    >
                      <EditIcon />
                    </SharedStyled.IconContainer>

                    <SharedStyled.IconContainer
                      className="delete"
                      onClick={() => {
                        handleDelete(res?._id)
                      }}
                    >
                      <DeleteIcon />
                    </SharedStyled.IconContainer>

                    {/* <Styled.IconContainer
                              className="permission"
                              onClick={() => navigate(`/settings/position/permission/1`)}
                              // onClick={() => {
                              //   setPositionData({ positionName: 'Crew Member', description: 'test' })
                              //   setShowEditPositionPopUp(true)
                              // }}
                            >
                              <PermissionIcon />
                            </Styled.IconContainer> */}
                  </SharedStyled.FlexBox>
                </>
              ),
            })
          })
          setActions(receivedData)
        } else throw new Error(res?.data?.message)
      } catch (err) {
        console.log('Failed init fetch', err)
      } finally {
        setLoading(false)
      }
    }
  }, [currentMember])

  return (
    <SettingsCont gap="24px">
      <SharedStyled.FlexRow justifyContent="space-between">
        <SharedStyled.SectionTitle>Actions</SharedStyled.SectionTitle>
        {activeTab === 1 && (
          <ButtonCont>
            <Button
              onClick={() => {
                setActionModal(true)
                setActionData({})
              }}
            >
              Add Action
            </Button>
          </ButtonCont>
        )}
      </SharedStyled.FlexRow>

      <SharedStyled.FlexRow alignItems="flex-start">
        <SharedStyled.FlexCol gap="24px">
          <TabBar
            onTabChange={(idx) => {
              setActiveTab(idx)
            }}
            tabs={[
              {
                title: 'To Do',
                render: () => (
                  <>
                    <ActionsDashboard />
                  </>
                ),
              },
              {
                title: 'Active',
                render: () => (
                  <Table
                    noOverflow
                    columns={columns}
                    data={actions}
                    loading={loading}
                    pageCount={1}
                    fetchData={fetchActionData}
                    onRowClick={(_vals) => {
                      //   setNewInputModal(true)
                      //   setEditInputVals(vals)
                    }}
                    noSearch
                    minWidth=""
                    noBorder
                    noPagination
                  />
                ),
              },
            ]}
            filterComponent={<></>}
          />
        </SharedStyled.FlexCol>
      </SharedStyled.FlexRow>

      <CustomModal show={actionModal}>
        <ActionModal
          onClose={() => {
            setActionModal(false)
            setActionData({})
          }}
          onComplete={() => {
            fetchActionData()
            setActionData({})
          }}
          actionData={actionData}
        />
      </CustomModal>
    </SettingsCont>
  )
}

export default Actions
