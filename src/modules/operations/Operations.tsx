import { useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import { useEffect, useMemo, useRef, useState } from 'react'
import {
  dayjsFormat,
  extractPermissionByName,
  filterOpportunity,
  getDataFromLocalStorage,
  getKeysFromObjects,
  getPercentCompleteFromId,
  getSalesPersonIdFromName,
  getStageNameFromId,
  hasValues,
  isSuccess,
  notify,
} from '../../shared/helpers/util'
import {
  getOperationsOpportunity,
  getOperationsOpportunityCompletion,
  getPositionMembersById,
  getStages,
  searchOpps,
} from '../../logic/apis/sales'
import HorizontalScrollableDiv from '../../shared/scrollableDiv/ScrollableDiv'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { I_Opportunity } from '../opportunity/Opportunity'
import useDebounce from '../../shared/hooks/useDebounce'
import { AnyKey } from '../opportunity/components/assessmentForm/AssessmentForm'
import { getPosition } from '../../logic/apis/position'
// import { I_Position, I_SalesPerson } from '../newLead/NewLead'
import Button from '../../shared/components/button/Button'
import { StagesBoard } from './component/StagesBoard'
import { useAppDispatch } from '../../logic/redux/reduxHook'
import { setFilterOppsBy } from '../../logic/redux/actions/ui'
import { useClickOutside } from '../../shared/hooks/useClickOutside'
import { SmallLoaderCont } from '../../shared/components/loader/style'
import { RenderData, SLoader } from '../../shared/components/loader/Loader'
import WithCheckUserId from '../../shared/components/hoc/WithPermission'
import { StageGroupEnum, StorageKey } from '../../shared/helpers/constants'
import { CustomModal } from '../../shared/customModal/CustomModal'
import AddLeadWarrantyModal from './component/AddLeadWarrantyModal'
import { I_Position } from '../sales/AddOpportunityModal'
import { EditClientModal } from '../client/components/addNewClientModal/EditClientModal'
import { DropdownButton, DropdownContainer, DropdownContent } from '../../shared/dropdownWithCheckboxes/style'
import CheckboxList from '../track/components/CheckboxList'
import { CaretSvg } from '../../shared/helpers/images'

const Operations = () => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()

  const [searchTerm, setSearchTerm] = useState('')
  const [searchedOpps, setSearchedOpps] = useState<I_Opportunity[]>([])
  const [projectManager, setProjectManager] = useState<I_SalesPerson[]>([])
  const [stage, setStages] = useState<I_Opportunity[]>([])
  const [percentComplete, setPercentComplete] = useState<any>([])
  const debouncedSearch = useDebounce(searchTerm, 500)
  const [clientAutoFill, setClientAutoFill] = useState<any>({})
  const [clientData, setClientData] = useState<any>({})

  // const [shimmerLoading, setShimmerLoading] = useState(true)

  const initialBoard = {
    columns: [
      {
        id: 1,
        title: 'Backlog',
        cards: [],
      },
      {
        id: 2,
        title: 'Doing',
        cards: [],
      },
      {
        id: 3,
        title: 'Q&A',
        cards: [],
      },
      {
        id: 4,
        title: 'Production',
        cards: [],
      },
      {
        id: 5,
        title: 'Production2',
        cards: [],
      },
    ],
  }

  const [boardValue, setBoardValue] = useState(initialBoard)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, positionDetails } = globalSelector.company

  const isRestrictedOpp =
    positionDetails?.symbol === 'subContractor' ||
    getDataFromLocalStorage('position') === 'Subcontractor' ||
    positionDetails?.symbol === 'Foreman' ||
    getDataFromLocalStorage('position') === 'Foreman'

  const {
    filterOperationsBy: { name: client, _id },
  } = globalSelector.ui
  const [opps, setOpps] = useState<I_Opportunity[]>([])
  const [searchData, setSearchData] = useState([])
  const [showDropdown, setShowDropdown] = useState<boolean>(false)
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [editClientModal, setShowEditClientModal] = useState(false)
  const [clientName, setClientName] = useState<any>('')
  const [crewTypeSelected, setCrewTypeSelected] = useState<any>({})
  const [projectTypeSelected, setProjectTypeSelected] = useState<any>({})
  const [projectManagerSelected, setProjectManagerSelected] = useState<any>({})
  const [salesPersonSelected, setSalesPersonSelected] = useState<any>({})
  const dropdownRef = useRef(null)
  const [searchLoading, setSearchLoading] = useState(false)
  const [showFilter, setShowFilter] = useState(false)

  const isOppInfoPermissionReadOnly =
    hasValues(positionDetails) && extractPermissionByName(positionDetails, 'opportunity info')?.crud?.write === false

  const positionPermission =
    positionDetails?.permissions?.find((v) => v.category === 'crm')?.resources?.find((v) => v.name === 'operations')
      ?.permissions || 3
  console.log({ positionPermission })
  // const [filteredOpps, setFilteredOpps] = useState<{ columns: any[] }>({ columns: [] })

  // useEffect(() => {
  //   if (_id && !loading) {
  //     const filter = boardValue.columns?.map((stage: any) => {
  //       return {
  //         ...stage,
  //         cards: stage?.cards?.filter((card: any) => {
  //           return card?.projectManager === _id || card?.salesPerson === _id
  //         }),
  //       }
  //     })

  //     setFilteredOpps({ columns: filter })
  //   }
  // }, [_id, loading])

  useClickOutside(dropdownRef, setShowDropdown)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowFilter(false)
      }
    }

    if (showFilter) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showFilter])

  useEffect(() => {
    if (searchTerm !== '') {
      setShowDropdown(true)
    } else {
      setShowDropdown(false)
      setSearchData([])
    }
  }, [searchTerm])

  const handleFilterChange = (selectedItems: { [key: string]: boolean }, fn: any) => {
    fn(selectedItems)
  }

  useEffect(() => {
    ;(async () => {
      if (debouncedSearch && opps?.length) {
        try {
          setSearchLoading(true)
          const res = await searchOpps({
            search: debouncedSearch,
          })

          // setSearchedOpps(res?.data?.data?.opps)
          setSearchData(res?.data?.data?.opps)
        } catch (error) {
          console.error('Search Error', error)
        } finally {
          setSearchLoading(false)
        }
      }
    })()
  }, [debouncedSearch, opps?.length])

  const handleOldCompleted = () => {
    navigate(`/operations/completed`)
  }

  const getPositionMembers = async (positionId: string) => {
    try {
      const response = await getPositionMembersById({ positionId }, false)
      if (isSuccess(response)) {
        setProjectManager(response?.data?.data?.memberData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const getSelectedFilters = (obj: any): any[] => {
    return (
      obj &&
      Object.entries(obj)
        ?.map(([k, v]) => (v ? k : null))
        ?.filter(Boolean)
    )
  }
  const projectTypeFilters = getSelectedFilters(projectTypeSelected)
  const crewTypeFilters = getSelectedFilters(crewTypeSelected)
  const salesPersonTypeFilters = getSelectedFilters(salesPersonSelected)
  const projectManagerTypeFilters = getSelectedFilters(projectManagerSelected)

  const filters = {
    projectTypeFilters,
    crewTypeFilters,
    salesPersonTypeFilters,
    projectManagerTypeFilters,
  }

  const uniqueValues = useMemo(() => {
    const projects = new Map<string, { name: string; _id: string | boolean }>()
    const crews = new Map<string, { name: string; _id: string }>()
    const projectManager = new Map<string, { name: string; _id: string }>()
    const salesPerson = new Map<string, { name: string; _id: string }>()

    boardValue?.columns?.forEach((column) => {
      column?.cards?.forEach((opp) => {
        if (opp?.['order-type']?.name) {
          projects.set(opp['order-type'].name, {
            name: opp['order-type'].name,
            _id: opp['order-type']._id,
          })
        }
        if (opp?.workingCrew?.name) {
          crews.set(opp.workingCrew.name, {
            name: opp.workingCrew.name,
            _id: opp.workingCrew.id,
          })
        }
        if (opp?.salesPersonName) {
          salesPerson.set(opp.salesPersonName, {
            name: opp.salesPersonName,
            _id: opp.salesPerson,
          })
        }
        if (opp?.projectManagerName) {
          projectManager.set(opp.projectManagerName, {
            name: opp.projectManagerName,
            _id: opp.projectManager,
          })
        }
      })
    })

    boardValue?.columns?.forEach((column) => {
      column?.cards?.forEach((opp) => {
        if (opp?.warrantyType) {
          projects.set('Warranty', {
            name: 'Warranty',
            _id: 'Warranty',
          })
        }
      })
    })

    return {
      projects: Array.from(projects.values()),
      crews: Array.from(crews.values()),
      projectManager: Array.from(projectManager.values()),
      salesPerson: Array.from(salesPerson.values()),
    }
  }, [boardValue])

  // const uniqueValues = useMemo(() => {
  //   const projects = new Set()
  //   const crews = new Set()
  //   const projectManager = new Set()
  //   const salesPerson = new Set()

  //   boardValue?.columns?.forEach((column) => {
  //     column?.cards?.forEach((opp) => {
  //       // Add values to the sets only if they are not undefined
  //       if (opp?.['order-type']?.name) projects.add(opp['order-type'].name)
  //       if (opp?.workingCrew?.name) crews.add(opp.workingCrew.name)
  //       if (opp?.salesPersonName) salesPerson.add(opp.salesPersonName)
  //       if (opp?.projectManagerName) projectManager.add(opp.projectManagerName)
  //     })
  //   })

  //   boardValue?.columns?.forEach((column) => {
  //     column?.cards?.forEach((opp) => {
  //       if (opp?.warrantyType) projects.add('Warranty')
  //     })
  //   })
  //   // Convert sets to arrays and map to the desired format
  //   return {
  //     projects: [...projects].map((itm) => ({ name: itm, _id: itm })),
  //     crews: [...crews].map((itm) => ({ name: itm, _id: itm })),
  //     projectManager: [...projectManager].map((itm) => ({ name: itm, _id: itm })),
  //     salesPerson: [...salesPerson].map((itm) => ({ name: itm, _id: itm })),
  //   }
  // }, [boardValue])

  const filteredOps = useMemo(() => {
    // Check if any filters are applied
    const isFiltering =
      projectTypeFilters.length > 0 ||
      crewTypeFilters.length > 0 ||
      salesPersonTypeFilters.length > 0 ||
      projectManagerTypeFilters.length > 0

    // If no filters are applied, return the original data
    if (!isFiltering) {
      return boardValue || []
    }

    // Apply filters
    const filteredColumns = boardValue?.columns?.map((column) => ({
      ...column,
      cards: column.cards.filter((opp) => {
        const projectMatch =
          projectTypeFilters.length === 0 ||
          (opp?.warrantyType
            ? projectTypeFilters.includes('Warranty')
            : projectTypeFilters.includes(opp?.['order-type']?._id))
        const crewMatch = crewTypeFilters.length === 0 || crewTypeFilters.includes(opp?.workingCrew?.id)
        const salesPersonMatch =
          salesPersonTypeFilters.length === 0 || salesPersonTypeFilters.includes(opp?.salesPerson)
        const projectManagerMatch =
          projectManagerTypeFilters.length === 0 || projectManagerTypeFilters.includes(opp?.projectManager)

        return projectMatch && crewMatch && salesPersonMatch && projectManagerMatch
      }),
    }))

    return {
      ...boardValue,
      columns: filteredColumns,
    }
  }, [boardValue, projectTypeFilters, crewTypeFilters, salesPersonTypeFilters, projectManagerTypeFilters])

  console.log({ projectTypeFilters, crewTypeFilters, salesPersonTypeFilters, filteredOps })

  const initFetch = async () => {
    const fetchParams = {
      deleted: false,
      projectManager: positionDetails.symbol === 'ProjectManager' ? positionDetails.memberId : '',
      salesPerson: positionDetails.symbol == 'RRTech' ? positionDetails.memberId : undefined,
      // stageGroup: StageGroupEnum.Operations,
      // lost: false,
      status: 'active',
    }
    try {
      const [stagesRes, oppRes, completed] = await Promise.all([
        getStages({}, false, StageGroupEnum.Operations),
        getOperationsOpportunity(fetchParams),
        getOperationsOpportunityCompletion(fetchParams),
      ])

      if (isSuccess(stagesRes) && isSuccess(oppRes)) {
        const { stage: stages } = stagesRes.data.data
        const { completed: completePercent } = completed.data.data

        let newBoard = new Array(stages?.length - 1)
        setStages(stagesRes.data.data.stage)
        setPercentComplete(completePercent)
        // handle opportunity response

        const { opportunity } = oppRes?.data?.data
        let stageNames: AnyKey = {}
        stages.forEach((stage: any) => {
          let item = {
            title: stage.name,
            cards: [],
            id: stage.sequence, // TODO: change to id
            description: stage.description,
            sortField: stage?.sortingField ? Object.keys(stage?.sortingField)[0] : undefined,
            sortOrder: stage?.sortingField ? Object.values(stage?.sortingField)[0] : undefined,
          }
          newBoard[stage.sequence - 1] = item
          stageNames[stage._id] = stage
        })

        opportunity.forEach((opp: any, idx: number) => {
          stages.forEach((stage: any) => {
            if (stage?._id === opp.stage) {
              const cards = newBoard[stage.sequence - 1].cards
              newBoard[stage.sequence - 1].cards = [
                ...cards,
                {
                  ...opp,
                  title: `${opp?.contactName} - ${opp.num}`,
                  id: cards?.length + 1,
                  placeInLine: opp['Ready To Start']?.checklists?.['place-in-line-result']?.value,
                },
              ]
            }
          })
        })
        setOpps(opportunity)
        setBoardValue({
          columns: newBoard,
        })
        setLoading(false)
      } else {
        if (!isSuccess(stagesRes)) throw new Error(stagesRes?.data.message)
        if (!isSuccess(oppRes)) throw new Error(oppRes?.data.message)
      }
    } catch (error: any) {
      notify(error?.message ?? 'Unable to fetch sales data', 'error')
      console.error('getAllStages error', error)
      setLoading(false)
    }
  }

  useEffect(() => {
    if (positionDetails?.symbol) {
      initFetch()
      getPositions()
    }
  }, [client, positionDetails?.symbol])

  const getPositions = async () => {
    try {
      const response = await getPosition({ deleted: false }, false)
      if (isSuccess(response)) {
        const positions: I_Position[] = response?.data?.data?.position
        let projectManagerIdx: string[] = []
        positions.forEach((position: any, idx) => {
          if (position.symbol === 'ProjectManager') {
            projectManagerIdx.push(position?._id)
            return
          }
          if (position.symbol === 'RRTech') {
            projectManagerIdx.push(position?._id)
            return
          }
        })
        getPositionMembers(projectManagerIdx?.join())
        // let projectManagerIdx = 0
        // positions.forEach((position: any, idx) => {
        //   if (position.symbol === 'ProjectManager') {
        //     projectManagerIdx = idx
        //     return
        //   }
        // })
        // getPositionMembers(positions[projectManagerIdx]._id)
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    } finally {
      // setShimmerLoading(false)
    }
  }

  return false ? (
    <>
      <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
      <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
      <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
    </>
  ) : (
    <Styled.SalesContainer>
      <SharedStyled.SectionTitle className="opportunity">Operations</SharedStyled.SectionTitle>
      <SharedStyled.FlexBox width="100%" justifyContent="space-between" wrap="wrap">
        {isRestrictedOpp ? (
          <div />
        ) : (
          <SharedStyled.FlexBox gap="10px">
            <RenderData loader={<SLoader width={104} height={40} />} loading={loading}>
              {isOppInfoPermissionReadOnly ? null : (
                <Button
                  className="fit"
                  onClick={() => {
                    setShowAddModal(true)
                  }}
                >
                  {' '}
                  New Warranty{' '}
                </Button>
              )}
            </RenderData>

            <RenderData loader={<SLoader width={200} height={40} />} loading={loading}>
              <Button className="fit" onClick={handleOldCompleted}>
                Old Completed
              </Button>
            </RenderData>
          </SharedStyled.FlexBox>
        )}

        <div>
          <RenderData loader={<SLoader width={200} height={40} />} loading={loading}>
            <DropdownContainer marginTop="0" ref={dropdownRef}>
              <DropdownButton
                disabled={uniqueValues.projects?.length === 0}
                type="button"
                onClick={() => setShowFilter((p) => !p)}
                style={{ width: '300px' }}
              >
                <div className="selection">
                  Filter Opportunities
                  <img className={showFilter ? 'rotate' : ''} src={CaretSvg} alt="chevron" />
                </div>
              </DropdownButton>

              {showFilter ? (
                <DropdownContent style={{ width: '300px', zIndex: '2' }}>
                  <Styled.FilterContainer gap="0px" justifyContent="flex-start" onClick={(e) => e.stopPropagation()}>
                    <CheckboxList
                      className="small"
                      title="Project Types"
                      data={uniqueValues.projects}
                      checkedItems={projectTypeSelected}
                      onSelectionChange={(val) => {
                        handleFilterChange(val, setProjectTypeSelected)
                      }}
                      allText="All"
                      isCheckedAll={!projectTypeFilters?.length}
                    />

                    {positionPermission === 1 || positionPermission === 2 ? (
                      <>
                        <CheckboxList
                          checkedItems={crewTypeSelected}
                          title="Crews"
                          className="small"
                          data={uniqueValues.crews}
                          onSelectionChange={(val) => {
                            handleFilterChange(val, setCrewTypeSelected)
                          }}
                          allText="All"
                          isCheckedAll={!crewTypeFilters?.length}
                        />
                        <CheckboxList
                          checkedItems={salesPersonSelected}
                          title="Salesperson"
                          className="small"
                          data={uniqueValues.salesPerson}
                          onSelectionChange={(val) => {
                            handleFilterChange(val, setSalesPersonSelected)
                          }}
                          allText="All"
                          isCheckedAll={!salesPersonTypeFilters?.length}
                        />
                        <CheckboxList
                          className="small"
                          checkedItems={projectManagerSelected}
                          title="Project Manager"
                          data={uniqueValues.projectManager}
                          allText="All"
                          isCheckedAll={!projectManagerTypeFilters?.length}
                          onSelectionChange={(val) => {
                            handleFilterChange(val, setProjectManagerSelected)
                          }}
                        />
                      </>
                    ) : null}
                  </Styled.FilterContainer>
                </DropdownContent>
              ) : null}
            </DropdownContainer>
          </RenderData>
        </div>
      </SharedStyled.FlexBox>
      <Styled.BoardContainer>
        <HorizontalScrollableDiv>
          <StagesBoard boardValue={filteredOps} percentComplete={percentComplete} loading={loading} />
        </HorizontalScrollableDiv>
      </Styled.BoardContainer>

      <CustomModal show={showAddModal}>
        <AddLeadWarrantyModal
          onClose={() => {
            setShowAddModal(false)
          }}
          onComplete={() => {
            setShowAddModal(false)
            initFetch()
            getPositions()
          }}
          setClientName={setClientName}
          clientData={clientData}
          setClientData={setClientData}
          setShowEditClientModal={setShowEditClientModal}
        />
      </CustomModal>

      <CustomModal show={editClientModal}>
        <EditClientModal
          setShowEditClientModal={setShowEditClientModal}
          setClientAutoFill={setClientAutoFill}
          clientName={clientName}
          noLoadScript={true}
          clientData={clientData}
          setClientData={setClientData}
        />
      </CustomModal>
    </Styled.SalesContainer>
  )
}

export default Operations
