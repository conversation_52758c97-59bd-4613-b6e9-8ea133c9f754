import styled from 'styled-components'
import { Field } from 'formik'
import { colors, screenSizes } from '../../styles/theme'
import { SettingsCont } from '../units/style'
import { FlexBox, FlexCol, FlexRow, InputFive } from '../../styles/styled'
import { DateContent, InputLabelDiv } from '../../shared/date/style'
import { Nue } from '../../shared/helpers/constants'

export const OppContainer = styled(SettingsCont)<{ navCollapsed?: boolean }>`
  gap: 32px;
  /* max-width: 1270px; */
  .medium {
    color: ${colors.text};
    font-family: ${Nue.medium};
    font-size: 12px;
  }

  .loading {
    margin-top: 10px;
  }

  h4 {
    color: ${colors.text};
    font-family: ${Nue.bold};
    font-size: 16px;
  }

  h3 {
    color: ${colors.text};
    font-family: ${Nue.bold};
    font-size: 24px;
    margin-bottom: 16px;
  }
  .state-error {
    width: 100%;
    text-align: center;
    padding: 5px;
    background-color: #feaeae;
  }
  .state-inactive {
    width: 100%;
    text-align: center;
    padding: 5px;
    background-color: ${colors.lightGray};
  }
  .custom-styles {
    padding: 16px !important;
    border-radius: 8px;
    background: #fff;
    @media (min-width: 1281px) {
      padding: 24px;
    }
    box-shadow: 0px 2px 20px 0px rgba(156, 145, 145, 0.25);
  }

  .half-width {
    width: 100%;

    @media (min-width: ${screenSizes.L}px) {
      width: ${(props) => (props.navCollapsed ? '44vw' : '38vw')};
    }
    @media (min-width: ${screenSizes.XXL}px) {
      width: 100%;
    }
  }

  .container {
    flex-wrap: wrap;
    @media (min-width: ${screenSizes.L}px) {
      flex-wrap: nowrap;
    }
  }

  &.is-opp-deleted,
  &.is-lead-deleted {
    position: relative;
    /* pointer-events: none; */
  }

  &.subcontractor {
    @media (min-width: ${screenSizes.L}px) {
      flex-direction: row;
    }

    #print-div-crew-sheet {
      display: none;
    }

    @media print {
      #print-div-crew-sheet {
        display: block;
      }
    }

    .subcontractor-hide {
      display: none;
    }
  }
`

export const DeletedContent = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  left: 50%;
  display: flex;
  justify-content: center;
  transform: translateX(-50%);
  z-index: 9;
  background: rgba(156, 145, 145, 0.5);

  cursor: not-allowed;
  p {
    font-size: 24px;
    font-family: ${Nue.medium};
    padding: 8px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.5);
    /* padding-top: 100px; */
    color: red;

    @media (min-width: ${screenSizes.XS}px) {
      font-size: 30px;
    }
  }

  &.deleted-opp {
    p {
      font-size: 18px;

      @media (min-width: ${screenSizes.XS}px) {
        font-size: 30px;
      }
    }
  }
`

export const AddressContainer = styled.div<any>`
  width: 100%;
  margin: 24px 0px;

  .sub-heading {
    font-size: 14px;
  }
  .two-input {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 18px;

    @media (max-width: ${screenSizes.M}px) {
      flex-wrap: wrap;
    }
  }

  .inner-inputs {
  }
`

export const LabelDiv = styled.label<any>`
  font-size: 14px;
  font-weight: 500;
  color: ${colors.darkGrey};
  margin-top: ${(props) => props.marginTop};
  text-align: ${(props) => props.textAlign};
  width: ${(props) => props.width};
  cursor: ${(props) => props.cursor};
  @media (min-width: ${screenSizes.M}px) {
    font-size: 16px;
  }
`

export const PoContainer = styled.div<any>`
  max-width: ${(props) => (props.maxWidth ? props.maxWidth : '100%')};
  width: 100%;
  /* border: 1px solid ${colors.lightBackground}; */

  display: flex;
  flex-direction: column;

  /* border-radius: 8px; */
  background: #fff;
  /* box-shadow: 0px 2px 20px 0px rgba(156, 145, 145, 0.25); */

  .data-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex-wrap: wrap;
    padding: ${(props) => (props.wrapperPadding ? props.wrapperPadding : '14px 28px')};
    .po-input {
      height: 38px;
      font-size: 24px !important;
    }
    p {
      text-transform: capitalize;
      white-space: nowrap;

      color: ${colors.gray};
      font-family: ${Nue.medium};
      font-size: 13px;
    }

    .distance {
      div,
      input {
        width: 50px !important;
        min-width: inherit;
      }
    }

    .google {
      @media (max-width: ${screenSizes.XS}px) {
        p {
          margin-left: auto;
        }
        flex-direction: column !important;
      }
    }

    input,
    select,
    option {
      min-width: auto;
      width: fit-content;
      padding: 0;
      font-size: 13px;
      font-family: ${Nue.medium};
      font-weight: 500;
      color: ${colors.text};
    }

    input:not([type='date']) {
      font-size: 13px;
    }

    select {
      font-size: 13px;
      padding: 0;
      margin-left: -5px;
    }

    span {
      text-transform: capitalize;
      font-size: 13px;
      font-family: ${Nue.medium};
    }

    ${FlexBox} {
      flex-direction: row-reverse;
      justify-content: space-between;
      & > div {
        width: max-content;
        justify-content: flex-start;
        /* margin-left: auto; */
        color: ${colors.text};
        font-family: ${Nue.medium};
        font-size: 13px;
      }
    }

    ${InputLabelDiv} {
      width: 100% !important;
      margin-left: 0 !important;
      div,
      input {
        height: 17px;
      }
      input {
        width: max-content;
      }
    }

    .date-value {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      font-size: 13px;
      font-family: ${Nue.medium};
    }
  }
  .header-wrapper {
    padding: 6px 12px;
  }
  p {
    font-size: 16px;
    margin: 0px;
  }

  .bold {
    font-size: 13px;
    font-family: ${Nue.medium};
    white-space: nowrap;
  }
  span {
    white-space: nowrap;
  }

  table {
    border: none;
    td:first-child,
    td:last-child {
      padding: 10px 8px;
    }
    td:first-child {
      border-radius: 8px 0px 0px 8px;
    }
    td:last-child {
      border-radius: 0px 8px 8px 0px;
      div {
        margin-left: auto;
      }
    }
    td {
      padding: 10px;
      border: none;
    }
  }

  .nested {
    span,
    p {
      font-size: 11px !important;
    }
    padding-left: 10px;
  }

  &.read-only {
    input,
    select,
    .edit-address {
      all: unset;
      outline: none;
      pointer-events: none;
      cursor: default;
      font-family: ${Nue.medium};
    }

    input[type='datetime-local']::-webkit-calendar-picker-indicator {
      display: none;
    }
  }
`

export const SubHeading = styled.h3``

export const ButtonsWrapper = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 10px;
  flex-direction: column;
`

export const TodoWrapper = styled.div`
  padding: 24px 0px;
  width: 100%;
  font-size: 14px;

  .checkbox-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 8px;
    width: 100%;
    margin-right: 12px;
    /* margin-bottom: 10px; */
    /* p {
      white-space: nowrap;
    } */
  }
  .adjust-box > div:first-child {
    flex: 55%;
  }

  .adjust-box > div:last-child {
    flex: 45%;
  }
  .strike {
    text-decoration: line-through;
  }

  .bold {
    color: ${colors.text};
    font-family: ${Nue.medium};
  }

  .todo-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    /* padding: 24px; */
    margin-top: 16px;
    border-radius: 8px;
    background: #fff;
    /* box-shadow: 0px 2px 20px 0px rgba(156, 145, 145, 0.25); */

    .link {
      color: ${colors.lightBlue};
      cursor: pointer;
      :hover {
        text-decoration: underline;
      }
    }
  }

  .todo-container {
    width: 100%;
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 10px;

    & > div:first-child {
      width: max-content;
    }
  }
`
export const ShowContract = styled.div`
  cursor: pointer;
  background-color: #ffc107;
  padding: 10px;
  text-align: center;
  font-weight: 700;
`

export const AddressCont = styled(FlexRow)`
  align-items: flex-start;
  width: 100% !important;
  div {
    font-family: ${Nue.medium};
  }

  button {
    font-size: 12px;
    border-radius: 4px;
    padding: 4px 10px;
  }
  ${FlexCol} {
    /* gap: 4px;
    align-items: flex-start;

    .zip {
      div,
      input {
        text-align: left !important;
        width: max-content;
      }
    } */
    width: 100%;
  }
  /* .label-float {
    label {
      font-size: 12px;
    }
  } */

  &#custom {
    input,
    select {
      border: 1px solid ${colors.darkGrey};
      padding-left: 6px;
      height: 36px;
      padding-top: 8px;
    }

    label {
      top: -3px;
      left: 6px;
      font-size: 10px;
      padding: 5px 0;
    }
    .label-float {
      padding-top: 0px;
    }
  }

  select {
    margin: 0;
  }

  /* .input {
    width: 100% !important;
    margin-right: 10px;

    div,
    input {
      width: 70px;
      font-size: 13px;
      font-family: ${Nue.medium};
    }
  } */

  #street {
    width: 100%;
    div,
    input {
      width: inherit;
    }
  }

  #city {
    flex: 1;
    min-width: 100px;
    div,
    input {
      width: 100%;
    }
  }
  #state {
    width: 60px;
    div,
    select {
      width: 60px;
      margin-left: 0;
    }
    label {
      left: 10px !important;
    }
  }

  .zip {
    width: 60px;
    input {
      width: 60px;
    }
  }

  &.google {
    ${FlexCol} {
      & > div {
        width: 100%;
      }
    }
  }

  &#google {
    .label-float {
      padding-top: 0;

      input {
        padding-left: 7px;
      }

      label {
        left: 3px;
        top: calc(50% - 17px);
      }
    }
  }

  ${InputFive} {
    width: 100%;
    input {
      padding: 20px 16px 8px 16px;
      width: inherit;
      font-family: ${Nue.medium};
    }
  }
`

export const TableWrap = styled.div`
  table {
    td {
      padding: 14px 8px;
      text-align: center;
      white-space: nowrap;
    }
  }
`

export const SummaryCont = styled.div`
  p {
    font-family: ${Nue.regular} !important;
  }

  .extra {
    font-family: ${Nue.medium};
  }
  span {
    font-family: ${Nue.medium};
  }
`

export const SalesOppCont = styled.div`
  input {
    border: none;
    /* margin-left: 2px; */
    width: 60px !important;
    padding: 1px !important;

    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    /* Firefox */
    [type='number'] {
      -moz-appearance: textfield;
    }
  }

  #commission {
    input {
      border: 1px solid rgba(106, 116, 126, 0.2);
      width: 100% !important;
      min-width: 100%;
      padding: 20px 16px 8px !important;
      height: 52px !important;
    }

    ${InputLabelDiv} {
      input,
      div {
        height: 52px;
      }
    }
  }
`
