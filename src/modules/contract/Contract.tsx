import { ErrorMessage, Field, Form, Formik } from 'formik'
import React, { Fragment, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate, useParams } from 'react-router-dom'

import {
  checkProject,
  createOrder,
  createPrice,
  deletePrice,
  getPriceIdByProject,
  getPrices,
  getProjectById,
  getProjectTask,
  getProjectTypeById,
  getSignContract,
  getTaskApi,
  getTax,
  getUnitsApi,
  getUpgradePackage,
  getWorkOrder,
  setAccepted,
  updateActivePrice,
  updatePrice,
  getOrderById,
  permDeletePrice,
  getMultipleProject,
} from '../../logic/apis/projects'
import { getOpportunityById, updateActivity } from '../../logic/apis/sales'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import {
  calculateDiscountPercentage,
  convertStrToKey,
  formatNumberToCommaS,
  formatPhoneNumber,
  getDataFromLocalStorage,
  getUnitSymbolFromId,
  hasValues,
  isSuccess,
  notify,
  sortByOrder,
  toPascalCase,
} from '../../shared/helpers/util'
import * as SharedStyled from '../../styles/styled'
import { colors } from '../../styles/theme'
import { IFullUnit } from '../units/Units'
import * as Styled from './style'
import * as Yup from 'yup'
import Button from '../../shared/components/button/Button'
import { upgradePackagesMethod, signContractMethod, upgradeOptions } from './signContract'
import {
  avgPitch,
  calculatePrice,
  combineDuplicateMaterials,
  getSalesTax,
  roundTo2,
  sumPricesAndPriceTotals,
} from './constant'
import Checkbox from '../../shared/checkbox/Checkbox'
import { boolean } from 'yup/lib/locale'
import OtherProjects from './components/OtherProjects'
import AutoComplete from '../../shared/autoComplete/AutoComplete'
import { StorageKey } from '../../shared/helpers/constants'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import Toggle from '../../shared/toggle/Toggle'
interface InitialValues {
  oppId: string
  projectId: string
  projectPriceId: string
  projectName: string
  projectType: string
  orderNotes: string
  priceTotals: any
  paymentType: string
  matList: string[]
  workOrder: string[]
  createdBy: string
  discount: number
  isUpdate: boolean
  options: any
  colors: any
  colors2: any
  addOtherProjects: boolean
  otherProjects: any
  projects: any[]
  // extraProjects: any
}

const Contract = () => {
  let { oppId, contactId, projectId } = useParams()
  const [colorDripEdge, setColorDripEdge] = useState('')
  const [discount, setDiscount] = useState(0)
  const [projectTaskData, setProjectTaskData] = useState([])
  const [upgradePackageData, setUpgradePackageData] = useState([])
  const [selectedupgradePackageData, setSelectedupgradePackageData] = useState([])
  const [signContractDataFrontEnd, setSignContractDataFrontEnd] = useState<any>()
  const [workOrder, setWorkOrder] = useState<any>([])
  const [colorFlashingVent, setColorFlashingVent] = useState('')
  // const [manualChange, setManualChange] = useState(0)
  const [colorGutter, setColorGutter] = useState('')
  const [priceIdByProject, setPriceIdByProject] = useState('')
  const [projectTypeId, setProjectTypeId] = useState('')
  const [multipleProjectType, setMultipleProjectType] = useState([])
  const [priceId, setPriceId] = useState('')
  const [isOrderId, setIsOrderId] = useState(false)
  const [orderCallFirst, setOrderCallFirst] = useState(false)
  const [projectById, setProjectById] = useState<any>()
  const [selectedPackage, setSelectedPackage] = useState<any>()
  const [orderDetails, setOrderDetails] = useState<any>({})
  const [priceByProject, setPriceByProject] = useState<any>()
  const [projectType, setProjectTypeData] = useState<any>({})
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [selectedItemsWorkOrder, setSelectedItemsWorkOrder] = useState<string[]>([])
  const [selectedObject, setSelectedObject] = useState<any>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [managePrice, setManagePrice] = useState(false)
  const [opportunity, setOpportunity] = useState<any>([])
  const [pricesManage, setPricesForManage] = useState<any>([])
  const [activePrice, setActivePrice] = useState<any>([])
  const [units, setUnits] = useState<IFullUnit[]>([])
  const [taxUniqueState, setTaxUniqueState] = useState<any>([])
  const [taxModal, setTaxModal] = useState<any>([])
  const [btnLoading, setBtnLoading] = useState(false)
  const [packageId, setPackageId] = useState('')
  const [btnLoadingAddPrice, setBtnLoadingAddPrice] = useState(false)
  const [btnLoadingMakePrice, setBtnLoadingMakePrice] = useState<any>([])
  const [btnLoadingDeletePrice, setBtnLoadingDeletePrice] = useState<any>([])
  const [signContractReload, setSignContractReload] = useState(false)
  const [togglePrice, setTogglePrice] = useState(false)
  const [upgradeOptionData, setUpgradeOptionsData] = useState([])
  const [filteredTaskForRemove, setFilteredTaskForRemove] = useState<string[]>([])
  const [selectedUniqueGroups, setSselectedUniqueGroups] = useState<string[][]>([])
  const [signContractProjectType, setSignContractProjectType] = useState<any>({})
  const [multipleProjectPackageId, setMultipleProjectPackageId] = useState<any>({})
  const [selectedProjectOptions, setSelectedProjectOptions] = useState<any>({})
  const [finalPriceFrontEnd, setFinalPriceFrontEnd] = useState<any>({})
  const [priceforProject, setPriceforProject] = useState<any>({})
  const [salesTax, setSalesTax] = useState<any>({
    jurisdictionState: {},
    taxValue: 0,
  })

  const operationsFlag = location.pathname?.includes('operations')

  const globalSelector = useSelector((state: any) => state)
  const { currentMember, companySettingForAll } = globalSelector.company
  const [initialValues, setInitialValues] = useState<InitialValues>({
    oppId: oppId!,
    projectId: projectId!,
    projectPriceId: '',
    projectName: '',
    projectType: '',
    orderNotes: '',
    priceTotals: {},
    matList: [],
    workOrder: [],
    paymentType: '',
    createdBy: currentMember._id!,
    discount: 0,
    isUpdate: false,
    options: {},
    colors: {},
    colors2: {},
    addOtherProjects: false,
    otherProjects: {
      types: [
        {
          projectType: '',
          value: false,
          packages: [],
          options: [],
          projectTypeColors: {},
          colors: {},
          colors2: {},
          notes: '',
        },
      ],
    },
    projects: [],
  })
  const navigate = useNavigate()
  const { pathname } = useLocation()

  const ContractSchema = Yup.object().shape({
    orderNotes: Yup.string().min(2, 'Too Short!').required('Required'),
    paymentType: Yup.string().min(2, 'Too Short!').required('Required'),
    otherProjects: Yup.object().shape({
      types: Yup.array().of(
        Yup.object().shape({
          value: Yup.boolean(),
          notes: Yup.string().when('value', {
            is: true,
            then: Yup.string().required('Required'),
            otherwise: Yup.string(), // Not required when `value` is false
          }),
        })
      ),
    }),
  })

  useEffect(() => {
    window.scrollTo(0, 0)
  }, [pathname])

  useEffect(() => {
    if (projectId) {
      fetchProjectById()
    }
  }, [projectId, signContractReload])

  useEffect(() => {
    if (projectTypeId !== '') {
      fetchProjectTypeById()
    }
  }, [projectTypeId])

  useEffect(() => {
    if (priceId !== '') {
      fetchMultipleProject()
    }
  }, [priceId, orderDetails])

  useEffect(() => {
    if (priceIdByProject !== '') {
      fetchPriceByPRoject()
    }
  }, [priceIdByProject])

  useEffect(() => {
    const finalPrice = calculatePrice(priceforProject?.totalPriceTotals, priceByProject, discount)
    setFinalPriceFrontEnd(finalPrice)
  }, [priceforProject?.totalPriceTotals, priceByProject, discount])

  useEffect(() => {
    if (upgradeOptionData?.length) {
      /**
       * upgradeOptionData: list of options
       * priceByProject?.tasks: task of project type from price
       * selectedItemsWorkOrder: _id of selected options
       */
      const arrayOfArrays =
        upgradeOptionData?.filter((v) => selectedItemsWorkOrder.includes(v._id))?.map((v) => v.selectedGroups) ?? []
      const combinedArray = Array.from(new Set(arrayOfArrays.flat()))
      const filteredTask = priceByProject?.tasks?.filter((v) => combinedArray?.includes(v.group))?.map((v) => v._id)
      setFilteredTaskForRemove(filteredTask)
      setSselectedUniqueGroups(combinedArray)
      console.log(
        { upgradeOptionData, selectedItemsWorkOrder, filteredTask, arrayOfArrays, combinedArray },
        priceByProject.tasks
      )
    }
  }, [projectType?._id, selectedItemsWorkOrder, upgradeOptionData])

  // useEffect(() => {
  //   if (hasValues(signContractDataFrontEnd) && hasValues(priceByProject)) {
  //     const { taxValue, jurisdictionState } = getSalesTax(
  //       signContractDataFrontEnd?.priceTotals?.jobTotal - roundTo2(100 * Number(discount) || 0),
  //       priceByProject?.state,
  //       priceByProject?.tax
  //     )
  //     setSalesTax({ taxValue, jurisdictionState })
  //   }
  // }, [signContractDataFrontEnd, priceByProject, discount])

  useEffect(() => {
    if (hasValues(signContractDataFrontEnd)) {
      const result = sumPricesAndPriceTotals(signContractDataFrontEnd, signContractProjectType, salesTax)
      console.log({ signContractProjectType, signContractDataFrontEnd, result })
      setPriceforProject(result)
    }
  }, [signContractProjectType, signContractDataFrontEnd, salesTax])
  console.log({ priceforProject })
  const fetchMultipleProject = async () => {
    try {
      const response = await getMultipleProject(oppId!, priceId)
      if (isSuccess(response)) {
        const { prices } = response?.data?.data

        const responseProjectTypes: { [key: string]: any } = {}
        const uniqueProjectTypeIds = new Set(prices.map((price: any) => price.projectType.id))

        // Fetch all project types in parallel
        await Promise.all(
          Array.from(uniqueProjectTypeIds).map(async (projectTypeId: any) => {
            const response = await getProjectTypeById(projectTypeId)
            responseProjectTypes[projectTypeId] = response?.data?.data?.projectType
          })
        )
        if (orderDetails?.projects?.length >= 2) {
          setInitialValues((pre) => ({
            ...pre,
            // colors: orderDetails?.colors ?? {},
            addOtherProjects: (orderDetails?.projects?.length >= 2 && true) || false,
            otherProjects: {
              types: prices.map((type: any) => ({
                ...type,
                value:
                  orderDetails?.projects?.find((v) => v?.projectId === type?.projectId?._id)?.projectTypeCheck || false,
                projectTypeColors: responseProjectTypes[type?.projectType?.id],
                colors: orderDetails?.projects?.find((v) => v?.projectId === type?.projectId?._id)?.colors,
                notes: orderDetails?.projects?.find((v) => v?.projectId === type?.projectId?._id)?.notes,
              })),
            },
            orderNotes: orderDetails?.projects?.[0]?.notes,
          }))
          const { optionsResult, selectedPackagesResult } = prices.reduce(
            (acc: any, project: any) => {
              const projectId = project?.projectId?._id
              const options = orderDetails?.projects?.find((v) => v?.projectId === projectId)?.chosenOptions
              const selectedPackages = orderDetails?.projects?.find((v) => v?.projectId === projectId)?.basePackage

              if (projectId) {
                acc.optionsResult[projectId] = options
                acc.selectedPackagesResult[projectId] = selectedPackages
              }

              return acc
            },
            { optionsResult: {}, selectedPackagesResult: {} }
          )
          console.log({ selectedPackagesResult, optionsResult, orderDetails })
          setMultipleProjectPackageId(selectedPackagesResult || {})
          setSelectedProjectOptions(optionsResult || {})
        } else {
          setInitialValues((prev) => ({
            ...prev,
            otherProjects: {
              ...prev.otherProjects,
              types: prices.map((type: any) => ({
                ...type,
                value: false,
                notes: type?.projectId?.notes,
                projectTypeColors: responseProjectTypes[type?.projectType?.id],
              })),
            },
          }))
        }

        // Optional: Call setMultipleProjectType if needed
        setMultipleProjectType(prices)
      }
    } catch (error) {
      console.log(error)
    }
  }

  const fetchPriceByPRoject = async () => {
    try {
      const response: any = await getPriceIdByProject(priceIdByProject)
      if (isSuccess(response)) {
        const updatedPriceByProject: any = { ...response.data.data.price }
        setPriceByProject(updatedPriceByProject)
        setPackageId(updatedPriceByProject?.packages?.[0]?._id)
      }
    } catch (error) {
      console.log(error)
    }
  }
  const fetchProjectById = async () => {
    try {
      const response = await getProjectById({ projectId: projectId, deleted: false })
      if (isSuccess(response)) {
        setProjectTypeId(response?.data?.data?.project?.projectType)
        setPriceIdByProject(response?.data?.data?.project?.priceId)
        setProjectById(response?.data?.data?.project)
        if (
          response?.data?.data?.project?.orderId !== undefined &&
          response?.data?.data?.project?.orderId !== null &&
          response?.data?.data?.project?.orderId !== ''
        ) {
          setIsOrderId(true)
          setOrderCallFirst(true)
          try {
            const res = await getOrderById(false, response?.data?.data?.project?.orderId!)
            if (isSuccess(res)) {
              setOrderDetails(res?.data?.data?.Order)
              // setManualChange(res?.data?.data?.Order?.projects?.[0]?.priceTotals?.discount || 0)
              setDiscount(res?.data?.data?.Order?.priceTotals?.discount || 0)
              setSelectedItemsWorkOrder(res?.data?.data?.Order?.projects?.[0]?.chosenOptions ?? [])
              setSelectedItems(res?.data?.data?.Order?.projects?.[0]?.chosenOptions ?? [])
              // setPackageId(res?.data?.data?.Order?.projects?.[0]?.basePackage ?? '')

              setInitialValues((pre) => ({
                ...pre,
                colors: res?.data?.data?.Order?.projects?.[0]?.colors ?? {},
                orderNotes: res?.data?.data?.Order?.projects?.[0]?.notes,
                discount: res?.data?.data?.Order?.priceTotals?.discount,
              }))
            } else {
              setOrderCallFirst(true)
            }
          } catch (error) {
            setOrderCallFirst(true)
            console.log('order by id fetch failed!', error)
          }
        } else {
          setInitialValues((pre) => ({
            ...pre,
            orderNotes: response?.data?.data?.project?.notes,
          }))
          setOrderCallFirst(true)
        }
      }
    } catch (error) {
      console.log(error)
    }
  }

  const fetchProjectTypeById = async () => {
    try {
      const response = await getProjectTypeById(projectTypeId)
      if (isSuccess(response)) {
        setProjectTypeData(response?.data?.data?.projectType)
        return response
      }
    } catch (error) {
      console.log(error)
    }
  }

  console.log({ selectedPackage, upgradePackageData })
  // newRoofUpgradeOptions
  useEffect(() => {
    if (orderCallFirst) {
      console.log({ selectedPackage, packageId })
      // if (isOrderId && orderDetails?.options?.upgradePackages?.length) {
      //   // console.log('without newRoofUpgradeOptions', isOrderId, orderDetails, orderDetails?.options?.chosenOptions)
      //   // setUpgradeOptionsData(orderDetails?.options?.upgradePackages)
      // } else
      if (hasValues(priceByProject) && hasValues(selectedPackage) && priceByProject?.tasks?.length) {
        const updatedCopy: any = JSON.parse(JSON.stringify(priceByProject))
        const selectedPackageCopy: any = JSON.parse(JSON.stringify(selectedPackage))
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        const res = upgradeOptions(updatedCopy, selectedPackageCopy, packageId)
        setUpgradeOptionsData(res)
        console.log('with newRoofUpgradeOptions', updatedCopy, res, selectedPackageCopy)
      }
    }
  }, [projectType, priceByProject, selectedPackage, orderCallFirst, isOrderId, orderDetails, packageId])

  useEffect(() => {
    if (orderCallFirst) {
      if (hasValues(priceByProject)) {
        const updatedCopy: any = JSON.parse(JSON.stringify(priceByProject))
        const upgradePackages = upgradePackagesMethod(updatedCopy)
        setUpgradePackageData(upgradePackages)
        console.log('with newRoofUpgradePackages', upgradePackages, updatedCopy)
      }
    }
  }, [projectType, priceByProject, orderCallFirst, isOrderId, orderDetails, packageId])

  useEffect(() => {
    let pckgId =
      isOrderId && orderDetails?.projects?.[0]?.basePackage ? orderDetails?.projects?.[0]?.basePackage : packageId
    if (isOrderId && pckgId) {
      setPackageId(pckgId)
    }
    const basePackage = upgradePackageData?.find((v) => v._id === pckgId)
    setSelectedPackage(basePackage)
  }, [upgradePackageData, packageId, isOrderId])

  useEffect(() => {
    try {
      if (
        projectId &&
        hasValues(projectType) &&
        hasValues(opportunity) &&
        hasValues(priceByProject) &&
        hasValues(projectById) &&
        taxModal.length
      ) {
        if (hasValues(selectedPackage)) {
          const priceByProjectCopy: any = JSON.parse(JSON.stringify(priceByProject))
          const selectedPackageCopy: any = JSON.parse(JSON.stringify(selectedPackage))
          const res = signContractMethod(
            {
              projectId: projectId!,
              chosenUpgrades: selectedItemsWorkOrder,
            },
            projectType,
            opportunity,
            priceByProjectCopy,
            projectById,
            taxModal,
            // discount,
            selectedPackageCopy,
            upgradeOptionData,
            filteredTaskForRemove,
            false
          )
          console.log({ signContractIndividual: res })
          setWorkOrder(res?.workOrder)
          setSignContractDataFrontEnd(res)
        }

        setLoading(false)
      } else {
        setLoading(false)
      }
    } catch (error) {
      setLoading(false)
    }
  }, [
    activePrice,
    opportunity,
    selectedItemsWorkOrder,
    selectedItems,
    projectType,
    priceByProject,
    selectedPackage,
    upgradePackageData,
    taxModal,
    // discount,
    projectTaskData,
    filteredTaskForRemove,
  ])

  useEffect(() => {
    if (oppId && projectId) {
      // fetchUnitsData()
      initFetchOpportunity()
      initPrices()
      fetchTax()
    }
  }, [])

  const initPrices = async () => {
    try {
      const res = await getPrices({ projectId: projectId!, deleted: false })
      if (isSuccess(res)) {
        const { prices } = res?.data?.data

        setPricesForManage(prices)
        const activePriceData = prices.filter((v: any) => v.active === true)
        setActivePrice(activePriceData[0])
        // setSelectedItemsWorkOrder(activePriceData[0].selectedPackages ?? [])
        // setSelectedItems(activePriceData[0].selectedPackages ?? [])
        setPriceId(activePriceData[0]._id ?? '')
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }
  const handleDeletePrices = async (_id: string) => {
    setBtnLoadingDeletePrice([...btnLoadingDeletePrice, _id])
    try {
      const confirm = window.confirm('Are you sure? This is permanent and cannot be undone.')
      if (confirm) {
        const res = await permDeletePrice({
          id: _id,
        })
        if (isSuccess(res)) {
          setBtnLoadingDeletePrice([...btnLoadingDeletePrice, _id])
          notify('Price deleted successfully!', 'success')
          initPrices()
          // fetchSignContract()
          // setProjectTaskData([])
          setSignContractReload((pre) => !pre)
          // fetchUpgradePackage()
        } else throw new Error(res?.data?.message)
      }
    } catch (error: any) {
      setBtnLoadingDeletePrice([...btnLoadingDeletePrice, _id])
      notify(error?.message ?? 'Failed to create price!', 'error')
    } finally {
      setBtnLoadingDeletePrice((prev) => prev.filter((id) => id !== _id)) // Remove _id from btnLoadingDeletePrice array
    }
  }

  const handleUpdatePrices = async (id: string) => {
    setBtnLoadingMakePrice([...btnLoadingMakePrice, id])

    try {
      const res = await updateActivePrice({
        projectId: projectId!,
        priceId: id,
      })
      if (isSuccess(res)) {
        notify('Price Activation updated successfully!', 'success')
        initPrices()
        // fetchSignContract()
        setSignContractReload((pre) => !pre)
        setProjectTaskData([])
        // fetchUpgradePackage()
        setBtnLoadingMakePrice(btnLoadingMakePrice.filter((id1: any) => id1 !== id))
      } else throw new Error(res?.data?.message)
    } catch (error: any) {
      setBtnLoadingMakePrice(btnLoadingMakePrice.filter((id1: any) => id1 !== id))
      notify(error?.message ?? 'Failed to updated price!', 'error')
    }
  }

  const handleAddPrices = async () => {
    setBtnLoadingAddPrice(true)

    try {
      const res = await createPrice({
        projectId: projectId!,
        createdBy: currentMember._id!,
        discount: Number(discount),
      })
      if (isSuccess(res)) {
        setBtnLoadingAddPrice(false)

        await updateActivity({
          id: oppId!,
          memberId: currentMember._id!,
          body: `created new Price for ${signContractDataFrontEnd?.name} project`,
          currDate: new Date().toISOString(),
        })

        notify('Price created successfully!', 'success')
        initPrices()
        // fetchSignContract()
        setSignContractReload((pre) => !pre)
        setProjectTaskData([])

        // fetchUpgradePackage()
      } else throw new Error(res?.data?.message)
    } catch (error: any) {
      setBtnLoadingAddPrice(false)
      notify(error?.message ?? 'Failed to create price!', 'error')
    }
  }

  const initFetchOpportunity = async () => {
    try {
      const response = await getOpportunityById({
        deleted: false,
        opportunityId: oppId!,
      })
      if (isSuccess(response)) {
        const { opportunity } = response?.data?.data
        setOpportunity(opportunity)
        setInitialValues((pre) => ({
          ...pre,
          paymentType: opportunity?.paymentType || '',
        }))
      } else throw new Error(response?.data?.message)
    } catch (err) {
      console.log('INIT FETCH ERR', err)
    }
  }

  const fetchTax = async () => {
    try {
      const res = await getTax()
      if (isSuccess(res)) {
        const { tax } = res?.data?.data
        setTaxUniqueState([...new Set(tax.map((item: any) => item.state))])
        setTaxModal(tax)
        // let uniqueStates = [...new Set(tax.map((item:any) => item.state))];
      } else throw new Error(res?.data?.message)
    } catch (error) {
      console.log('get tax fetch failed!', error)
    }
  }

  const handleToggleItemWorkOrder = (itemId: string) => {
    setSelectedItemsWorkOrder((prevSelectedItems: any) => {
      if (prevSelectedItems?.includes(itemId)) {
        return prevSelectedItems.filter((id: string) => id !== itemId)
      } else {
        return [...prevSelectedItems, itemId]
      }
    })
  }

  const handleKeyDown = (event: { key: string; preventDefault: () => void }) => {
    if (event.key === 'Enter') {
      event.preventDefault()
    }
  }

  const outSideBlur = async (discount: number) => {
    setDiscount(discount)
  }

  const handleSubmit = async (submittedValues: InitialValues) => {
    setBtnLoading(true)
    try {
      if (
        (opportunity?.taxJurisdiction === '' || !opportunity?.taxJurisdiction) &&
        taxUniqueState.some((state: any) => opportunity.state?.includes(state))
      ) {
        throw new Error('Must Select Tax Jurisdiction!')
      }

      const confirmed = window.confirm(
        'Please Confirm The Contract Is Signed\nTotal Investment: $' + formatNumberToCommaS(finalPriceFrontEnd?.total)
      )

      if (confirmed) {
        const orderObject: any = {}
        orderObject.projects = []

        orderObject.projectPriceId = signContractDataFrontEnd?.projectPriceId
        orderObject.projectId = projectId

        // submittedValues.projectType = signContractDataFrontEnd?.projectType
        orderObject.matList = signContractDataFrontEnd?.matList?.filter((itm: any) => itm?._id)

        //minimum price check
        const minimumPrice = priceByProject?.projectType?.typeMinimum ? priceByProject.projectType.typeMinimum * 100 : 0
        if (priceforProject?.totalPriceTotals?.jobTotal < minimumPrice) {
          if (priceforProject?.totalPriceTotals) {
            priceforProject.totalPriceTotals.jobTotal = minimumPrice
          }
        }

        //adding discount
        priceforProject.totalPriceTotals.discount = roundTo2(100 * Number(discount)) || 0
        priceforProject.totalPriceTotals.jobTotal -= priceforProject.totalPriceTotals.discount

        priceforProject.totalPriceTotals.commission -= roundTo2(
          priceByProject?.variables?.commission * priceforProject.totalPriceTotals.discount
        )
        priceforProject.totalPriceTotals.discountComm = roundTo2(
          priceByProject?.variables?.commission * priceforProject.totalPriceTotals.discount
        )

        //tax
        const { taxValue, jurisdictionState } = getSalesTax(
          priceforProject.totalPriceTotals.jobTotal,
          priceByProject?.state,
          priceByProject?.tax
        )
        priceforProject.totalPriceTotals.salesTax = roundTo2(taxValue / 100)

        if (priceforProject.totalPriceTotals.commission < 0) {
          priceforProject.totalPriceTotals.commission = 0
          priceforProject.totalPriceTotals.discountComm = 0
        } else {
          priceforProject.totalPriceTotals.commission = roundTo2(priceforProject.totalPriceTotals.commission / 100)
          priceforProject.totalPriceTotals.discountComm = roundTo2(priceforProject.totalPriceTotals.discountComm / 100)
        }

        // priceforProject.totalPriceTotals.commission = roundTo2(priceforProject.totalPriceTotals.commission / 100)
        // priceforProject.totalPriceTotals.discountComm = roundTo2(priceforProject.totalPriceTotals.discountComm / 100)

        priceforProject.totalPriceTotals.discount = roundTo2(priceforProject.totalPriceTotals.discount / 100)
        priceforProject.totalPriceTotals.jobTotal = roundTo2(priceforProject.totalPriceTotals.jobTotal / 100)
        priceforProject.totalPriceTotals.actRev = roundTo2(
          priceforProject.totalPriceTotals.jobTotal - priceforProject.totalPriceTotals.mTotal
        )
        //travel hourly rate for labor
        priceforProject.totalPriceTotals.travelHrlyRate = priceByProject?.variables?.travelHrlyRate

        orderObject.isUpdate = false
        orderObject.oppId = oppId
        orderObject.priceTotals = priceforProject?.totalPriceTotals
        orderObject.createdBy = currentMember._id

        const {
          lSubtotal,
          lTotal,
          mTotal,
          profit,
          overhead,
          jobTotal,
          grandTotal,
          cashTotal,
          jobCost,
          ...filteredPriceTotal
        } = signContractDataFrontEnd?.priceTotals
        orderObject.projects.push({
          // typeName: signContractDataFrontEnd?.typeName,
          type: signContractDataFrontEnd?.projectType,
          name: signContractDataFrontEnd?.name,
          notes: submittedValues.orderNotes,
          colors: submittedValues.colors2,
          basePackage: selectedPackage?._id,
          chosenOptions: selectedItemsWorkOrder,
          avgPitch: parseFloat(avgPitch(projectById)) || undefined,
          pitch: projectById?.customData?.pitch || undefined,
          workOrder: signContractDataFrontEnd?.workOrder,
          priceTotals: filteredPriceTotal,
          projectId: projectId,
          // manualDiscount: Number(manualChange),
        })

        if (hasValues(signContractProjectType)) {
          const combinedResult = {
            workOrder: [],
            matList: [],
          }
          for (const projectId in signContractProjectType) {
            if (signContractProjectType.hasOwnProperty(projectId)) {
              const project = signContractProjectType[projectId]
              const individualType = submittedValues?.otherProjects?.types?.find(
                (item: any) => item?.projectId?._id === projectId
              )
              // combinedResult.workOrder = combinedResult.workOrder.concat(project.workOrder)
              combinedResult.matList = combinedResult.matList.concat(project.matList)
              // submittedValues.extraProjects.addOtherProjectsCheck = submittedValues?.otherProjects?.value
              const {
                lSubtotal,
                lTotal,
                mTotal,
                profit,
                overhead,
                jobTotal,
                grandTotal,
                cashTotal,
                jobCost,
                ...filteredPriceTotal
              } = signContractProjectType[projectId]?.priceTotals
              orderObject.projects.push({
                // typeName: project?.typeName,
                type: individualType.projectType.id,
                name: project.name,
                notes: individualType?.notes || '',
                colors: individualType?.colors2,
                basePackage: multipleProjectPackageId[projectId],
                chosenOptions: selectedProjectOptions[projectId],
                avgPitch: parseFloat(avgPitch(individualType?.projectId)) || undefined,
                pitch: individualType?.projectId?.customData?.pitch || undefined,
                workOrder: project.workOrder,
                priceTotals: filteredPriceTotal || undefined,
                projectId: projectId,
                projectTypeCheck: individualType?.value,
              })
            }
          }
          orderObject.matList = combineDuplicateMaterials(
            signContractDataFrontEnd?.matList.concat(combinedResult.matList) || []
          )
          // orderObject.workOrder = combineDuplicateMaterials(
          //   signContractDataFrontEnd?.workOrder.concat(combinedResult.workOrder) || []
          // )
          console.log({ signContractProjectType })
        }

        const res = await createOrder(orderObject)
        if (isSuccess(res)) {
          await updateActivity({
            id: oppId!,
            memberId: currentMember._id!,
            body: `created Order for ${signContractDataFrontEnd?.name} project`,
            currDate: new Date().toISOString(),
          })

          const data = {
            oppId: signContractDataFrontEnd?.oppId,
            orderId: res?.data,
            soldValue: priceforProject?.totalPriceTotals?.jobTotal,
            realRevValue: priceforProject?.totalPriceTotals?.actRev,
            contactId: signContractDataFrontEnd?.client?._id,
            discount: Number(discount),
            paymentType: submittedValues.paymentType,
            projectId: projectId!,
          }
          await setAccepted(data)
          setBtnLoading(false)
          notify('Order created successfully!', 'success')
          navigate(`/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}/orderDetail/${res?.data}`)
        } else throw new Error(res?.data?.message)
      } else {
        setBtnLoading(false)
      }

      // console.log({ submittedValues, signContractDataFrontEnd }, orderObject)
      // if (confirmed) {

      // } else {
      //   setBtnLoading(false)
      // }
    } catch (error: any) {
      setBtnLoading(false)
      notify(error?.message ?? 'Failed to create order!', 'error')
    }
  }
  return loading ? (
    <>
      <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
      <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
      <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
    </>
  ) : (
    <Styled.SectionCont>
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        enableReinitialize
        validateOnChange={true}
        validationSchema={ContractSchema}
        validateOnBlur={false}
      >
        {({ touched, errors, values, setFieldValue }) => {
          useEffect(() => {
            if (values?.paymentType === 'Wells Fargo Financing') {
              setFieldValue('discount', 0)
              setDiscount(0)
            }
          }, [values.paymentType])

          const messages = []

          if (!opportunity?.duration || opportunity?.duration === 0) {
            messages.push('The duration information is missing.')
          }

          if (!companySettingForAll?.workingStates?.includes(opportunity?.state)) {
            messages.push('The opportunity state doesn’t match the required working state.')
          }

          if (opportunity?.orderId) {
            messages.push('Cannot accept a new Order when there is an existing Order.')
          }

          const disableForTax = !companySettingForAll?.workingStates?.includes(opportunity?.state)
            ? true
            : (opportunity?.taxJurisdiction === '' || !opportunity?.taxJurisdiction) &&
              taxUniqueState?.some((state: any) => opportunity?.state?.includes(state))
            ? true
            : priceByProject?.duration !== opportunity?.duration
            ? true
            : false
          console.log({ taxUniqueState }, discount)
          const avgPtch = avgPitch(projectById)
          return (
            <Form className="form" onKeyDown={handleKeyDown}>
              <SharedStyled.ContentHeader as={'p'} className="heading" textAlign="left">
                Client Info
              </SharedStyled.ContentHeader>
              <SharedStyled.FlexRow alignItems="start" margin="8px 0 14px 0">
                <SharedStyled.Text fontSize="14px" className="capitalize">
                  {opportunity?.client?.firstName} {opportunity?.client?.lastName}
                  {/* {opportunity?.firstName} {opportunity?.lastName} */}
                  <br />
                  {opportunity?.street}
                  <br />
                  {opportunity?.city}, {opportunity?.state} {opportunity?.zip}
                </SharedStyled.Text>
                &emsp;
                <SharedStyled.Text fontSize="14px">
                  {formatPhoneNumber(opportunity?.client?.phone, '') || ''}
                  {opportunity?.client?.phone && <br />}
                  {opportunity?.client?.email || ''}
                </SharedStyled.Text>
              </SharedStyled.FlexRow>

              <SharedStyled.FlexBox>
                <div style={{ width: '100%' }}>
                  <Styled.BoxGap margin="10px 0 0 0">
                    <SharedStyled.Text
                      textTransform="capitalize"
                      fontSize="28px"
                      fontWeight="700"
                      className="regular title"
                    >
                      {projectById?.name || '--'}
                    </SharedStyled.Text>

                    <SharedStyled.FlexBox>
                      <SharedStyled.Text fontSize="14px" className="regular" fontWeight="700">
                        {projectType?.name} Agreement
                      </SharedStyled.Text>
                    </SharedStyled.FlexBox>

                    <Styled.BoxGap margin="0 0 10px 0">
                      {projectType?.typeReplacement ? (
                        <SharedStyled.Text fontWeight="700">
                          {' '}
                          {projectById?.customData?.reroofAreas && parseFloat(avgPtch) ? (
                            <>Avg Pitch: {avgPtch}/12</>
                          ) : (
                            <></>
                          )}
                        </SharedStyled.Text>
                      ) : (
                        <SharedStyled.Text fontWeight="700">
                          {' '}
                          {projectById?.customData?.pitch > 0 && <>Pitch: {projectById?.customData?.pitch}/12</>}
                        </SharedStyled.Text>
                      )}
                    </Styled.BoxGap>
                  </Styled.BoxGap>
                  {workOrder
                    ?.sort?.((a: { order?: number }, b: { order?: number }) => sortByOrder(a, b))
                    ?.map(
                      (
                        { taskName, taskUnit, rawValue }: { taskName: string; taskUnit: string; rawValue: string },
                        index: number
                      ) => {
                        return (
                          <SharedStyled.SpaceBetween key={index}>
                            <div>
                              <span>{index + 1}.</span>&nbsp;
                              <SharedStyled.Text fontSize="14px">
                                {taskName}: {rawValue?.toFixed(2)} {taskUnit}
                              </SharedStyled.Text>
                            </div>
                          </SharedStyled.SpaceBetween>
                        )
                      }
                    )}
                  {/* {selectedObject.map(
                ({ tName, tUnit, tRawVal }: { tName: string; tUnit: string; tRawVal: string }, index: number) => {
                  return (
                    <div key={index}>
                      <SharedStyled.Text fontSize="14px">
                        <li>
                          {tName}: {tRawVal} {getUnitSymbolFromId(tUnit, units).split(' ')[0]}
                        </li>
                      </SharedStyled.Text>
                    </div>
                  )
                }
              )} */}

                  <>
                    {values?.otherProjects?.types?.map((v: any, index: number) => {
                      const projectId = v?.projectId?._id
                      const avgPtch = avgPitch(v?.projectId) || 0
                      return (
                        values?.otherProjects?.types?.[index]?.value && (
                          <Fragment key={projectId}>
                            <SharedStyled.HorizontalDivider />
                            <Styled.BoxGap margin="10px 0 0 0">
                              <SharedStyled.Text
                                textTransform="capitalize"
                                fontSize="28px"
                                fontWeight="700"
                                className="regular title"
                              >
                                {v?.projectId?.name || '--'}
                              </SharedStyled.Text>

                              <SharedStyled.FlexBox>
                                <SharedStyled.Text fontSize="14px" className="regular" fontWeight="700">
                                  {v?.projectType?.name} Agreement
                                </SharedStyled.Text>
                              </SharedStyled.FlexBox>

                              <Styled.BoxGap margin="0 0 10px 0">
                                {v?.projectType?.typeReplacement ? (
                                  <SharedStyled.Text fontWeight="700">
                                    {' '}
                                    {v?.projectId?.customData?.reroofAreas && parseFloat(avgPtch) ? (
                                      <>Avg Pitch: {avgPtch}/12</>
                                    ) : (
                                      <></>
                                    )}
                                  </SharedStyled.Text>
                                ) : (
                                  <SharedStyled.Text fontWeight="700">
                                    {' '}
                                    {v?.projectId?.customData?.pitch > 0 && (
                                      <>Pitch: {v?.projectId?.customData?.pitch}/12</>
                                    )}
                                  </SharedStyled.Text>
                                )}
                              </Styled.BoxGap>
                            </Styled.BoxGap>
                            {signContractProjectType[projectId]?.workOrder
                              ?.sort?.((a: { order?: number }, b: { order?: number }) => sortByOrder(a, b))
                              ?.map(
                                (
                                  {
                                    taskName,
                                    taskUnit,
                                    rawValue,
                                  }: { taskName: string; taskUnit: string; rawValue: string },
                                  index: number
                                ) => {
                                  return (
                                    <SharedStyled.SpaceBetween key={index}>
                                      <div>
                                        <span>{index + 1}.</span>&nbsp;
                                        <SharedStyled.Text fontSize="14px">
                                          {taskName}: {rawValue?.toFixed(2)} {taskUnit}
                                        </SharedStyled.Text>
                                      </div>
                                    </SharedStyled.SpaceBetween>
                                  )
                                }
                              )}
                          </Fragment>
                        )
                      )
                    })}
                  </>

                  <SharedStyled.FlexCol margin="16px 0 0 0" gap="4px">
                    <h3 style={{ fontSize: '14px' }}>Monthly Payment:</h3>

                    <SharedStyled.Text className="regular" fontSize="14px" margin="0px 0px 0px 10px">
                      <SharedStyled.TooltipContainer
                        width="250px"
                        positionLeft="4px"
                        positionBottom="0px"
                        positionLeftDecs="100%"
                        positionBottomDecs="100%"
                      >
                        <span className="tooltip-content">9.99% over 120 months</span>

                        <h1>${formatNumberToCommaS(finalPriceFrontEnd?.total * 0.0133)} / mo</h1>
                      </SharedStyled.TooltipContainer>
                    </SharedStyled.Text>
                  </SharedStyled.FlexCol>
                  <Styled.BoxGap margin="14px 0 0 0">
                    <CustomSelect
                      labelName="Payment Type"
                      stateName="paymentType"
                      dropDownData={['Cash/Check/ACH', 'Credit Card (3.5% fee)', 'Wells Fargo Financing']}
                      value={values.paymentType}
                      setValue={() => {}}
                      setFieldValue={setFieldValue}
                      error={touched.paymentType && errors.paymentType ? true : false}
                      innerHeight="52px"
                      margin="10px 0 0 0"
                    />

                    <SharedStyled.Text fontSize="14px" className="regular">
                      {discount > 0 &&
                        `Project Cost: $${formatNumberToCommaS(priceforProject?.totalPriceTotals?.grandTotal)}`}
                      <br />
                      {discount > 0 &&
                        `Discount: $${formatNumberToCommaS(discount)} (${calculateDiscountPercentage(
                          discount,
                          priceforProject?.totalPriceTotals?.grandTotal
                        )})`}{' '}
                      <br />
                      Project Subtotal: ${finalPriceFrontEnd?.subTotal?.toLocaleString()}
                      {/* Project Subtotal: ${signContractDataFrontEnd?.price?.subTotal?.toLocaleString()} */}
                      {/* Project Subtotal: ${formatNumberToCommaS(signContractDataFrontEnd?.price?.subTotal)}  */}
                      <br />
                      State Taxes: ${formatNumberToCommaS(finalPriceFrontEnd?.salesTax)}{' '}
                      {signContractDataFrontEnd?.price?.taxJurisdiction?.salesTax > 0 &&
                        `(${
                          signContractDataFrontEnd?.price?.taxJurisdiction?.city &&
                          signContractDataFrontEnd?.price?.taxJurisdiction?.city !== ''
                            ? `${signContractDataFrontEnd?.price?.taxJurisdiction?.city}, ${signContractDataFrontEnd?.price?.taxJurisdiction?.state}`
                            : `${signContractDataFrontEnd?.price?.taxJurisdiction?.state}`
                        }-${signContractDataFrontEnd?.price?.taxJurisdiction?.salesTax}%)`}
                      {/* State Taxes: ${formatNumberToCommaS(signContractDataFrontEnd?.price?.salesTax)}{' '}
                      {signContractDataFrontEnd?.price?.taxJurisdiction?.salesTax > 0 &&
                        `(${
                          signContractDataFrontEnd?.price?.taxJurisdiction?.city &&
                          signContractDataFrontEnd?.price?.taxJurisdiction?.city !== ''
                            ? `${signContractDataFrontEnd?.price?.taxJurisdiction?.city}, ${signContractDataFrontEnd?.price?.taxJurisdiction?.state}`
                            : `${signContractDataFrontEnd?.price?.taxJurisdiction?.state}`
                        }-${signContractDataFrontEnd?.price?.taxJurisdiction?.salesTax}%)`} */}
                      <br />
                      {console.log({ sfsdfsf: priceByProject?.duration }, opportunity?.duration)}
                      {companySettingForAll?.workingStates?.includes(opportunity?.state) ? (
                        <></>
                      ) : (
                        hasValues(opportunity) && (
                          <>
                            <SharedStyled.Warning>
                              The selected state in the opportunity is invalid. Please choose a valid state.
                            </SharedStyled.Warning>
                            <br />
                          </>
                        )
                      )}
                      {priceByProject?.duration === opportunity?.duration ? (
                        <></>
                      ) : (
                        <>
                          <SharedStyled.Warning>
                            Drive time has been changed from {priceByProject?.duration} to {opportunity?.duration} mins.
                            Please update price.
                            {/* The price duration does not match the opportunity duration. Please create a new price. */}
                          </SharedStyled.Warning>
                          <br />
                        </>
                      )}
                      {(opportunity?.taxJurisdiction === '' || !opportunity?.taxJurisdiction) &&
                      taxUniqueState?.some((state: any) => opportunity?.state?.includes(state)) ? (
                        <>
                          <SharedStyled.Warning>"MUST SELECT TAX JURISDICTION"</SharedStyled.Warning>
                          <br />
                        </>
                      ) : // companySettingForAll?.workingStates
                      null}
                      <b>Investment Total: ${finalPriceFrontEnd?.total?.toLocaleString()}</b>
                      {/* <b>Investment Total: ${signContractDataFrontEnd?.price?.total?.toLocaleString()}</b> */}
                      {/* <b>Investment Total: ${formatNumberToCommaS(signContractDataFrontEnd?.price?.total)}</b> */}
                    </SharedStyled.Text>
                  </Styled.BoxGap>
                  {/* {projectType?.typeReplacement ? ( */}
                  <>
                    <Styled.BoxGap margin="14px 0 0 0">
                      <SharedStyled.Text fontSize="21px">Payment Schedule</SharedStyled.Text>
                    </Styled.BoxGap>

                    <Styled.BoxGap margin="20px 0 0 0">
                      <SharedStyled.Text fontSize="14px">
                        {finalPriceFrontEnd?.paySchdl?.deposit > 0 && (
                          <>
                            {finalPriceFrontEnd?.paySchdl?.deposit}% Scheduling pmt: $
                            {finalPriceFrontEnd?.deposit?.toLocaleString()} <br />
                          </>
                        )}

                        {finalPriceFrontEnd?.paySchdl?.downPmt > 0 && (
                          <>
                            {finalPriceFrontEnd?.paySchdl?.downPmt}% Job Start pmt: $
                            {finalPriceFrontEnd?.downPmt?.toLocaleString()} <br />
                          </>
                        )}

                        {finalPriceFrontEnd?.paySchdl?.finalPmt > 0 && (
                          <>
                            {finalPriceFrontEnd?.paySchdl?.finalPmt}% Final pmt: $
                            {finalPriceFrontEnd?.finalPmt?.toLocaleString()}
                          </>
                        )}
                      </SharedStyled.Text>
                    </Styled.BoxGap>
                  </>
                  {/* ) : (
                    <>
                       <Styled.BoxGap margin="20px 0 0 0">
                        <SharedStyled.Text fontSize="21px">Payment Schedule</SharedStyled.Text>
                      </Styled.BoxGap>

                     <Styled.BoxGap margin="20px 0 0 0">
                        <SharedStyled.Text fontSize="14px">
                          {signContractDataFrontEnd?.price?.paySchdl?.deposit}% Deposit: $
                          {formatNumberToCommaS(signContractDataFrontEnd?.price?.deposit)} <br />
                          {signContractDataFrontEnd?.price?.paySchdl?.finalPmt}% Final Pmt: $
                          {formatNumberToCommaS(signContractDataFrontEnd?.price?.finalPmt)}
                        </SharedStyled.Text>
                      </Styled.BoxGap>
                    </>
                  )} */}
                  <Styled.BoxGap margin="20px 0 0 0">
                    {/* {values?.paymentType !== 'Wells Fargo Financing' && ( */}
                    <div style={{ width: '35%' }}>
                      <InputWithValidation
                        labelName="Promotion"
                        stateName="discount"
                        forceType="number"
                        onBlur={() => outSideBlur(values.discount)}
                        onWheel={(e: any) => {
                          e?.target?.blur()
                        }}
                        error={touched.discount && errors.discount ? true : false}
                      />
                    </div>
                    {/* )} */}
                    {/* <SharedStyled.NameText>Manual Discount</SharedStyled.NameText>
                    <SharedStyled.NameValueUnitContainer>
                      <SharedStyled.ValueInput
                        name={`discount`}
                        type="number"
                        value={values?.discount}
                        // onChange={(e: { target: { value: React.SetStateAction<number> } }) => {
                        //   setManualChange(Number(e.target.value))
                        // }}
                        onBlur={(e: { target: { value: React.SetStateAction<string> } }) =>
                          outSideBlur(Number(e.target.value))
                        }
                        onWheel={(e: any) => {
                          e.target.blur()
                        }}
                      />
                    </SharedStyled.NameValueUnitContainer> */}
                  </Styled.BoxGap>
                </div>

                <div style={{ width: '100%' }}>
                  <>
                    {upgradePackageData?.length ? (
                      <Styled.BoxGap margin="14px 0 0 0">
                        <SharedStyled.ContentHeader
                          textTransform={'uppercase'}
                          as={'p'}
                          className="heading"
                          textAlign="left"
                        >
                          Packages
                        </SharedStyled.ContentHeader>
                      </Styled.BoxGap>
                    ) : null}
                    <Styled.BoxGap>
                      {upgradePackageData
                        ?.sort?.((a: { order?: number }, b: { order?: number }) => sortByOrder(a, b))
                        ?.map(
                          (
                            {
                              _id,
                              name,
                              price,
                              description,
                            }: { _id: string; name: string; price: number; description: string },
                            index: number
                          ) => {
                            return (
                              <div key={index}>
                                <SharedStyled.HorizontalDivider />
                                <SharedStyled.SpaceBetween>
                                  <div>
                                    <SharedStyled.CheckboxZoneLabel margin={0}>
                                      <Field
                                        type="radio"
                                        name=""
                                        disabled={isOrderId}
                                        value={_id}
                                        id={_id}
                                        checked={packageId === _id}
                                        onChange={() => setPackageId(_id)}
                                      />
                                    </SharedStyled.CheckboxZoneLabel>

                                    <SharedStyled.Text className="regular" fontSize="14px" margin="0px 0px 0px 10px">
                                      {name}:
                                      <SharedStyled.TooltipContainer
                                        width="250px"
                                        positionLeft="4px"
                                        positionBottom="0px"
                                        positionLeftDecs="0px"
                                        positionBottomDecs="20px"
                                      >
                                        <span className="tooltip-content">{description}</span>
                                        <Styled.IButton>i</Styled.IButton>
                                      </SharedStyled.TooltipContainer>
                                    </SharedStyled.Text>
                                  </div>
                                  <div>
                                    {togglePrice ? (
                                      <SharedStyled.Text className="regular" fontSize="14px" margin="0px 10px 0px 0px">
                                        {`$${price?.toLocaleString()}`}
                                      </SharedStyled.Text>
                                    ) : (
                                      <SharedStyled.Text className="regular" fontSize="14px" margin="0px 10px 0px 0px">
                                        {packageId === _id ? `$${price?.toLocaleString()}` : null}
                                      </SharedStyled.Text>
                                    )}
                                  </div>
                                </SharedStyled.SpaceBetween>
                              </div>
                            )
                          }
                        )}
                    </Styled.BoxGap>
                  </>

                  <>
                    {upgradeOptionData?.length ? (
                      <Styled.BoxGap margin="14px 0 0 0">
                        <SharedStyled.ContentHeader
                          textTransform={'uppercase'}
                          as={'p'}
                          className="heading"
                          textAlign="left"
                        >
                          Options
                        </SharedStyled.ContentHeader>
                      </Styled.BoxGap>
                    ) : null}

                    <Styled.BoxGap>
                      {upgradeOptionData
                        ?.filter((v) => v?.taskPrices?.length > 0)
                        ?.sort?.((a: { order?: number }, b: { order?: number }) => sortByOrder(a, b))
                        ?.map(
                          (
                            {
                              _id,
                              name,
                              grandTotal,
                              description,
                              selectedGroups,
                              price,
                            }: {
                              _id: string
                              name: string
                              grandTotal: number
                              description: string
                              selectedGroups: string[]
                              price: number
                            },
                            index: number
                          ) => {
                            let optionCheck = false
                            if (selectedItemsWorkOrder.includes(_id)) {
                              optionCheck = false
                            } else {
                              if (selectedUniqueGroups.some((id) => selectedGroups.includes(id))) {
                                optionCheck = true
                              }
                            }

                            return (
                              <div key={index}>
                                <SharedStyled.HorizontalDivider />
                                <SharedStyled.SpaceBetween>
                                  <div>
                                    <SharedStyled.CheckboxZoneLabel margin={0}>
                                      <Field
                                        type="checkbox"
                                        name="selectedPackages"
                                        value={_id}
                                        id={_id}
                                        disabled={isOrderId || optionCheck}
                                        checked={selectedItemsWorkOrder?.includes(_id)}
                                        onChange={() => handleToggleItemWorkOrder(_id)}
                                      />
                                    </SharedStyled.CheckboxZoneLabel>
                                    <SharedStyled.Text className="regular" fontSize="14px" margin="0px 0px 0px 10px">
                                      {name}:
                                      <SharedStyled.TooltipContainer
                                        width="250px"
                                        positionLeft="4px"
                                        positionBottom="0px"
                                        positionLeftDecs="0px"
                                        positionBottomDecs="20px"
                                      >
                                        <span className="tooltip-content">{description}</span>
                                        <Styled.IButton>i</Styled.IButton>
                                      </SharedStyled.TooltipContainer>
                                    </SharedStyled.Text>
                                  </div>
                                  <div>
                                    <SharedStyled.Text className="regular" fontSize="14px" margin="0px 10px 0px 0px">
                                      {`$${price?.toLocaleString()}`}
                                    </SharedStyled.Text>
                                  </div>
                                </SharedStyled.SpaceBetween>
                              </div>
                            )
                          }
                        )}
                    </Styled.BoxGap>
                  </>

                  <SharedStyled.FlexCol margin="10px 0 -5px 0">
                    <SharedStyled.Text className="regular" fontWeight="700" fontSize="16px">
                      Notes:
                    </SharedStyled.Text>
                    <Styled.TextArea
                      component="textarea"
                      as={Field}
                      name="orderNotes"
                      margintop="5px"
                      height="42px"
                      width="25%"
                      className="textarea"
                      margin="0 0 0 20px"
                      error={touched.orderNotes && errors.orderNotes ? true : false}
                      // placeHolder="ie. Remove existing shingle,flashing, and underlayment; then install new ice shield, flashing, and shingle to match"
                    />
                    {touched.orderNotes && errors.orderNotes && (
                      <Styled.ErrorMsg>
                        <ErrorMessage name={'orderNotes'} />
                      </Styled.ErrorMsg>
                    )}
                  </SharedStyled.FlexCol>

                  {projectType?.priceColor?.length ? (
                    <>
                      <Styled.BoxGap margin="5px 0 -8px 0">
                        <SharedStyled.Text fontWeight="700" className="regular">
                          Colors:
                        </SharedStyled.Text>
                      </Styled.BoxGap>
                      {projectType?.priceColor?.map((v: any, index: number) => {
                        // Extract the relevant color value based on the comparison
                        const colorValue =
                          Object.entries(values?.colors || {}).find(([key, value]) => {
                            const [colorId] = key.split('@')
                            return colorId === v._id
                          })?.[1] || ''

                        const localColorValue =
                          Object.entries(values?.colors2 || {}).find(([key, value]) => {
                            const [colorId] = key.split('@')
                            return colorId === v._id
                          })?.[1] || ''
                        console.log({ localColorValue, colorValue }, v?.colors)
                        return (
                          <SharedStyled.FlexBox margin="0 0 0 20px" key={index} alignItems="center" className="grid">
                            {/* <SharedStyled.Text className="regular" fontSize="14px">
                              {v.name}:
                            </SharedStyled.Text> */}
                            <AutoComplete
                              labelName={v.name}
                              error={false}
                              value={localColorValue ? localColorValue : colorValue}
                              options={
                                v?.colors
                                  ?.map((item: string) => item)
                                  ?.includes(localColorValue ? localColorValue : colorValue)
                                  ? v?.colors?.map((item: string) => item)
                                  : [
                                      ...v?.colors?.map((item: string) => item),
                                      localColorValue ? localColorValue : colorValue,
                                    ]
                              }
                              // setValue={setColorDripEdge}
                              setFieldValue={setFieldValue}
                              // innerHeight="40px"
                              // margin="10px 0 0 0"
                              stateName={`colors2.${v?._id}@${v?.name}`}
                              // maxWidth="250px"
                              // fontSize="14px"
                              className="colorDropdown"
                            />
                          </SharedStyled.FlexBox>
                        )
                      })}
                    </>
                  ) : null}
                  <>
                    {values?.otherProjects?.types?.length ? (
                      <OtherProjects
                        setFieldValue={setFieldValue}
                        values={values}
                        isOrderId={isOrderId}
                        orderCallFirst={orderCallFirst}
                        projectType={projectType}
                        orderDetails={orderDetails}
                        taxModal={taxModal}
                        // discount={discount}
                        opportunity={opportunity}
                        setSignContractProjectType={setSignContractProjectType}
                        setMultipleProjectPackageId={setMultipleProjectPackageId}
                        multipleProjectPackageId={multipleProjectPackageId}
                        setSelectedProjectOptions={setSelectedProjectOptions}
                        selectedProjectOptions={selectedProjectOptions}
                        multipleProjectType={multipleProjectType}
                        togglePrice={togglePrice}
                        touched={touched}
                        errors={errors}
                      />
                    ) : null}
                  </>
                </div>
              </SharedStyled.FlexBox>

              <SharedStyled.FlexRow>
                <SharedStyled.FlexRow margin="10px 0" gap="16px">
                  <Button
                    maxWidth="200px"
                    type="submit"
                    disabled={opportunity?.orderId || disableForTax}
                    positionLeftDecs={'100%'}
                    tooltip={
                      !(opportunity?.orderId || disableForTax) ? (
                        ''
                      ) : (
                        <>
                          {messages.length > 0 && (
                            <ul style={{ padding: '0' }}>
                              {messages.map((msg, index) => (
                                <span>{msg}</span>
                              ))}
                            </ul>
                          )}
                        </>
                      )
                    }
                    tooltipWidth={'230px'}
                    isLoading={btnLoading}
                  >
                    Accept
                  </Button>

                  <Button
                    onClick={() => navigate(`/${operationsFlag ? 'operations' : 'sales'}/opportunity/${oppId}`)}
                    className="gray"
                    type="button"
                    width="max-content"
                  >
                    Back
                  </Button>
                </SharedStyled.FlexRow>
                <Button
                  maxWidth="200px"
                  type="button"
                  className="gray"
                  onClick={() => {
                    setManagePrice(!managePrice)
                  }}
                  disabled={opportunity?.orderId}
                >
                  Manage Prices
                </Button>
              </SharedStyled.FlexRow>

              <SharedStyled.HorizontalDivider />

              {managePrice && (
                <>
                  <Toggle
                    title="Show all prices"
                    customStyles={{ margin: '16px', justifyContent: 'flex-start' }}
                    isToggled={togglePrice}
                    onToggle={() => {
                      setTogglePrice((prev) => !prev)
                    }}
                  />
                  <Styled.StakeTable>
                    <Styled.TableHead>
                      <Styled.TableRow>
                        <Styled.Tableheading className="name">Created date</Styled.Tableheading>
                        <Styled.Tableheading className="unit">Created by</Styled.Tableheading>
                        <Styled.Tableheading className="description"></Styled.Tableheading>
                      </Styled.TableRow>
                    </Styled.TableHead>
                    <Styled.TableBody>
                      {pricesManage &&
                        pricesManage.map(
                          (
                            {
                              createdByUser,
                              createdAt,
                              active,
                              _id,
                            }: { createdByUser: any; createdAt: string; active: boolean; _id: string },
                            index: number
                          ) => {
                            return (
                              <Styled.TableRow type={index} key={index}>
                                <Styled.TableData className="name">
                                  {new Date(createdAt).toLocaleDateString('en-US', {
                                    month: '2-digit',
                                    day: '2-digit',
                                    year: 'numeric',
                                  })}
                                  &nbsp;
                                  {new Date(createdAt).toLocaleTimeString('en-US', {
                                    hour: '2-digit',
                                    minute: 'numeric',
                                  })}
                                </Styled.TableData>
                                <Styled.TableData>{`${createdByUser?.[0]?.firstName ?? '--'} ${
                                  createdByUser?.[0]?.lastName ?? '--'
                                }`}</Styled.TableData>
                                <Styled.TableData className="description">
                                  <SharedStyled.FlexBox flexWrap="wrap" gap="10px">
                                    {!active && (
                                      <Button
                                        className="delete"
                                        width="max-content"
                                        type="button"
                                        onClick={() => handleDeletePrices(_id)}
                                        isLoading={btnLoadingDeletePrice?.includes(_id)}
                                      >
                                        Delete Price
                                      </Button>
                                    )}
                                    &nbsp;
                                    {!active && (
                                      <Button
                                        width="max-content"
                                        type="button"
                                        onClick={() => handleUpdatePrices(_id)}
                                        isLoading={btnLoadingMakePrice?.includes(_id)}
                                      >
                                        Make Active
                                      </Button>
                                    )}
                                  </SharedStyled.FlexBox>
                                </Styled.TableData>
                              </Styled.TableRow>
                            )
                          }
                        )}
                    </Styled.TableBody>
                  </Styled.StakeTable>
                  <Button
                    maxWidth="200px"
                    type="button"
                    onClick={() => {
                      handleAddPrices()
                    }}
                    isLoading={btnLoadingAddPrice}
                  >
                    Add New Price
                  </Button>
                </>
              )}
            </Form>
          )
        }}
      </Formik>
    </Styled.SectionCont>
  )
}

export default Contract
