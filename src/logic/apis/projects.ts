import { AxiosInstance } from '.'
import { ColorCategory, ProjectTypeGroups } from '../../modules/projectTypes/components/projectTypeModal/constant'
import { filterUndefinedAndNull, notify, simplifyBackendError } from '../../shared/helpers/util'

interface IGetUnits {
  deleted: boolean
  limit?: number
}
interface IGetTask {
  deleted: boolean
  limit?: number
  group?: string[]
  search?: string
}
interface IGetPackage {
  deleted: boolean
  limit?: number
  packageId?: string
}
interface IGetUnitsById {
  deleted: boolean
  unitId: string
}
interface ICreateUnits {
  name: string
  symbol: string

  createdBy: string
}
interface IDeleteUnit {
  id: string
}
interface IDeleteTax {
  id: string
}
interface IRestoreUnit {
  id: string
}
interface IUpdateUnit {
  name: string
  symbol: string

  createdBy: string
  unitId: string
}
interface IUpdateTax {
  name: string
  rate: 0

  createdBy?: string
  taxId?: string
  city: string
  state: string
  salesTax?: number
}
interface ICreateInput {
  name: string
  unit: string
  projectType: string
  orderNumber: string

  createdBy: string
}
interface IDeleteInput {
  id: string
}
interface IRestoreInput {
  id: string
}
interface IUpdateInput {
  name: string
  unit: string
  projectType: string
  orderNumber: number

  createdBy: string
  inputId: string
}
interface IGetInputs {
  deleted: boolean
  unit?: string
  type?: string
  isNew?: boolean
}
interface IGetInputsById {
  inputId: string
  deleted: boolean
}
interface ICreateProjectCategory {
  name: string

  createdBy: string
}
interface IGetProjectTypes {
  opportunityId?: string
  deleted: boolean
}
interface ICreateProjectType {
  name: string
  minTravelPpl: number
  asbTestRequired: boolean
  permitRequired: boolean
  description: string
  typeMinimum: number
  markup?: number
  deposit?: number
  downPmt?: number
  // usesPackages: boolean
  createdBy: string
  colorCode?: string
  usesPitch?: boolean
  pitchMod?: any
  groups?: ProjectTypeGroups[]
  questions?: string[]
  priceColor?: ColorCategory[]
  salesCommision?: {
    commission: number
    jobSale: number
    jobStart: number
    jobCompletion: number
  }
}
interface IUpdateProjectType {
  minTravelPpl: number
  name: string
  asbTestRequired: boolean
  permitRequired: boolean
  description: string
  typeMinimum: number
  markup?: number
  deposit?: number
  downPmt?: number
  // usesPackages: boolean
  createdBy: string
  projectTypeId: string
  colorCode?: string
  usesPitch?: boolean
  pitchMod?: any
  groups?: ProjectTypeGroups[]
  questions?: string[]
  priceColor?: ColorCategory[]
  salesCommision?: {
    commission: number
    jobSale: number
    jobStart: number
    jobCompletion: number
  }
}
interface IGetMaterials {
  deleted: boolean
  limit?: number
  search?: string
  categoryId?: string
  subCategoryId?: string
}
interface ICreateCrewPosition {
  title: string
  rate: string

  createdBy: string
  // editable: boolean
}
interface IGetCrewPosition {
  deleted: boolean
}
interface ICreateMaterial {
  name: string
  categoryId: string
  subCategoryId: string
  description: string
  unitId: string
  cost: number
  vendor: number
  inv: boolean
  createdBy: string
}
interface IDeleteCategory {
  id: string
}
interface IRestoreCategory {
  id: string
}
interface IUpdateCategory {
  name: string

  createdBy: string
  categoryId: string
}
interface IGetCategoryById {
  categoryId: string
  deleted: boolean
}
interface ICreateSubCategory {
  name: string
  categoryId: string

  createdBy: string
}
interface IUpdateSubCategory {
  name: string
  categoryId: string

  createdBy: string
  subCategoryId: string
}
interface IGetSubCategoryById {
  deleted: boolean
  subCategoryId: string
}
interface IDeleteMaterials {
  id: string
}
interface IUpdateMaterials {
  name: string
  categoryId: string
  subCategoryId: string
  description: string
  unitId: string
  cost: number
  vendor: string
  inv: boolean
  createdBy: string
  materialId: string
}
interface IRestoreMaterials {
  id: string
}
interface IUpdateCrewPositions {
  title: string
  rate: number

  createdBy: string
  crewPositionId: string
  // editable: boolean
}
interface IGetSubCategory {
  deleted: boolean
}
interface I_ProjectInputs {
  name: string
  unit: string
  value: number | undefined
  _id: string
  desc: string
}
interface I_RoofAreas {
  areaId?: string
  install?: string
  remove?: string
  size?: number
  name: string
  pitch: number
  // size: number
  layers: string
  ventArea: string
  rmvPly: string
  instPly: string
  noAccess: string
  twoStory: string
}
interface I_Chimneys {
  width: string
  length: string
}
interface I_SkyLights {
  width: string | number
  length: string | number
  skylightType: string
}
interface I_CreateProject {
  clientId: string | undefined | undefined
  createdBy: string | undefined
  name: string
  newProject: boolean
  notes: string
  oppId: string | undefined
  projectInputs: I_ProjectInputs[]
  projectType: string
  customData?: {
    roofAreas: I_RoofAreas[]
    bVents: string[] | number[]
    stemVents: string[] | number[]
    sunTunnels: string[] | number[]
    chimneys: I_Chimneys[]
    skylights: I_SkyLights[]
    eaves: string
    sideWall: string
    endWall: string
    ridges: string
    rakes: string
    valleys: string
    hips: string
    pitchChange: string
    splitPipe2: string
    pipeFlash4: string
    pipeFlash123: string
    canVentsExist: string | number
    ridgeVentExist: string | number
  }
}
interface I_CreatePackages {
  name: string
  description: string
  // group: string
  order: number
  upsell: number
  type: string
  taskArray: string[]
  // replaceTask: string[]
  deleted: boolean
}
interface I_UpdatePackages {
  name: string
  description: string
  // group: string
  order: number
  upsell: number
  type: string
  default: boolean
  taskArray: string[]
  // replaceTask: string[]
  deleted: boolean

  packageId: string
}
interface I_InputObject {
  iNum: string
  input: string
  oper: string
  name: string
}
interface I_MaterialObject {
  tMat: string
  mat: string
  cov: string
  name: string
}
interface I_LaborObject {
  tLabor: string
  worker: string
  time: number
  mod: string
  name: string
}
interface I_CreateTask {
  type: string
  name: string
  description: string
  unit: string
  unitName: string
  input: I_InputObject[]
  material: I_MaterialObject[]
  labor: I_LaborObject[]
  waste: number
  createdBy: string | undefined
  active?: boolean
}

interface I_UpdateTask {
  type: string
  name: string
  description: string
  unit: string
  unitName: string
  input: I_InputObject[]
  material: I_MaterialObject[]
  labor: I_LaborObject[]
  waste: number
  createdBy: string | undefined
  taskId: string
}
interface I_DeleteTask {
  id: string
}
interface I_PackageTask {
  id: string
}
interface I_ProjectById {
  projectId: string | undefined
  deleted: boolean
}
interface I_ProjectTask {
  projectId: string | undefined
}
interface I_TaskGroupsInUse {}
interface I_SignContract {
  chosenUpgrades: string[]

  projectId: string | undefined
  manualChange?: string
}
interface I_CreateOrder {
  oppId: string
  projectId: string
  projectPriceId: string
  projectName: string
  projectType: string
  // shingleColor: string
  // dripEdgeColor: string
  // flashingColor: string
  // gutterColor: string
  orderNotes: string
  priceTotals: {}
  matList: string[]
  workOrder: string[]
  createdBy: string
  isUpdate: boolean
  options?: any
  colors: any
  orderId?: string
  projects?: any
}
interface I_SetAccepted {
  oppId: string
  orderId: string
  soldValue: string
  realRevValue: string
  clientId: string
  paymentType: string
  discount?: number
  projectId: string
}

interface I_ProjectSearch {
  search?: string
}
interface I_ProjectReportProps {
  oppId: string
  projectId: string
}
interface I_ProjectCrewReportProps {
  oppId: string
}
interface ActualTotals {
  actualPrice: number
  actualLaborCost: number
  actualDays: number
  subcontractorCost: number
  inventoryCost: number
  qbCOGS: number
  other: number
  receipts: number
  actualMatCost: number
}

interface I_UpdateOrder {
  orderId: string

  projectId: string
  // actualTotals?: ActualTotals
  colors?: any
  notes?: string
  originalMatList?: boolean
}
interface I_UpdatePrice {
  discount?: number
  selectedPackage?: string[]
  priceId: string
}
interface I_CreatePRice {
  projectId: string
  createdBy: string
  discount?: number
  selectedPackage?: string[]
}
export const getPrices = async ({ projectId, deleted }: { projectId: string; deleted: boolean }) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/price/projectId/${projectId}/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('failed to fetch get price', error)
  }
}

export const updatePrice = async (data: I_UpdatePrice) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/update-price`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.log('price creation failed', error)
    return error?.response
  }
}

export const createPrice = async (data: I_CreatePRice) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/project/create-price`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.log('price creation failed', error)
    return error?.response
  }
}

export const updateActivePrice = async ({ projectId, priceId }: { projectId: string; priceId: string }) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(
      `/project/price-activate/projectId/${projectId}/priceId/${priceId}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.log('set accepted failed', error)
    return error?.response
  }
}

export const deletePrice = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/project/delete-price`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error) {
    console.log('price deletion failed', error)
  }
}

export const permDeletePrice = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/project/perm-delete-price`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error) {
    console.log('price deletion failed', error)
  }
}

export const restorePrice = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/project/restore-price`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('price restore failed', error)
  }
}

export const getTax = async () => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-tax/deleted/false`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        limit: 100,
      },
    })
    return response
  } catch (error) {
    console.log('failed to fetch get tex', error)
  }
}

export const permDeleteOrder = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/project/delete-order`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.log('order deletion failed', error)
  }
}
export const getOrderById = async (deleted: boolean, orderId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-order-by-id/orderId/${orderId}/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('failed to fetch work order by id', error)
  }
}

export const getOrder = async (deleted: boolean, oppId?: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-order`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        deleted: deleted,
        oppId: oppId,
      },
    })
    return response
  } catch (error) {
    console.log('failed to fetch work order', error)
  }
}

export const getWorkOrder = async (data: I_SignContract) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/roof-replacement-work-order&projectId=${data.projectId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        chosenUpgrades: data.chosenUpgrades.join(),
      },
    })
    return response
  } catch (error) {
    console.log('failed to fetch work order', error)
  }
}

export const setAccepted = async (data: I_SetAccepted) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/project/setAccepted`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.log('set accepted failed', error)
    return error?.response
  }
}

export const updateMaterialOrder = async (data: any[], orderId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(
      `/project/update-materials-for-order/${orderId}`,
      { matList: data },
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.log('update materials for order', error)
    return error?.response
  }
}

export const AddMaterialToOrder = async (data: any, orderId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/project/add-materials-to-order/${orderId}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.log('add materials to order', error)
    return error?.response
  }
}

export const createOrder = async (data: I_CreateOrder) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/project/create-order`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.log('order creation failed', error)
    return error?.response
  }
}

export const getPriceIdByProject = async (priceId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/price-by-id/priceId/${priceId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('failed to fetch price id by project', error)
  }
}

export const getProjectTypeById = async (projectTypeId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(
      `/project/get-project-type-by-id/projectTypeId/${projectTypeId}/deleted/false`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error) {
    console.log('failed to fetch project type', error)
  }
}

export const getSignContract = async (data: I_SignContract) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/sign-contract&projectId=${data.projectId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        chosenUpgrades: data.chosenUpgrades.join(),
        manualChange: data.manualChange,
      },
    })
    return response
  } catch (error) {
    console.log('failed to fetch sign contract', error)
  }
}
export const getUpgradePackage = async (data: I_ProjectTask) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/roof-replacement-upgrade-package`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: data,
    })
    return response
  } catch (error) {
    console.log('failed to fetch project task', error)
  }
}

export const getProjectTask = async (data: I_ProjectTask) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/project-tasks/projectId/${data.projectId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('failed to fetch project task', error)
  }
}

export const getTaskGroupsInUse = async (data: I_TaskGroupsInUse) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/task-groups-in-use`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.log('failed to fetch task groups', error)
  }
}

export const updateOption = async (data: any) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/project/update-option`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('option updation failed', error)
  }
}

export const createOption = async (data: any) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/project/create-option`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('option creation failed', error)
  }
}

export const deleteOption = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/project/delete-option`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.log('project creation failed', error)
  }
}
export const permDeleteOption = async (data: I_PackageTask) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/project/perm-delete-option`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error) {
    console.log('option deletion failed', error)
  }
}

export const restoreOption = async (data: I_PackageTask) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/project/restore-option`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('option restoration failed', error)
  }
}

export const getOptionApi = async (data: any, type?: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-option/deleted/${data.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        limit: data.limit,
        type: type || undefined,
      },
    })
    return response
  } catch (error: any) {
    return error?.response
  }
}

export const getOptionByIdApi = async (data: any) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(
      `/project/get-option-by-id/OptionId/${data.optionId}/deleted/${data.deleted}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
        params: {
          limit: data.limit,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('option by id error', error)
    return error?.response
  }
}

export const restoreProject = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/project/restore-project`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('project restore failed', error)
  }
}

export const deleteProject = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/project/delete-project`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error) {
    console.log('project creation failed', error)
  }
}
export const permDeleteProject = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/perm-delete-project`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}
export const getProjectById = async (data: I_ProjectById) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(
      `/project/get-project-by-id/projectId/${data.projectId}/deleted/${data.deleted}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error) {
    console.log('failed to fetch project by id', error)
  }
}

// export const getMultipleProject = async ( oppId: string, projectType: string) => {
//   try {
//     const token: any = localStorage.getItem('token')
//     const response = await AxiosInstance.get(
//       `/project/price-by-project-type/oppId/${oppId}/projectType/${projectType}`,
//       {
//         headers: {
//           Authorization: `Bearer ${JSON.parse(token)}`,
//         },
//       }
//     )
//     return response
//   } catch (error) {
//     console.log('failed to fetch multiple project', error)
//   }
// }

export const getMultipleProject = async (oppId: string, priceId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/opp-all-active-price/oppId/${oppId}/priceId/${priceId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('failed to fetch multiple project', error)
  }
}

export const createProject = async (data: I_CreateProject) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/project/upsert-project`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.log('project creation failed', error)
  }
}

export const checkProject = async (projectId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/project/check-project-accepted/projectId/${projectId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('project acception failed', error)
  }
}

export const getProject = async (data: IGetProjectTypes, skip?: number) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(
      `/project/get-project/oppId/${data.opportunityId}/deleted/${data.deleted}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
        params: {
          skip: skip,
          limit: 30,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('getProject error', error)
    return error?.response
  }
}

export const deletePackages = async (data: I_PackageTask) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/project/delete-package`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error) {
    console.log('package deletion failed', error)
  }
}

export const permDeletePackages = async (data: I_PackageTask) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/project/perm-delete-package`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error) {
    console.log('package deletion failed', error)
  }
}

export const restorePackages = async (data: I_PackageTask) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/project/restore-package`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('package restoration failed', error)
  }
}

export const updatePackages = async (data: I_UpdatePackages) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/project/update-package`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('package updation failed', error)
  }
}

export const createPackages = async (data: I_CreatePackages) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/project/create-package`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('package creation failed', error)
  }
}

export const getPackageApi = async (data: IGetPackage, type?: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-package/deleted/${data.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        limit: data.limit,
        type: type || undefined,
      },
    })
    return response
  } catch (error: any) {
    console.error('package error', error)
    return error?.response
  }
}

export const getPackageByIdApi = async (data: IGetPackage) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(
      `/project/get-package-by-id/packageId/${data.packageId}/deleted/${data.deleted}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
        params: {
          limit: data.limit,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('package by id error', error)
    return error?.response
  }
}

export const permDeleteTasks = async (data: I_PackageTask) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/project/perm-delete-task`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error) {
    console.log('Task deletion failed', error)
  }
}

export const restoreTasks = async (data: I_PackageTask) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/project/restore-task`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log('Task restoration failed', error)
  }
}

export const createTask = async (data: I_CreateTask) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/project/create-task`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.log('Task creation failed', error)
  }
}

export const updateTask = async (data: I_UpdateTask) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/update-task`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.log('Task updation failed', error)
  }
}

export const deleteTask = async (data: I_DeleteTask) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/project/delete-task`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.log('Task deletion failed', error)
  }
}

export const getTaskApi = async (dataObj: IGetTask, type?: string) => {
  const data = filterUndefinedAndNull(dataObj)

  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-task/deleted/${data.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        limit: data?.limit,
        type,
        group: data?.group,
        search: data?.search,
      },
    })
    return response
  } catch (error: any) {
    console.error('getWeeklySalesReport error', error)
    return error?.response
  }
}

export const getTaskById = async (data: { taskId: string; deleted: boolean }) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-task-by-id/taskId/${data.taskId}/deleted/${data.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getWeeklySalesReport error', error)
    return error?.response
  }
}

export const getUnitsApi = async (data: IGetUnits) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-unit/deleted/${data.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        limit: data.limit || 100,
      },
    })
    return response
  } catch (error: any) {
    console.error('getWeeklySalesReport error', error)
    return error?.response
  }
}

export const getUnitsById = async (data: IGetUnitsById) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(
      `/project/get-unit-by-id/unitId/${data.unitId}/deleted/${data.deleted}?limit=1000`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('getWeeklySalesReport error', error)
    return error?.response
  }
}

export const createUnit = async (body: ICreateUnits) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/project/create-unit`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}

export const deleteUnit = async (data: IDeleteUnit) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/delete-unit`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}

export const permDeleteUnit = async (data: IDeleteUnit) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/perm-delete-unit`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}

export const restoreUnit = async (data: IRestoreUnit) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/restore-unit`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}

export const updateUnit = async (data: IUpdateUnit) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/update-unit`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}

// Input

export const createInput = async (body: ICreateInput) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/project/create-input`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}

export const deleteInput = async (data: IDeleteInput) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/delete-input`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}

export const permDeleteInput = async (data: IDeleteInput) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/perm-delete-input`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}

export const restoreInput = async (data: IRestoreInput) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/restore-input`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}

export const updateInput = async (data: IUpdateInput) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/update-input`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}

export const getProjectInputsApi = async (projectTypeId: string, projectId?: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/project-inputs`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        projectType: projectTypeId,
        projectId: projectId,
      },
    })
    return response
  } catch (error) {
    console.log(error)
  }
}

export const getInputsApi = async (data: IGetInputs) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-input/deleted/${data.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },

      params: {
        limit: 1000,
        unit: data.unit && data.unit.trim() !== '' ? data.unit : '',
        projectType: data.type && data.type.trim() !== '' ? data.type : '',
        isNew: data.isNew ? true : '',
      },
    })
    return response
  } catch (error: any) {
    console.error('getWeeklySalesReport error', error)
    return error?.response
  }
}

export const getInputsById = async (data: IGetInputsById) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(
      `/project/get-input-by-id/inputId/${data.inputId}/deleted/${data.deleted}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('getWeeklySalesReport error', error)
    return error?.response
  }
}

// Category

export const createProjectCategory = async (body: ICreateProjectCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/project/create-category`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}
export const getCatergoryApi = async (data: IGetProjectTypes) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-category/deleted/${data.deleted}?limit=1000`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getCatergoryApi error', error)
    return error?.response
  }
}
export const getCatergoryById = async (data: IGetCategoryById) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(
      `/project/get-category-by-id/categoryId/${data.categoryId}/deleted/${data.deleted}?limit=1000`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('getCatergoryApi error', error)
    return error?.response
  }
}
export const deleteCategory = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/delete-category`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}
export const deleteCategoryPerm = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/perm-delete-category`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}
export const restoreCategory = async (data: IRestoreCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/restore-category`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}
export const updateCategory = async (data: IUpdateCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/update-category`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}
export const getSubCatergoryApi = async (data: IGetSubCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-sub-category/deleted/${data.deleted}?limit=1000`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getSubCatergoryApi error', error)
    return error?.response
  }
}
export const getSubCatergoryById = async (data: IGetSubCategoryById) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(
      `/project/get-category-by-id/subCategoryId/${data.subCategoryId}/deleted/${data.deleted}?limit=1000`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('getSubCatergoryApi error', error)
    return error?.response
  }
}
export const createSubCategory = async (body: ICreateSubCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/project/create-sub-category`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('createSubCategory error', error)
    return error?.response
  }
}
export const deleteSubCategory = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/delete-sub-category`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}
export const deleteSubCategoryPerm = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/perm-delete-sub-category`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}
// same data as category
export const restoreSubCategory = async (data: IRestoreCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/restore-sub-category`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}
export const updateSubCategory = async (data: IUpdateSubCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/update-sub-category`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}

// Project Types

export const getProjectTypes = async (data: IGetProjectTypes) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-project-type/deleted/${data.deleted}?limit=1000`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getProjectTypes error', error)
    return error?.response
  }
}
export const createProjectType = async (body: ICreateProjectType) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/project/create-project-type`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('get checkpoint error', error)
    return error?.response
  }
}
export const deleteProjectType = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/delete-project-type`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    console.error('delete checkpoint error', error)
    return error?.response
  }
}
export const updateProjectType = async (data: IUpdateProjectType) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/update-project-type`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('get checkpoint error', error)
    return error?.response
  }
}
export const restoreProjectTypes = async (data: IRestoreMaterials) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/restore-project-type`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}
export const permDeleteProjectType = async (data: IDeleteCategory) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/perm-delete-project-type`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}

// Materials

export const createMaterial = async (body: ICreateMaterial) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/project/create-material`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}
export const deleteMaterials = async (data: IDeleteMaterials) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/delete-material`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}

export const getTaxJurisdictionApi = async (data: IGetMaterials) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-tax/deleted/${data.deleted}?limit=1000`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get-tax error', error)
    return error?.response
  }
}
export const deleteTax = async (data: IDeleteTax) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/delete-tax`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}

export const createTax = async (data: IUpdateTax) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/project/create-tax`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('update tax error', error)
    return error?.response
  }
}

export const updateTax = async (data: IUpdateTax) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/update-tax`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('update tax error', error)
    return error?.response
  }
}

export const restoreTax = async (data: IDeleteTax) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/restore-tax`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('permanent tax delete error', error)
    return error?.response
  }
}

export const permanentDeleteTax = async (data: IDeleteTax) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/perm-delete-tax`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('restore tax error', error)
    return error?.response
  }
}

export const getMaterialsById = async (data: { materialId: string; deleted: boolean }) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(
      `/project/get-material-by-id/materialId/${data.materialId}/deleted/${data.deleted}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('getMaterialsById error', error)
    return error?.response
  }
}

export const getMaterialsApi = async (data: IGetMaterials) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-material/deleted/${data.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        limit: data?.limit,
        search: data?.search,
        categoryId: data?.categoryId,
        subCategoryId: data?.subCategoryId,
      },
    })
    return response
  } catch (error: any) {
    console.error('getProjectTypes error', error)
    return error?.response
  }
}
export const permDeleteMaterials = async (data: IDeleteMaterials) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/project/perm-delete-material`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}
export const updateMaterials = async (data: IUpdateMaterials) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/update-material`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}
export const restoreMaterials = async (data: IRestoreMaterials) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/restore-material`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}
// export const getProjectsUsingMaterial = async (data: { companyId?: string; materialId: string }) => {
//   try {
//     const token: any = localStorage.getItem('token')
//     const response = await AxiosInstance.get(
//       `/project/material-project-task/company/${data?.companyId}/material/${data?.materialId}`,
//       {
//         headers: {
//           Authorization: `Bearer ${JSON.parse(token)}`,
//         },
//       }
//     )

//     return response
//   } catch (error: any) {
//     console.error('get checkpoint error', error)
//     return error?.response
//   }
// }

// // Crew Positions
// export const createCrewPosition = async (body: ICreateCrewPosition) => {
//   try {
//     const token: any = localStorage.getItem('token')
//     const response = await AxiosInstance.post(`/project/create-crew-position`, body, {
//       headers: {
//         Authorization: `Bearer ${JSON.parse(token)}`,
//       },
//     })
//     return response
//   } catch (error: any) {
//     console.error('get checkpoint error', error)
//     return error?.response
//   }
// }
// export const getCrewPositionsApi = async (data: IGetCrewPosition) => {
//   try {
//     const token: any = localStorage.getItem('token')
//     const response = await AxiosInstance.get(
//       `/project/get-crew-position/deleted/${data.deleted}?limit=1000`,
//       {
//         headers: {
//           Authorization: `Bearer ${JSON.parse(token)}`,
//         },
//       }
//     )
//     return response
//   } catch (error: any) {
//     console.error('getProjectTypes error', error)
//     return error?.response
//   }
// }
// // same props
// export const deleteCrewPosition = async (data: IDeleteMaterials) => {
//   try {
//     const token: any = localStorage.getItem('token')
//     const response = await AxiosInstance.delete(`/project/delete-crew-position`, {
//       headers: {
//         Authorization: `Bearer ${JSON.parse(token)}`,
//       },
//       data,
//     })
//     return response
//   } catch (error: any) {
//     console.error('delete checkpoint error', error)
//     return error?.response
//   }
// }
// export const permDeleteCrewPositions = async (data: IDeleteMaterials) => {
//   try {
//     const token: any = localStorage.getItem('token')
//     const response = await AxiosInstance.delete(`/project/perm-delete-crew-position`, {
//       headers: {
//         Authorization: `Bearer ${JSON.parse(token)}`,
//       },
//       data,
//     })
//     return response
//   } catch (error: any) {
//     console.error('delete checkpoint error', error)
//     return error?.response
//   }
// }
// export const updateCrewPositions = async (data: IUpdateCrewPositions) => {
//   try {
//     const token: any = localStorage.getItem('token')
//     const response = await AxiosInstance.patch(`/project/update-crew-position`, data, {
//       headers: {
//         Authorization: `Bearer ${JSON.parse(token)}`,
//       },
//     })
//     return response
//   } catch (error: any) {
//     console.error('get checkpoint error', error)
//     return error?.response
//   }
// }
// export const restoreCrewPositions = async (data: IRestoreMaterials) => {
//   try {
//     const token: any = localStorage.getItem('token')
//     const response = await AxiosInstance.patch(`/project/restore-crew-position`, data, {
//       headers: {
//         Authorization: `Bearer ${JSON.parse(token)}`,
//       },
//     })
//     return response
//   } catch (error: any) {
//     console.error('get checkpoint error', error)
//     return error?.response
//   }
// }

export const getOppClockIn = async () => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-opp-for-clockin`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response?.data
  } catch (error: any) {
    console.error('getOppClockIn error', error)
    return error?.response
  }
}
export const getOppClockInMobile = async () => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/get-opp-for-clockin-mobile`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response?.data
  } catch (error: any) {
    console.error('getOppClockIn error', error)
    return error?.response
  }
}

export const getSearchProjectReport = async (data: I_ProjectSearch) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/project/project-active-search?search=${data.search}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getCustomReport error', error)
    return error?.response
  }
}
export const getProjectReport = async (data: I_ProjectReportProps) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/report/get-project-report/${data.oppId}/${data.projectId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getCustomReport error', error)
    return error?.response
  }
}

export const getCrewProjectReport = async (data: I_ProjectCrewReportProps) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/report/crew-project-report/${data.oppId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('crew project report error', error)
    return error?.response
  }
}

export const projectUpdateOrder = async (data: Partial<I_UpdateOrder>) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/project/update-order`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.log('project restore failed', error)
  }
}
