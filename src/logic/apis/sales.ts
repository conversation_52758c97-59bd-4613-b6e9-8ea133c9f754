import { AxiosInstance } from '.'
import { notify, simplifyBackendError } from '../../shared/helpers/util'

interface I_CreateStage {
  name: string
  sequence: number
  createdBy: string
  stageGroup?: string
  PMRequired?: boolean
  projectRequired?: boolean
  orderRequired?: boolean
}

interface I_DeleteStage {
  id: string
}

interface I_UpdateStage {
  stageId: string
  name: string
  sequence: number

  createdBy: string
  stageGroup?: string
  PMRequired?: boolean
  projectRequired?: boolean
  orderRequired?: boolean
  defaultCsrId?: string
}

interface I_CommissionAlter {
  oppId: string
  salesPersonId: string
  amount: number
  reason: string
  createdBy: string
  date: string
  id?: string
}

interface I_UpdateStageSequence {
  data: { _id: string; sequence: number }[]
}

interface I_GetStages {
  skip?: string
  limit?: string
}

interface I_GetStageById {
  stageId: string
}
interface I_GetDisplaySteps {
  oppId: string
}
interface I_CreateStep {
  stageId: string
  name: string
  lable?: string | undefined
  sequence: number
  fieldType: string
  dropDownOptions: any[]
  isDisplay: boolean
  parent: string
  isRequire: boolean
  isParentRequired?: boolean // was string
  activityType: string
  createdBy: string
  projectTypeId: string[]
  location: string[]
}

interface I_DeleteStep {
  id: string
}

interface I_RestoreStep {
  id: string
}

interface I_UpdateStep {
  stepId: string

  stageId: string
  name: string
  sequence: number
  fieldType: string
  dropDownOptions: any[]
  isDisplay: boolean
  parent: string
  isRequire: boolean
  isParentRequired?: boolean
  activityType: string
  createdBy: string
  projectTypeId: string[]
  location: string[]
}

interface I_UpdateStepSequence {
  data: { _id: string; sequence: number }[]
}

interface I_GetStep {
  stageId: string
  skip?: string
  limit?: string
}

interface I_GetStepById {
  stageId: string
}

interface I_CreateCheckpoint {
  name: string
  sequence: number
  createdBy: string
  editable: boolean
  stageEditable: string[]
  stageDisplay: string[]
  stageSet: string[]
  stageGroup: string
  requiredStage: string
}

interface I_DeleteCheckpoint {
  id: string
}

interface I_RestoreStage {
  id: string
}
interface I_UpdateCheckpoint {
  name: string
  sequence: number
  createdBy: string
  editable: boolean
  checkpointId: string
  stageEditable: string[]
  stageDisplay: string[]
  isDisplay: boolean
  stageSet: string[]
  stageGroup: string
  requiredStage: string
}

interface I_UpdateCheckpointSequence {
  data: { _id: string; sequence: number }[]
}

interface I_GetCheckpoint {}

interface I_GetCheckpointById {
  checkpointId: string
}

interface I_CreateOpportunity {
  city: string
  contactId: string
  comments: Array<any>
  companyAddress: string

  companyLang: string
  companyLat: string
  createdBy: string
  distance?: number
  duration: number
  firstName: string
  lastName: string
  leadCost: string
  leadSource: string
  leadSourceId: string
  newLeadDate: Date | string
  oppType: string
  referredBy?: string | undefined
  salesPerson: string
  stage: string
  state: string
  zip: string
}

interface I_UpdateOpportunityDate {
  opportunityId: string
  dateLabel: string
  currDate: Date
  date: Date
  updatedBy: string
}

interface I_UpdateOpportunity {
  oppType: string
  contactId: string
  firstName: string
  lastName: string
  street: string
  city: string
  state: string
  zip: string
  leadSource: string
  PO?: string
  num?: string
  // leadCost: string
  referredBy?: string
  distance?: number
  duration?: string | number
  salesPerson: string
  projectManager?: any
  stage: string
  newLeadDate: string
  comments?: Array<any>
  createdBy: string
  opportunityId: string
  editedBy: string
  currDate: string
  workingCrew: any
  leadSourceId: string
  // [key: string]: any
}

interface I_DeleteOpportunity {
  id: string | undefined
}

interface I_PositionMembersById {
  positionId?: string
  departmentId?: string
  startDate?: string
  endDate?: string
  hasOpportunity?: boolean
}

interface I_GetOpportunity {
  deleted: boolean
  salesPerson?: string
  projectManager?: string
  stageGroup?: string
  lost?: boolean
  status?: string
}

interface I_GetOpportunityById {
  opportunityId: string
  //
  deleted: boolean
}

interface I_LostOpportunity {
  id: string

  memberId: string
  reason: string
  date: string
}

interface I_UpdateActivity {
  id: string
  body: string
  currDate: string
  memberId: string
}

interface I_CreateAction {
  memberId: string
  oppId?: string
  type: string
  body: string
  assignTo?: string
  dueDate: any
  currDate: Date
  leadId?: string
  id?: string
}
interface I_CreateForm {
  oppId: string
  builderFormId: string
  name: string
  createdBy: string
  mediaUrl: string
  fields: any[]
  _id: string
}
interface I_UpdateAction {
  memberId: string
  oppId?: string
  type: string
  body: string
  dueDate: any
  currDate: Date
  leadId?: string
  id: string
}

interface I_UpdateChecklist {
  opportunityId?: string
  leadId?: string
  stage: string
  currDate: string
  key: string
  value?: string
  boolean: boolean
  updatedBy: string
}

interface I_UpdateCompleteStage {
  opportunityId: string
  stageCompleted: string
  newStage: string
  currDate: string
  completedBy: string
}

interface I_UpdateOppDate {
  opportunityId: string
  dateLabel: string
  currDate: string
  date: string
  updatedBy: string
}

interface I_UpdateOppStatus {
  opportunityId: string
  status: string
  statusChangedBy: string
}

interface I_UpdateOpportunityStage {
  id: string

  newStage: string
  currDate: string
  memberId: string
}

interface I_CreateComment {
  memberId: string
  oppId?: string
  leadId?: string
  body: string
  currDate: string
  id?: string
}

interface I_UpdateComment {
  id: string

  oppId?: string
  leadId?: string
  memberId: string
  body: string
  currDate: string
}

interface I_DeleteComment {
  id: string

  oppId?: string
  leadId?: string
  memberId: string
}

interface I_UpdateOppCheckpoint {
  id: string

  currDate: string
  checkpointId: string
  memberId: string
}

interface I_DeleteOppCheckpoint {
  id: string

  symbol: string
  memberId: string
  name: string
  date: string
}

interface I_CreateSalesAction {
  action: {
    name: string
    type: string
    _id: string
  }
  memberId?: string
}
interface I_UpdateSalesAction {
  name: string
  type: string
  actionId: string
  memberId: string
}
interface I_DeleteSalesAction {
  actionId: string
}
interface I_SalesAction {}
interface I_SalesActionById {
  salesActionId: string
}
interface I_UpdateStatus {
  opportunityId?: string
  leadId?: string
  status: string
}
interface I_ModifyPieceWork {
  pitches: number
  layers: number
}

interface I_AddChangeOrder {
  num: number
  name: string
  description: string
  materials: number
  labor: number
  addCost: number
  jobCost: number
  tax: number
  total: number
  deleted: boolean
  // changeOrderValue: number
  // changeOrderRRValue: number
  oldJobCost: number
  oldMaterials: number
  modificationId?: string
}

//---------------------------------CRM-------------------------------------------------

export const createStage = async (data: I_CreateStage) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/crm/create-stage`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('createStage error', error)
    return error?.response
  }
}

export const deleteStage = async (data: I_DeleteStage) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/crm/delete-stage`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: data,
    })

    return response
  } catch (error: any) {
    console.error('deleteStage error', error)
    return error?.response
  }
}

export const updateStage = async (data: I_UpdateStage) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/crm/update-stage`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateStage error', error)
    return error?.response
  }
}

export const getStages = async (data: I_GetStages, deleted: boolean, stageGroup?: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/crm/get-stage/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        stageGroup,
        skip: data.skip,
        // limit: data.limit,
        limit: 30,
      },
    })

    return response
  } catch (error: any) {
    console.error('getStages error', error)
    return error?.response
  }
}

export const getStageById = async (data: I_GetStageById, deleted: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/crm/get-stage-by-id/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getStageById error', error)
    return error?.response
  }
}

export const restoreStage = async (body: I_RestoreStage) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/crm/restore-stage`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('restore stage error', error)
    return error?.response
  }
}

export const updateStageSequenceApi = async (data: I_UpdateStageSequence) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/crm/update-stage-sequence`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('updateStage error', error)
    return error?.response
  }
}

//---------------------------------Step-------------------------------------------------

export const createStep = async (data: I_CreateStep) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/crm/create-step`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('createStep error', error)
    return error?.response
  }
}

export const deleteStep = async (data: I_DeleteStep) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/crm/delete-step`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: data,
    })

    return response
  } catch (error: any) {
    console.error('deleteStep error', error)
    return error?.response
  }
}

export const permDeleteStep = async (body: I_DeleteCheckpoint) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/crm/perm-delete-step`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: body,
    })
    return response
  } catch (error: any) {
    console.error('permanent delete checkpoint error', error)
    return error?.response
  }
}

export const restoreStep = async (data: I_RestoreStep) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/crm/restore-step`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('restore step error', error)
    return error?.response
  }
}

export const updateStep = async (data: I_UpdateStep) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/crm/update-step`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('updateStep error', error)
    return error?.response
  }
}

export const getStep = async (data: I_GetStep, deleted: boolean, projectTypeId?: string, location?: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`crm/get-step/stage/${data.stageId}/deleted/${deleted}?limit=1000`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        skip: data.skip,
        limit: data.limit,
        projectTypeId: projectTypeId,
        location: location?.trim(),
      },
    })

    return response
  } catch (error: any) {
    console.error('getStep error', error)
    return error?.response
  }
}

export const getStepById = async (data: I_GetStepById, deleted: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/crm/get-step-by-id/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('getStepById error', error)
    return error?.response
  }
}

export const updateStepSequenceApi = async (data: I_UpdateStepSequence) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/crm/update-step-sequence`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    console.error('updateStep error', error)
    return error?.response
  }
}

//---------------------------------CHECKPOINTS-------------------------------------------------

export const createCheckpoint = async (body: I_CreateCheckpoint) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/crm/create-checkpoint`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('get checkpoint error', error)
    return error?.response
  }
}

export const deleteCheckpoint = async (body: I_DeleteCheckpoint) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/crm/delete-checkpoint`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: body,
    })
    return response
  } catch (error: any) {
    console.error('delete checkpoint error', error)
    return error?.response
  }
}

export const restoreCheckpoint = async (body: I_DeleteCheckpoint) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/crm/restore-checkpoint`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('restore checkpoint error', error)
    return error?.response
  }
}

export const permDeleteCheckpoint = async (body: I_DeleteCheckpoint) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/crm/perm-delete-checkpoint`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: body,
    })
    return response
  } catch (error: any) {
    console.error('permanent delete checkpoint error', error)
    return error?.response
  }
}

export const updateCheckpoint = async (body: I_UpdateCheckpoint) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/crm/update-checkpoint`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getStepById error', error)
    return error?.response
  }
}

export const getCheckpoint = async (deleted: boolean, groupStage?: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/crm/get-checkpoint/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        stagegroup: groupStage,
      },
    })
    return response
  } catch (error: any) {
    console.error('getStepById error', error)
    return error?.response
  }
}

export const getCheckpointById = async (body: I_GetCheckpointById, deleted: boolean) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(
      `/crm/get-checkpoint-by-id/checkpoint/${body.checkpointId}/deleted/${deleted}`,
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('getStepById error', error)
    return error?.response
  }
}

export const updateCheckpointSequenceApi = async (body: I_UpdateCheckpointSequence) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/crm/update-checkpoint-sequence`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getStepById error', error)
    return error?.response
  }
}

export const updateOppCheckpoint = async (data: I_UpdateOppCheckpoint) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/crm/update-opportunity-checkpoint`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('update oppCheckpoint error', error)
    return error?.response
  }
}

export const getSalesActionDefault = async () => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/company/sales-action-default`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getSalesActionDefault error', error)
    return error?.response
  }
}

export const createCompanySalesAction = async (body: I_CreateSalesAction) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/company/sales-action`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('createCompanySalesAction error', error)
    return error?.response
  }
}

export const updateCompanySalesActionApi = async (data: I_UpdateSalesAction) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/company/sales-action`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('updateCompanySalesActionApi error', error)
    return error?.response
  }
}

export const deleteCompanySalesAction = async (data: I_DeleteSalesAction) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/company/sales-action`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('deleteCompanySalesAction error', error)
    return error?.response
  }
}

export const createSalesAction = async (body: I_CreateSalesAction) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/crm/sales-action`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('createSalesAction error', error)
    return error?.response
  }
}

export const updateSalesActionApi = async (data: I_UpdateSalesAction) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/crm/sales-action`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('updateSalesActionApi error', error)
    return error?.response
  }
}

export const deleteSalesAction = async (data: I_DeleteSalesAction) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/crm/sales-action`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    console.error('deleteSalesAction error', error)
    return error?.response
  }
}

export const getSalesActions = async (deleted: boolean) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/crm/sales-actions-all-members/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getStepById error', error)
    return error?.response
  }
}

export const getSalesActionByMemberId = async (memberId: string, deleted: boolean) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/crm/sales-action-by-memberId`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        memberId: memberId,
        deleted: deleted,
      },
    })
    return response
  } catch (error: any) {
    console.error('getStepById error', error)
    return error?.response
  }
}

export const getStageOppsData = async (stageId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/crm/stage-opportunity-list/${stageId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getStageOppsData error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const getAllStages = async () => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/crm/stages`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getAllStages error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const migrateStageOpportunity = async (oldStageId: string, newStageId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/crm/migrate-stage-opportunity/${oldStageId}/${newStageId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('migrateStageOpportunity error', error)
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

//---------------------------------Lead-------------------------------------------------

export const createLead = async (body: any) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/lead/create-lead/`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const updateLead = async (data: I_UpdateOpportunity, id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/update-lead/${id}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const getLeads = async (body: any) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/lead/fetch-all-lead/deleted/${body.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        lost: body.lost,
        status: body.status,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const getLeadById = async (id: string, deleted?: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/lead/fetch-lead/${id}/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error
  }
}
export const restoreLead = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/lead/restore-lead/${id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error
  }
}

export const createLeadComment = async (body: I_CreateComment) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/lead/create-lead-comment`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const updateLeadComment = async (data: I_UpdateComment) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/update-lead-comment`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const getLeadStepChecklist = async (leadId: string, stageId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/lead/lead-step-checklist/leadId/${leadId}/stageId/${stageId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const updateLeadChecklist = async (data: I_UpdateChecklist) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/update-checklist`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const createLeadAction = async (body: I_CreateAction) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/lead/create-new-action`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const completeLeadAction = async (data: I_UpdateAction) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/update-action`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const lostLead = async (data: I_LostOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/lost-lead`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const unlostLead = async (data: I_LostOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/un-lost-lead`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const updateLeadStatus = async (data: I_UpdateStatus) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/update-lead-status`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const deleteLead = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/lead/delete-lead/${id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const updateLeadCheckpoint = async (data: I_UpdateOppCheckpoint) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/update-lead-checkpoint`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('update oppCheckpoint error', error)
    return error?.response
  }
}
export const deleteLeadCheckpoint = async (body: I_DeleteOppCheckpoint) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/delete-lead-checkpoint`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const updateLeadStage = async (data: I_UpdateOpportunityStage) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/lead/update-lead-stage`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const getLeadsCompletion = async (body: I_GetOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    // let paramsObj = {} as { salesPerson: string; projectManager: string }
    // if (body?.salesPerson) {
    //   paramsObj['salesPerson'] = body?.salesPerson
    // }

    // if (body?.projectManager) {
    //   paramsObj['projectManager'] = body?.projectManager
    // }
    const response = await AxiosInstance.get(`/lead/lead-completion-percent/deleted/${body.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        stageGroup: body.stageGroup,
        lost: body.lost,
        status: body.status,
        // ...paramsObj,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

//---------------------------------OPPORTUNITY-------------------------------------------------

export const createOpportunity = async (body: I_CreateOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/opportunity`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('create opp error', error)
    return error?.response
  }
}

export const updateOpportunity = async (data: I_UpdateOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateOpportunity error', error)
    return error?.response
  }
}

export const restoreOpportunity = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity/restore/${id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('restoreOpportunity error', error)
    return error?.response
  }
}

export const convertToOpportunity = async (body: I_CreateOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/opportunity/convert-to-opportunity`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const createWarranty = async (body: I_CreateOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/opportunity/warranty`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('create warranty error', error)
    return error?.response
  }
}

export const getPONum = async (po: string, warrantyType: boolean) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/opportunity/po-num/${po}/${warrantyType}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('get po num error', error)
    return error?.response
  }
}

export const getOperationsOpportunity = async (body: I_GetOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')

    let paramsObj = {} as { salesPerson: string; projectManager: string }
    if (body?.salesPerson) {
      paramsObj['salesPerson'] = body?.salesPerson
    }

    if (body?.projectManager) {
      paramsObj['projectManager'] = body?.projectManager
    }

    const response = await AxiosInstance.get(`/opportunity/operations/deleted/${body.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        stageGroup: body.stageGroup,
        // lost: body.lost,
        status: body.status,
        ...paramsObj,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getOperationsOpportunity error', error)
    return error?.response
  }
}

export const getSalesOpportunity = async (body: I_GetOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')

    let paramsObj = {} as { salesPerson: string; projectManager: string }
    if (body?.salesPerson) {
      paramsObj['salesPerson'] = body?.salesPerson
    }

    // if (body?.projectManager) {
    //   paramsObj['projectManager'] = body?.projectManager
    // }

    const response = await AxiosInstance.get(`/opportunity/sales/deleted/${body.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        stageGroup: body.stageGroup,
        // lost: body.lost,
        status: body.status,
        ...paramsObj,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getSalesOpportunity error', error)
    return error?.response
  }
}

export const getOpportunityById = async (data: I_GetOpportunityById) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/opportunity/id/${data.opportunityId}/deleted/${data.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getOpportunityById error', error)
    return error?.response
  }
}

export const updateChecklist = async (data: I_UpdateChecklist) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity/step-checklist`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateChecklist error', error)
    return error?.response
  }
}

export const updateCompleteStage = async (data: I_UpdateCompleteStage) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity/complete-stage`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateCompleteStage error', error)
    return error?.response
  }
}

export const lostOpportunity = async (data: I_LostOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity/lost`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('lostOpportunity error', error)
    return error?.response
  }
}

export const unlostOpportunity = async (data: I_LostOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity/un-lost`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('unlostOpportunity error', error)
    return error?.response
  }
}

// export const updateOppStatus = async (data: I_UpdateOppStatus) => {
//   try {
//     const token: any = localStorage.getItem('token')
//     const response = await AxiosInstance.patch(`/opportunity/status`, data, {
//       headers: {
//         Authorization: `Bearer ${JSON.parse(token)}`,
//       },
//     })
//     return response
//   } catch (error: any) {
//     notify(simplifyBackendError(error?.response?.data?.message), 'error')
//     console.error('updateOppStatus error', error)
//     return error?.response
//   }
// }

export const updateOppStatusApi = async (data: I_UpdateStatus) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity/status`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateOppStatusApi error', error)
    return error?.response
  }
}

export const deleteOpportunity = async (data: I_DeleteOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/opportunity/soft`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('deleteOpportunity error', error)
    return error?.response
  }
}

export const permDeleteOpportunity = async (data: I_DeleteOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/opportunity/permanently`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('permDeleteOpportunity error', error)
    return error?.response
  }
}

export const updateActivity = async (data: I_UpdateActivity) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity/activity`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateActivity error', error)
    return error?.response
  }
}

export const getOpportunityActivity = async (oppId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`opportunity/activity/oppId/${oppId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.log('failed to fetch activity', error)
  }
}

export const updateOpportunityStage = async (data: I_UpdateOpportunityStage) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity/stage`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateOpportunityStage error', error)
    return error?.response
  }
}

export const createAction = async (body: I_CreateAction) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/opportunity/action`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('createAction error', error)
    return error?.response
  }
}

export const completeAction = async (data: I_UpdateAction) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity/action`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('completeAction error', error)
    return error?.response
  }
}

export const createComment = async (body: I_CreateComment) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/opportunity/comment`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('createComment error', error)
    return error?.response
  }
}

export const updateCommentApi = async (data: I_UpdateComment) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity/comment`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateCommentApi error', error)
    return error?.response
  }
}

export const deleteCommentApi = async (data: I_DeleteComment) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/opportunity/comment`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data,
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('delete checkpoint error', error)
    return error?.response
  }
}

export const getDisplaySteps = async (data: I_GetDisplaySteps) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/opportunity/display-steps/opp/${data.oppId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getSteps error', error)
    return error?.response
  }
}

export const getOpportunityModifiedCommission = async (oppId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`opportunity/commission/oppId/${oppId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.log('failed to fetch getOpportunityModifiedCommission', error)
  }
}

export const updateOpportunityCommission = async (data: I_CommissionAlter) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/opportunity/commission`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateOpportunityCommission error', error)
    return error?.response
  }
}

export const createOpportunityCommission = async (data: I_CommissionAlter) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = AxiosInstance.post(`/opportunity/commission`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('createOpportunityCommission error', error)
    return error?.response
  }
}

export const updateOpportunityCheckpoint = async (data: I_UpdateOppCheckpoint) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/opportunity/checkpoint`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('update opportunity checkpoint error', error)
    return error?.response
  }
}

export const deleteOppCheckpoint = async (body: I_DeleteOppCheckpoint) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.delete(`/opportunity/checkpoint`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: body,
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
  }
}

export const ModifyPieceWork = async (data: I_ModifyPieceWork, oppId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/opportunity/piece-work/opp/${oppId}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('ModifyPieceWork error', error)
    return error?.response
  }
}

export const addChangeOrder = async (data: I_AddChangeOrder, oppId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/opportunity/change-order/opp/${oppId}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('add change order error', error)
    return error?.response
  }
}

export const updateChangeOrder = async (data: I_AddChangeOrder, oppId: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/opportunity/change-order/opp/${oppId}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('update change order error', error)
    return error?.response
  }
}

export const getOldOpportunities = async (filterObj?: { type?: string; limit?: number; salesPerson?: string }) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/opportunity/old`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        typeId: filterObj?.type,
        limit: filterObj?.limit,

        salesPerson: filterObj?.salesPerson,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getOldOpportunities error', error)
    return error?.response
  }
}

export const getSalesOpportunityCompletion = async (body: I_GetOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    let paramsObj = {} as { salesPerson: string; projectManager: string }
    if (body?.salesPerson) {
      paramsObj['salesPerson'] = body?.salesPerson
    }

    if (body?.projectManager) {
      paramsObj['projectManager'] = body?.projectManager
    }
    const response = await AxiosInstance.get(`/opportunity/sales-completion-percent/deleted/${body.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        stageGroup: body.stageGroup,
        // lost: body.lost,
        status: body.status,
        ...paramsObj,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getSalesOpportunityCompletion error', error)
    return error?.response
  }
}

export const getOperationsOpportunityCompletion = async (body: I_GetOpportunity) => {
  try {
    const token: any = localStorage.getItem('token')
    let paramsObj = {} as { salesPerson: string; projectManager: string }
    if (body?.salesPerson) {
      paramsObj['salesPerson'] = body?.salesPerson
    }

    if (body?.projectManager) {
      paramsObj['projectManager'] = body?.projectManager
    }
    const response = await AxiosInstance.get(`/opportunity/operations-completion-percent/deleted/${body.deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        stageGroup: body.stageGroup,
        lost: body.lost,
        status: body.status,
        ...paramsObj,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getOperationsOpportunityCompletion error', error)
    return error?.response
  }
}

export const updateJobNotes = async (oppId: string, note: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(
      `/opportunity/job-note/oppId/${oppId}/note/${note}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )
    return response
  } catch (error: any) {
    console.error('Note update error', error)
    return error?.response
  }
}

export const getStepChecklist = async (stageId: string, oppId: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/opportunity/step-checklist/oppId/${oppId}/stageId/${stageId}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('get step checklist error', error)
    return error?.response
  }
}

export const searchOpps = async (options: { search?: string; salesPerson?: string; projectManager?: string }) => {
  try {
    const token: any = localStorage.getItem('token')

    let paramsObj = {} as { salesPerson: string; projectManager: string }
    if (options?.salesPerson) {
      paramsObj['salesPerson'] = options?.salesPerson
    }

    if (options?.projectManager) {
      paramsObj['projectManager'] = options?.projectManager
    }

    const response = await AxiosInstance.get(`/opportunity/search`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        search: options?.search,
        ...paramsObj,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('get step checklist error', error)
    return error?.response
  }
}

export const getReferresCount = async () => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/opportunity/referrer-count`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    console.error('getReferresCount error', error)
    return error?.response
  }
}

export const updateOppDate = async (data: I_UpdateOppDate) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity/update-opportunity-date`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateOppDate error', error)
    return error?.response
  }
}

export interface ISalesCommission {
  opportunityId: string

  totalCommission?: number
  saleCommission?: number
  startCommission?: number
  completedCommission?: number
  modificationAmount?: number
  date?: string
  reason?: string
}

// export const updateSalesCommission = async (data: ISalesCommission) => {
//   try {
//     const token: any = localStorage.getItem('token')
//     const response = await AxiosInstance.patch(`/opportunity/update-opportunity-commission`, data, {
//       headers: {
//         Authorization: `Bearer ${JSON.parse(token)}`,
//       },
//     })
//     return response
//   } catch (error: any) {
//     notify(simplifyBackendError(error?.response?.data?.message), 'error')
//     console.error('get checkpoint error', error)
//     return error?.response
//   }
// }

// export const updateAction = async (body: I_CreateAction) => {
//   try {
//     const token: any = localStorage.getItem('token')
//     const response = await AxiosInstance.post(`/opportunity/update-action`, body, {
//       headers: {
//         Authorization: `Bearer ${JSON.parse(token)}`,
//       },
//     })
//     return response
//   } catch (error: any) {
//     console.error('update action error', error)
//     return error?.response
//   }
// }

export const getPositionMembersById = async (data: I_PositionMembersById, deleted?: boolean) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/company/members`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        positionId: data.positionId,
        departmentId: data.departmentId,
        startDate: data.startDate,
        endDate: data.endDate,
        hasOpportunity: data.hasOpportunity,
        deleted,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    return error?.response
  }
}
export const getActionMembers = async (data: I_PositionMembersById, deleted?: boolean) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`/company/members-with-actions`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        positionId: data?.positionId,
        departmentId: data?.departmentId,
        startDate: data?.startDate,
        endDate: data?.endDate,
        hasOpportunity: data?.hasOpportunity,
        deleted,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    return error?.response
  }
}

export const createOpportunityForm = async (body: I_CreateForm) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.post(`/opportunity/form`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const updateOpportunityForm = async (body: I_CreateForm, _id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.patch(`/opportunity/form/id/${_id}`, body, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    return error?.response
  }
}

export const deleteOpportunityForm = async (_id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/opportunity/form/id/${_id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    return error?.response
  }
}

export const opportunityFormById = async (_id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/opportunity/form/id/${_id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log({ error })
  }
}

export const opportunityForm = async (deleted: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/opportunity/form/all/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error) {
    console.log({ error })
  }
}
